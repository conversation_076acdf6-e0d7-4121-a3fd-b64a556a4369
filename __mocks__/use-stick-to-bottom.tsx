import React from 'react'

type StickProps = React.PropsWithChildren<Record<string, unknown>>

type ContextValue = {
  isAtBottom: boolean
  scrollToBottom: () => void
}

export const StickToBottom = ({ children, ...props }: StickProps) => (
  <div {...props}>{children}</div>
)

StickToBottom.Content = ({ children, ...props }: StickProps) => (
  <div {...props}>{children}</div>
)

export const useStickToBottomContext = (): ContextValue => ({
  isAtBottom: true,
  scrollToBottom: () => {},
})
