{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    // Enable unused variable checks for early detection
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./*"]
    }
  },
  "include": [
    "next-env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    ".next/types/**/*.ts",
    ".next-dev/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    // Exclude legacy code from TypeScript unused variable checks
    "**/*legacy*/**",
    "**/(legacy)/**",
    "**/legacy/**",
    // Exclude stale generated types for removed routes
    ".next/types/app/api/aipane/chat-v2/**",
    ".next-dev/types/app/api/aipane/chat-v2/**"
  ]
}
