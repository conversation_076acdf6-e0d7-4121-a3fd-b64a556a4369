import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { renderHook, act } from '@testing-library/react'
import { useWorkspaceLayoutStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import {
  assetTypeRegistry,
  generateAssetType,
  chatAssetType,
} from '@/lib/asset-types'
import type { Asset } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { TabPrefixUtils } from '@/app/(conv)/dragTree/[dragTreeId]/constants/tab-prefixes'
import * as dragTreeStoreModule from '@/app/stores/dragtree_store/store'

const mockNodeContentMap = new Map<
  string,
  Map<
    string,
    {
      contentId: string
      contentText?: string
    }
  >
>()

const dragTreeGetStateSpy = jest.spyOn(
  dragTreeStoreModule.useDragTreeStore,
  'getState'
)

jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: {
      user: {
        subscription: {
          tier: 'FREE',
        },
      },
    },
  })),
}))

jest.mock('next-auth', () => ({}))

jest.mock('@/auth', () => ({
  auth: jest.fn(async () => ({ user: { id: 'test-user' } })),
}))

jest.mock('@/app/server-actions/drag-tree/get_ai_generations', () => ({
  getAIGenerations: jest.fn(async () => []),
}))

afterAll(() => {
  dragTreeGetStateSpy.mockRestore()
})

describe('Tab Validation and Cleanup', () => {
  beforeEach(() => {
    mockNodeContentMap.clear()

    dragTreeGetStateSpy.mockReturnValue({
      nodeContent: mockNodeContentMap,
      findNodeById: jest.fn(),
      getNodePath: jest.fn(),
    } as any)

    const store = useWorkspaceLayoutStore.getState()
    act(() => {
      store.resetTabs()
      store.setIsOpen(false)
      store.setMode('compress')
      store.setWidth(400)
      store.setContextTokenCount(0)
      store.resetSettings()
      store.clearFilter()
      useWorkspaceLayoutStore.setState({
        loadingContextIds: new Set<string>(),
        isLoading: false,
      })
    })

    // Clear registry and register built-in types
    assetTypeRegistry.clear()
    assetTypeRegistry.register(generateAssetType)
    assetTypeRegistry.register(chatAssetType)
  })

  describe('validateAndCleanupTabs', () => {
    it('should keep core tabs (main, research)', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      // Set up tabs with core tabs
      act(() => {
        result.current.tabs = [
          {
            id: 'main',
            title: 'Main',
            fullTitle: 'Main',
            type: 'main',
            isClosable: false,
          },
          {
            id: 'research-1',
            title: 'Research',
            fullTitle: 'Research',
            type: 'research',
            isClosable: true,
          },
        ]
      })

      // Validate with empty assets
      act(() => {
        result.current.validateAndCleanupTabs([])
      })

      // Core tabs should remain
      expect(result.current.tabs).toHaveLength(2)
      expect(result.current.tabs.map(t => t.type)).toEqual(['main', 'research'])
    })

    it('should keep asset tabs with valid assets', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const validAsset: Asset = {
        id: 'asset-123',
        title: 'Valid Asset',
        content: 'This is valid content',
        type: 'generate',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        viewed: true,
        dragTreeId: 'tree-123',
      }

      const validTab: Tab = {
        id: 'asset-generate-asset-123',
        title: 'Generate - Valid Asset',
        fullTitle: 'Generate - Valid Asset',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          assetId: 'asset-123',
        },
      }

      act(() => {
        result.current.tabs = [validTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([validAsset])
      })

      // Valid tab should remain
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].id).toBe('asset-generate-asset-123')
    })

    it('should remove asset tabs with missing assets', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const invalidTab: Tab = {
        id: 'asset-generate-missing-123',
        title: 'Generate - Missing Asset',
        fullTitle: 'Generate - Missing Asset',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          assetId: 'missing-asset-123',
        },
      }

      act(() => {
        result.current.tabs = [invalidTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([]) // No assets available
      })

      // Invalid tab should be removed, main tab should be added
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].type).toBe('main')
    })

    it('should remove asset tabs with empty content and not persisted in DB', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const emptyAsset: Asset = {
        id: 'asset-456',
        title: 'Empty Asset',
        content: '', // Empty content
        type: 'generate',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        viewed: true,
        dragTreeId: 'tree-123',
        persistedInDb: false, // Not persisted in DB
      }

      const invalidTab: Tab = {
        id: 'asset-generate-asset-456',
        title: 'Generate - Empty Asset',
        fullTitle: 'Generate - Empty Asset',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          assetId: 'asset-456',
        },
      }

      act(() => {
        result.current.tabs = [invalidTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([emptyAsset])
      })

      // Tab with empty asset that's not persisted should be removed
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].type).toBe('main')
    })

    it('should keep asset tabs with empty content but persisted in DB', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const persistedAsset: Asset = {
        id: 'asset-789',
        title: 'Persisted Asset',
        content: '', // Empty content but persisted in DB
        type: 'generate',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        viewed: true,
        dragTreeId: 'tree-123',
        persistedInDb: true, // Persisted in DB - content will be loaded lazily
      }

      const validTab: Tab = {
        id: 'asset-generate-asset-789',
        title: 'Generate - Persisted Asset',
        fullTitle: 'Generate - Persisted Asset',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          assetId: 'asset-789',
        },
      }

      act(() => {
        result.current.tabs = [validTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([persistedAsset])
      })

      // Tab with persisted asset should be kept even if content is empty
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].id).toBe('asset-generate-asset-789')
    })

    it('should remove asset tabs without assetId', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const brokenTab: Tab = {
        id: 'asset-generate-broken',
        title: 'Generate - Broken',
        fullTitle: 'Generate - Broken',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          // Missing assetId
        },
      }

      act(() => {
        result.current.tabs = [brokenTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([])
      })

      // Broken tab should be removed
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].type).toBe('main')
    })

    it('should preserve active tab if valid', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const validAsset: Asset = {
        id: 'asset-789',
        title: 'Valid Asset',
        content: 'Valid content',
        type: 'chat',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        viewed: true,
        dragTreeId: 'tree-123',
      }

      const validTab: Tab = {
        id: 'asset-chat-asset-789',
        title: 'Chat - Valid Asset',
        fullTitle: 'Chat - Valid Asset',
        type: 'chat',
        isClosable: true,
        aiPaneData: {
          type: 'chat',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          assetId: 'asset-789',
        },
      }

      act(() => {
        result.current.tabs = [validTab]
        result.current.activeTabId = 'asset-chat-asset-789'
      })

      act(() => {
        result.current.validateAndCleanupTabs([validAsset])
      })

      // Active tab should remain active
      expect(result.current.activeTabId).toBe('asset-chat-asset-789')
    })

    it('should switch active tab if current active tab is removed', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const mainTab: Tab = {
        id: 'main',
        title: 'Main',
        fullTitle: 'Main',
        type: 'main',
        isClosable: false,
      }

      const invalidTab: Tab = {
        id: 'asset-generate-invalid',
        title: 'Generate - Invalid',
        fullTitle: 'Generate - Invalid',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          assetId: 'invalid-asset',
        },
      }

      act(() => {
        result.current.tabs = [mainTab, invalidTab]
        result.current.activeTabId = 'asset-generate-invalid'
      })

      act(() => {
        result.current.validateAndCleanupTabs([]) // No assets
      })

      // Should switch to main tab
      expect(result.current.activeTabId).toBe('main')
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].type).toBe('main')
    })

    it('should keep newly created temp tabs without assetId (fresh tabs)', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const freshTempTab: Tab = {
        id: 'temp-generate-1234567890',
        title: '20241204_143022',
        fullTitle: 'Generate - 20241204_143022',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Create a summary of the research findings',
          contextIds: ['context1', 'context2'],
          settings: {},
          // No assetId yet - this is a fresh temp tab waiting for generation
        },
      }

      act(() => {
        result.current.tabs = [freshTempTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([]) // No assets available yet
      })

      // Fresh temp tab should be kept even without assetId
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].id).toBe('temp-generate-1234567890')
    })

    it('should remove stale temp tabs with assetId (completed but not transformed)', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const staleTempTab: Tab = {
        id: 'temp-generate-1234567890',
        title: 'Completed Generation',
        fullTitle: 'Generate - Completed Generation',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Original prompt',
          contextIds: [],
          settings: {},
          assetId: 'uuid-from-database', // Has assetId = generation completed but tab not transformed
        },
      }

      act(() => {
        result.current.tabs = [staleTempTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([]) // Validation after browser reload
      })

      // Stale temp tab should be removed (user can access via asset sidebar)
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].type).toBe('main')
    })

    it('should remove broken asset tabs without assetId that are not new AI tabs', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const brokenTab: Tab = {
        id: 'some-broken-tab',
        title: 'Broken Tab',
        fullTitle: 'Broken Tab',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: '', // Empty prompt - not a new AI tab
          contextIds: [],
          settings: {},
          // No assetId and not a new AI tab pattern
        },
      }

      act(() => {
        result.current.tabs = [brokenTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([])
      })

      // Broken tab should be removed
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].type).toBe('main')
    })

    it('should transform temp tab ID to permanent asset ID when assetId is assigned', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const tempTab: Tab = {
        id: 'temp-generate-1234567890',
        title: 'Fresh Generation',
        fullTitle: 'Generate - Fresh Generation',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Original prompt',
          contextIds: [],
          settings: {},
        },
      }

      act(() => {
        result.current.tabs = [tempTab]
        result.current.activeTabId = 'temp-generate-1234567890'
      })

      // Simulate generation completion by updating with assetId
      act(() => {
        result.current.updateTabAiPaneData('temp-generate-1234567890', {
          assetId: 'uuid-from-database',
        })
      })

      // Tab ID should be transformed to permanent asset pattern
      expect(result.current.tabs).toHaveLength(1)
      expect(result.current.tabs[0].id).toBe(
        'asset-generate-uuid-from-database'
      )
      expect(result.current.tabs[0].aiPaneData?.assetId).toBe(
        'uuid-from-database'
      )
      expect(result.current.activeTabId).toBe(
        'asset-generate-uuid-from-database'
      )
    })

    it('should handle mixed valid and invalid tabs', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const validAsset: Asset = {
        id: 'asset-valid',
        title: 'Valid Asset',
        content: 'Valid content',
        type: 'generate',
        model: 'gpt-4',
        prompt: 'Test prompt',
        contextIds: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        viewed: true,
        dragTreeId: 'tree-123',
      }

      const mainTab: Tab = {
        id: 'main',
        title: 'Main',
        fullTitle: 'Main',
        type: 'main',
        isClosable: false,
      }

      const validTab: Tab = {
        id: 'asset-generate-valid',
        title: 'Generate - Valid',
        fullTitle: 'Generate - Valid',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          assetId: 'asset-valid',
        },
      }

      const invalidTab: Tab = {
        id: 'asset-generate-invalid',
        title: 'Generate - Invalid',
        fullTitle: 'Generate - Invalid',
        type: 'generate',
        isClosable: true,
        aiPaneData: {
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Test prompt',
          contextIds: [],
          settings: {},
          assetId: 'asset-invalid',
        },
      }

      act(() => {
        result.current.tabs = [mainTab, validTab, invalidTab]
      })

      act(() => {
        result.current.validateAndCleanupTabs([validAsset])
      })

      // Should keep main and valid tab, remove invalid tab
      expect(result.current.tabs).toHaveLength(2)
      expect(result.current.tabs.map(t => t.id)).toEqual([
        'main',
        'asset-generate-valid',
      ])
    })
  })

  describe('tab creation deduplication', () => {
    it('should reuse existing tab for same nodeId/contentId', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      const existingTab: Tab = {
        id: 'research-tab',
        title: 'Research Tab',
        fullTitle: 'Research Tab',
        type: 'research',
        nodeId: 'node-1',
        contentId: 'content-1',
        isClosable: true,
      }

      act(() => {
        result.current.tabs = [existingTab]
        result.current.activeTabId = 'main'
      })

      let returnedId: string | undefined
      act(() => {
        returnedId = result.current.addTab({
          title: 'Duplicate',
          fullTitle: 'Duplicate',
          type: 'research',
          nodeId: 'node-1',
          contentId: 'content-1',
          isClosable: true,
        })
      })

      expect(result.current.tabs).toHaveLength(1)
      expect(returnedId).toBe('research-tab')
      expect(result.current.activeTabId).toBe('research-tab')
    })

    it('should create new tab for different identity', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      act(() => {
        result.current.tabs = [
          {
            id: 'research-tab-1',
            title: 'Research Tab',
            fullTitle: 'Research Tab',
            type: 'research',
            nodeId: 'node-1',
            contentId: 'content-1',
            isClosable: true,
          },
        ]
        result.current.activeTabId = 'research-tab-1'
      })

      let newTabId: string | undefined
      act(() => {
        newTabId = result.current.addTab({
          title: 'New Research',
          fullTitle: 'New Research',
          type: 'research',
          nodeId: 'node-1',
          contentId: 'content-2',
          isClosable: true,
        })
      })

      expect(result.current.tabs).toHaveLength(2)
      expect(newTabId).toBeDefined()
      expect(newTabId).not.toBe('research-tab-1')
      expect(result.current.activeTabId).toBe(newTabId)
    })
  })

  describe('addTab deduplication', () => {
    it('reuses research tab for identical node and content', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      let firstTabId: string | undefined
      act(() => {
        firstTabId = result.current.addTab({
          title: 'Research Node',
          fullTitle: 'Research Node > Detail',
          type: 'research',
          nodeId: 'node-1',
          contentId: 'content-1',
          isClosable: true,
        })
      })

      let secondTabId: string | undefined
      act(() => {
        secondTabId = result.current.addTab({
          title: 'Research Node Duplicate',
          fullTitle: 'Research Node > Detail',
          type: 'research',
          nodeId: 'node-1',
          contentId: 'content-1',
          isClosable: true,
        })
      })

      expect(secondTabId).toBe(firstTabId)
      const researchTabs = result.current.tabs.filter(
        tab => tab.nodeId === 'node-1'
      )
      expect(researchTabs).toHaveLength(1)
      expect(result.current.activeTabId).toBe(firstTabId)
    })

    it('reuses asset tabs by assetId and applies asset prefix', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      let firstTabId: string | undefined
      act(() => {
        firstTabId = result.current.addTab({
          title: 'Generate Asset',
          fullTitle: 'Generate Asset',
          type: 'generate',
          isClosable: true,
          aiPaneData: {
            type: 'generate',
            model: 'gpt-4',
            prompt: 'Test prompt',
            contextIds: [],
            settings: {},
            assetId: 'asset-1',
          },
        })
      })

      expect(firstTabId).toBe(
        TabPrefixUtils.createAssetTabId('generate', 'asset-1')
      )

      let secondTabId: string | undefined
      act(() => {
        secondTabId = result.current.addTab({
          title: 'Generate Asset Duplicate',
          fullTitle: 'Generate Asset Duplicate',
          type: 'generate',
          isClosable: true,
          aiPaneData: {
            type: 'generate',
            model: 'gpt-4',
            prompt: 'Another prompt',
            contextIds: [],
            settings: {},
            assetId: 'asset-1',
          },
        })
      })

      expect(secondTabId).toBe(firstTabId)
      const assetTabs = result.current.tabs.filter(
        tab => tab.aiPaneData?.assetId === 'asset-1'
      )
      expect(assetTabs).toHaveLength(1)
      expect(result.current.activeTabId).toBe(firstTabId)
    })
  })

  describe('closeAiPane behaviour', () => {
    it('switches back to main tab by default when closing an AI tab', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      let aiTabId: string | undefined
      act(() => {
        aiTabId = result.current.addAiTab({
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Generate summary',
          contextIds: [],
          settings: {},
        })
        result.current.setIsOpen(true)
      })

      expect(aiTabId).toBeDefined()
      expect(result.current.activeTabId).toBe(aiTabId)

      act(() => {
        result.current.closeAiPane()
      })

      expect(result.current.activeTabId).toBe('main')
      expect(result.current.isOpen).toBe(false)
    })

    it('should keep AI tab active when not switching to main tab', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      let aiTabId: string | undefined
      act(() => {
        aiTabId = result.current.addAiTab({
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Generate summary',
          contextIds: [],
          settings: {},
        })
        result.current.setIsOpen(true)
      })

      expect(aiTabId).toBeDefined()

      act(() => {
        result.current.closeAiPane(false)
      })

      expect(result.current.activeTabId).toBe(aiTabId)
    })

    it('should switch to main tab when requested', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      act(() => {
        result.current.addAiTab({
          type: 'generate',
          model: 'gpt-4',
          prompt: 'Generate summary',
          contextIds: [],
          settings: {},
        })
        result.current.setIsOpen(true)
      })

      act(() => {
        result.current.closeAiPane(true)
      })

      expect(result.current.activeTabId).toBe('main')
    })
  })

  describe('isAllSelectedContentLoaded', () => {
    it('returns false when selected context has not loaded content', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      act(() => {
        result.current.setContext([
          {
            id: 'node-1',
            title: 'Node 1',
            type: 'question',
            selected: true,
          },
        ])
      })

      expect(result.current.isAllSelectedContentLoaded()).toBe(false)
    })

    it('returns true when all selected context content is available', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      act(() => {
        result.current.setContext([
          {
            id: 'node-1',
            title: 'Node 1',
            type: 'question',
            selected: true,
          },
        ])
      })

      act(() => {
        mockNodeContentMap.set(
          'node-1',
          new Map([
            [
              'content-1',
              {
                contentId: 'content-1',
                contentText: 'Loaded content',
              },
            ],
          ])
        )
        result.current.setContextLoading('node-1', false)
      })

      expect(result.current.settings.context[0]?.selected).toBe(true)
      expect(mockNodeContentMap.get('node-1')?.size).toBe(1)
      expect(result.current.loadingContextIds.has('node-1')).toBe(false)

      const { useDragTreeStore } = require('@/app/stores/dragtree_store/store')
      expect(useDragTreeStore.getState().nodeContent).toBe(mockNodeContentMap)

      const storeSnapshot = useWorkspaceLayoutStore.getState()
      const selectedIds = storeSnapshot.settings.context
        .filter(item => item.selected)
        .map(item => item.id)
      expect(selectedIds).toEqual(['node-1'])
      selectedIds.forEach(id => {
        expect(storeSnapshot.loadingContextIds.has(id)).toBe(false)
        const contentMap = useDragTreeStore.getState().nodeContent.get(id)
        expect(contentMap?.size ?? 0).toBeGreaterThan(0)
      })

      const manualCheck = (() => {
        const state = useWorkspaceLayoutStore.getState()
        const ids = state.settings.context
          .filter(item => item.selected)
          .map(item => item.id)
        return ids.every(id => {
          if (state.loadingContextIds.has(id)) {
            return false
          }
          const nodeContent = useDragTreeStore.getState().nodeContent.get(id)
          return Boolean(nodeContent && nodeContent.size > 0)
        })
      })()

      expect(manualCheck).toBe(true)
      expect(result.current.isAllSelectedContentLoaded()).toBe(manualCheck)
    })

    it('returns false when any selected context item is still loading', () => {
      const { result } = renderHook(() => useWorkspaceLayoutStore())

      act(() => {
        result.current.setContext([
          {
            id: 'node-1',
            title: 'Node 1',
            type: 'question',
            selected: true,
          },
        ])
      })

      act(() => {
        mockNodeContentMap.set(
          'node-1',
          new Map([
            [
              'content-1',
              {
                contentId: 'content-1',
                contentText: 'Loaded content',
              },
            ],
          ])
        )
        result.current.setContextLoading('node-1', true)
      })

      expect(result.current.isAllSelectedContentLoaded()).toBe(false)
    })
  })
})
