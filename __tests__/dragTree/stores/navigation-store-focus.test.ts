import { useNavigationStore } from '@/app/stores/navigation_store'

// Mock the debounce and timeout utilities
jest.mock('@/app/stores/navigation_store', () => {
  const actual = jest.requireActual('@/app/stores/navigation_store')
  return {
    ...actual,
    // Override the store creation to avoid actual timeouts in tests
    useNavigationStore: jest.fn(),
  }
})

describe('Navigation Store Focus Parameter Integration', () => {
  let mockStore: any
  let mockCallback: jest.Mock

  beforeEach(() => {
    mockCallback = jest.fn()

    // Create a mock store with the focus parameter functionality
    mockStore = {
      navigationState: 'idle',
      targetNodeId: null,
      treeTargetNodeId: null,
      preventReactFlowFocus: false,
      isNavigating: false,
      lastNavigationTime: 0,
      lastNavigationSource: null,
      onNodeFocusChange: null,

      setTargetNodeId: jest.fn(),
      setTreeTargetNodeId: jest.fn(),
      setOnNodeFocusChange: jest.fn(callback => {
        mockStore.onNodeFocusChange = callback
      }),
      dispatch: jest.fn(),

      // Mock navigation functions that should trigger callbacks
      navigateToNode: jest.fn(nodeId => {
        mockStore.targetNodeId = nodeId
        mockStore.isNavigating = true
        mockStore.lastNavigationSource = 'outline'

        // Simulate the callback invocation with error handling
        if (mockStore.onNodeFocusChange) {
          try {
            mockStore.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }
      }),

      navigateToTreeNode: jest.fn(nodeId => {
        mockStore.treeTargetNodeId = nodeId
        mockStore.isNavigating = true
        mockStore.lastNavigationSource = 'visualization'

        // Simulate the callback invocation with error handling
        if (mockStore.onNodeFocusChange) {
          try {
            mockStore.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }
      }),

      navigateToTreeNodeFromReactFlow: jest.fn(nodeId => {
        mockStore.treeTargetNodeId = nodeId
        mockStore.preventReactFlowFocus = true
        mockStore.isNavigating = true
        mockStore.lastNavigationSource = 'visualization'

        // Simulate the callback invocation with error handling
        if (mockStore.onNodeFocusChange) {
          try {
            mockStore.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }
      }),

      cleanup: jest.fn(),
    }
    ;(useNavigationStore as unknown as jest.Mock).mockReturnValue(mockStore)
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('Focus callback registration', () => {
    it('should allow setting focus change callback', () => {
      mockStore.setOnNodeFocusChange(mockCallback)

      expect(mockStore.onNodeFocusChange).toBe(mockCallback)
    })

    it('should allow clearing focus change callback', () => {
      mockStore.setOnNodeFocusChange(mockCallback)
      mockStore.setOnNodeFocusChange(null)

      expect(mockStore.onNodeFocusChange).toBeNull()
    })
  })

  describe('Navigation function callback integration', () => {
    beforeEach(() => {
      mockStore.setOnNodeFocusChange(mockCallback)
    })

    it('should trigger callback when navigateToNode is called', () => {
      mockStore.navigateToNode('test-node-1')

      expect(mockCallback).toHaveBeenCalledWith('test-node-1')
      expect(mockStore.targetNodeId).toBe('test-node-1')
      expect(mockStore.lastNavigationSource).toBe('outline')
    })

    it('should trigger callback when navigateToTreeNode is called', () => {
      mockStore.navigateToTreeNode('test-node-2')

      expect(mockCallback).toHaveBeenCalledWith('test-node-2')
      expect(mockStore.treeTargetNodeId).toBe('test-node-2')
      expect(mockStore.lastNavigationSource).toBe('visualization')
    })

    it('should trigger callback when navigateToTreeNodeFromReactFlow is called', () => {
      mockStore.navigateToTreeNodeFromReactFlow('test-node-3')

      expect(mockCallback).toHaveBeenCalledWith('test-node-3')
      expect(mockStore.treeTargetNodeId).toBe('test-node-3')
      expect(mockStore.preventReactFlowFocus).toBe(true)
      expect(mockStore.lastNavigationSource).toBe('visualization')
    })

    it('should not trigger callback when no callback is registered', () => {
      mockStore.setOnNodeFocusChange(null)

      mockStore.navigateToNode('test-node-4')

      expect(mockCallback).not.toHaveBeenCalled()
      expect(mockStore.targetNodeId).toBe('test-node-4')
    })
  })

  describe('Multiple navigation calls', () => {
    beforeEach(() => {
      mockStore.setOnNodeFocusChange(mockCallback)
    })

    it('should trigger callback for each navigation call', () => {
      mockStore.navigateToNode('node-1')
      mockStore.navigateToTreeNode('node-2')
      mockStore.navigateToTreeNodeFromReactFlow('node-3')

      expect(mockCallback).toHaveBeenCalledTimes(3)
      expect(mockCallback).toHaveBeenNthCalledWith(1, 'node-1')
      expect(mockCallback).toHaveBeenNthCalledWith(2, 'node-2')
      expect(mockCallback).toHaveBeenNthCalledWith(3, 'node-3')
    })

    it('should handle rapid navigation calls', () => {
      mockStore.navigateToNode('rapid-1')
      mockStore.navigateToNode('rapid-2')
      mockStore.navigateToNode('rapid-3')

      expect(mockCallback).toHaveBeenCalledTimes(3)
      expect(mockCallback).toHaveBeenLastCalledWith('rapid-3')
    })
  })

  describe('Callback error handling', () => {
    it('should handle callback errors gracefully', () => {
      const errorCallback = jest.fn(() => {
        throw new Error('Callback error')
      })

      mockStore.setOnNodeFocusChange(errorCallback)

      // Should not throw when callback errors
      expect(() => {
        mockStore.navigateToNode('error-node')
      }).not.toThrow()

      expect(errorCallback).toHaveBeenCalledWith('error-node')
    })

    it('should continue working after callback error', () => {
      const errorCallback = jest.fn(() => {
        throw new Error('Callback error')
      })

      mockStore.setOnNodeFocusChange(errorCallback)
      mockStore.navigateToNode('error-node-1')

      // Replace with working callback
      mockStore.setOnNodeFocusChange(mockCallback)
      mockStore.navigateToNode('working-node')

      expect(mockCallback).toHaveBeenCalledWith('working-node')
    })
  })

  describe('State consistency', () => {
    beforeEach(() => {
      mockStore.setOnNodeFocusChange(mockCallback)
    })

    it('should maintain navigation state when callback is triggered', () => {
      mockStore.navigateToNode('state-test-node')

      expect(mockStore.isNavigating).toBe(true)
      expect(mockStore.targetNodeId).toBe('state-test-node')
      expect(mockStore.lastNavigationSource).toBe('outline')
      expect(mockCallback).toHaveBeenCalledWith('state-test-node')
    })

    it('should handle different navigation sources correctly', () => {
      mockStore.navigateToNode('outline-node')
      expect(mockStore.lastNavigationSource).toBe('outline')

      mockStore.navigateToTreeNode('viz-node')
      expect(mockStore.lastNavigationSource).toBe('visualization')

      mockStore.navigateToTreeNodeFromReactFlow('flow-node')
      expect(mockStore.lastNavigationSource).toBe('visualization')
      expect(mockStore.preventReactFlowFocus).toBe(true)
    })
  })

  describe('Integration scenarios', () => {
    it('should support URL focus parameter workflow', () => {
      // Simulate focus parameter hook registering callback
      const urlUpdateCallback = jest.fn()
      mockStore.setOnNodeFocusChange(urlUpdateCallback)

      // Simulate user clicking outline node
      mockStore.navigateToNode('clicked-outline-node')

      // Should update URL
      expect(urlUpdateCallback).toHaveBeenCalledWith('clicked-outline-node')

      // Simulate user clicking visualization node
      mockStore.navigateToTreeNodeFromReactFlow('clicked-viz-node')

      // Should update URL again
      expect(urlUpdateCallback).toHaveBeenCalledWith('clicked-viz-node')
      expect(urlUpdateCallback).toHaveBeenCalledTimes(2)
    })

    it('should handle callback replacement during navigation', () => {
      const firstCallback = jest.fn()
      const secondCallback = jest.fn()

      mockStore.setOnNodeFocusChange(firstCallback)
      mockStore.navigateToNode('node-1')

      expect(firstCallback).toHaveBeenCalledWith('node-1')
      expect(secondCallback).not.toHaveBeenCalled()

      // Replace callback
      mockStore.setOnNodeFocusChange(secondCallback)
      mockStore.navigateToNode('node-2')

      expect(firstCallback).toHaveBeenCalledTimes(1)
      expect(secondCallback).toHaveBeenCalledWith('node-2')
    })
  })
})
