/**
 * Unit tests for ChatTabContent component - simplified version to prevent CI failures
 */

import React from 'react'
import { render, screen } from '@testing-library/react'

// Mock all dependencies to prevent rendering issues
jest.mock('@/app/(conv)/dragTree/[dragTreeId]/hooks/useAiConversation', () => ({
  useAiConversation: jest.fn(() => ({
    messages: [],
    hasMoreMessages: false,
    isLoadingMoreMessages: false,
    loadMoreMessages: jest.fn(),
    input: '',
    handleInputChange: jest.fn(),
    handleSubmit: jest.fn(),
    isLoading: false,
    append: jest.fn(),
    // Ensure assistant UI renders by providing a conversation object
    conversation: { id: 'thread_mock' },
    isLoadingConversation: false,
    liveSteps: [],
    isStreamingSteps: false,
  })),
}))

jest.mock('@/app/stores/ui_store', () => ({
  useUIStore: jest.fn(() => true),
}))

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: {
      user: {
        subscription: {
          tier: 'FREE',
        },
      },
    },
  })),
}))

jest.mock('@/app/stores/dragtree_store', () => ({
  useDragTreeStore: jest.fn(selector => {
    const state = {
      nodeContent: new Map([
        ['node1', new Map([['content', { contentText: 'Test content 1' }]])],
        ['node2', new Map([['content', { contentText: 'Test content 2' }]])],
      ]),
      findNodeById: jest.fn(() => ({ label: 'Test Node' })),
    }
    return selector ? selector(state) : state
  }),
}))

jest.mock('@/app/stores/navigation_store', () => ({
  useNavigationStore: jest.fn(() => ({
    navigateToTreeNode: jest.fn(),
  })),
}))

jest.mock(
  '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore',
  () => ({
    useWorkspaceLayoutStore: jest.fn(selector => {
      const state = {
        tabs: [],
        updateTabAiPaneData: jest.fn(),
        updateTabTitle: jest.fn(),
      }
      return selector ? selector(state) : state
    }),
  })
)

jest.mock('@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore', () => ({
  useAssetStore: jest.fn(selector => {
    const state = {
      assets: [],
      addAsset: jest.fn(),
      updateAsset: jest.fn(),
      removeAsset: jest.fn(),
      bulkLoadAssets: jest.fn(),
      getAssetById: jest.fn(),
    }
    return selector ? selector(state) : state
  }),
}))

// AI Pane store is now part of useWorkspaceLayoutStore

jest.mock('@/app/(conv)/dragTree/[dragTreeId]/lib/feature-flags', () => ({
  featureFlags: {},
}))

jest.mock(
  '@/app/(conv)/dragTree/[dragTreeId]/components/chat/SimpleMessageList',
  () => {
    return function MockSimpleMessageList() {
      return <div data-testid="simple-message-list">Simple Message List</div>
    }
  }
)

jest.mock(
  '@/app/(conv)/dragTree/[dragTreeId]/components/chat/SimpleChatInput',
  () => {
    return function MockSimpleChatInput() {
      return <div data-testid="simple-chat-input">Simple Chat Input</div>
    }
  }
)

jest.mock(
  '@/app/(conv)/dragTree/[dragTreeId]/components/chat/ReasoningTimeline',
  () => {
    return function MockReasoningTimeline() {
      return <div data-testid="reasoning-timeline">Reasoning Timeline</div>
    }
  }
)

jest.mock(
  '@/app/(conv)/dragTree/[dragTreeId]/components/chat/InfiniteScrollContainer',
  () => {
    return function MockInfiniteScrollContainer({
      children,
    }: {
      children: React.ReactNode
    }) {
      return <div data-testid="infinite-scroll-container">{children}</div>
    }
  }
)

jest.mock('@/app/components/editor/BaseTiptapEditor', () => {
  return function MockBaseTiptapEditor() {
    return <div data-testid="base-tiptap-editor">Base TipTap Editor</div>
  }
})

jest.mock('@/app/components/editor/ReadOnlyTiptapEditor', () => {
  return function MockReadOnlyTiptapEditor() {
    return (
      <div data-testid="readonly-tiptap-editor">ReadOnly TipTap Editor</div>
    )
  }
})

jest.mock('@/app/components/editor/utils', () => ({
  convertTiptapJsonToMarkdown: jest.fn(() => 'Converted markdown'),
}))

jest.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: any) => (
    <button {...props}>{children}</button>
  ),
}))

jest.mock('@/components/ui/textarea', () => ({
  Textarea: (props: any) => <textarea {...props} />,
}))

jest.mock('@/components/ui/dialog', () => ({
  Dialog: ({ children }: any) => <div data-testid="dialog">{children}</div>,
  DialogContent: ({ children }: any) => (
    <div data-testid="dialog-content">{children}</div>
  ),
  DialogHeader: ({ children }: any) => (
    <div data-testid="dialog-header">{children}</div>
  ),
  DialogTitle: ({ children }: any) => (
    <div data-testid="dialog-title">{children}</div>
  ),
}))

jest.mock('@/components/ui/input', () => ({
  Input: (props: any) => <input {...props} />,
}))

jest.mock('react-hot-toast', () => ({
  __esModule: true,
  default: {
    error: jest.fn(),
    success: jest.fn(),
  },
}))

// Mock fetch globally with a resolved conversation to satisfy UI expectations
global.fetch = jest.fn(async () => ({
  ok: true,
  json: async () => ({
    conversation: { id: 'thread_existing123', messages: [] },
  }),
})) as unknown as typeof fetch

// Mock use-stick-to-bottom
jest.mock('use-stick-to-bottom', () => ({
  __esModule: true,
  StickToBottom: ({ children, className, ...props }: any) => (
    <div data-testid="stick-to-bottom" className={className} {...props}>
      {children}
    </div>
  ),
  useStickToBottomContext: () => ({
    isAtBottom: true,
    scrollToBottom: jest.fn(),
  }),
  default: {
    Content: ({ children, className, ...props }: any) => (
      <div
        data-testid="stick-to-bottom-content"
        className={className}
        {...props}
      >
        {children}
      </div>
    ),
    Viewport: ({ children, className, ...props }: any) => (
      <div
        data-testid="stick-to-bottom-viewport"
        className={className}
        {...props}
      >
        {children}
      </div>
    ),
  },
}))

// Import the component after all mocks are set up
import ChatTabContent from '@/app/(conv)/dragTree/[dragTreeId]/components/tabs/ChatTabContent'

describe('ChatTabContent Component', () => {
  const mockTab = {
    id: 'test-tab-1',
    title: 'Test Chat',
    fullTitle: 'Test Chat',
    type: 'chat' as const,
    isClosable: true,
    aiPaneData: {
      type: 'chat' as const,
      model: 'gpt-4.1',
      prompt: '',
      contextIds: ['node1', 'node2'],
      settings: {},
    },
  }

  const defaultProps = {
    tab: mockTab,
    dragTreeId: 'test-drag-tree',
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(global.fetch as jest.Mock).mockClear()
    ;(global.fetch as jest.Mock).mockImplementation(async () => ({
      ok: true,
      json: async () => ({
        conversation: { id: 'thread_existing123', messages: [] },
      }),
    }))
  })

  describe('Basic Rendering', () => {
    it('should render without crashing', () => {
      expect(() => {
        render(<ChatTabContent {...defaultProps} />)
      }).not.toThrow()
    })

    it('should render assistant UI wrapper when conversation is ready', async () => {
      const propsWithConversation = {
        ...defaultProps,
        tab: {
          ...defaultProps.tab,
          aiPaneData: {
            ...defaultProps.tab.aiPaneData,
            conversationId: 'thread_test123456789',
          },
        },
      }
      render(<ChatTabContent {...propsWithConversation} />)
      // Conversation ID should be displayed somewhere in settings
      expect(
        await screen.findByText('thread_test123456789')
      ).toBeInTheDocument()
      // Input area should be rendered
      expect(
        screen.getByPlaceholderText(/Message AI Assistant|Start a chat/)
      ).toBeInTheDocument()
    })

    it('should generate conversation ID immediately for new tabs', () => {
      render(<ChatTabContent {...defaultProps} />)
      // With the new approach, conversation ID is generated immediately
      // so there should be no "Initializing chat..." message
      expect(screen.queryByText('Initializing chat...')).not.toBeInTheDocument()
    })

    it('should display context count in settings dialog content', () => {
      render(<ChatTabContent {...defaultProps} />)
      // Header no longer shows context; verify settings dialog content includes count label
      expect(screen.getByText('Context Items')).toBeInTheDocument()
      expect(screen.getByText(/2\s*selected/)).toBeInTheDocument()
      expect(
        screen.getByText(/Context Items\s*\(\s*2\s*\)/)
      ).toBeInTheDocument()
    })
  })

  describe('Component Structure', () => {
    it('should render with proper tab data', () => {
      const { container } = render(<ChatTabContent {...defaultProps} />)
      expect(container.firstChild).toBeTruthy()
    })

    it('should handle tab without conversation ID', () => {
      expect(() => {
        render(<ChatTabContent {...defaultProps} />)
      }).not.toThrow()
    })

    it('should handle tab with conversation ID', async () => {
      const tabWithConversation = {
        ...mockTab,
        aiPaneData: {
          ...mockTab.aiPaneData!,
          conversationId: 'thread_test123',
        },
      }

      render(
        <ChatTabContent tab={tabWithConversation} dragTreeId="test-drag-tree" />
      )
      // Conversation ID should be visible in settings
      expect(await screen.findByText('thread_test123')).toBeInTheDocument()
    })
  })

  describe('Conversation Management', () => {
    it('should generate conversation ID immediately without API calls', () => {
      render(<ChatTabContent {...defaultProps} />)

      // With the new approach, no API calls are made during initialization
      // The conversation ID is generated client-side and conversation creation
      // happens idempotently in the chat API when the first message is sent
      expect(global.fetch).not.toHaveBeenCalled()
    })

    it('should use existing conversation ID when provided', async () => {
      const propsWithConversation = {
        ...defaultProps,
        tab: {
          ...defaultProps.tab,
          aiPaneData: {
            ...defaultProps.tab.aiPaneData,
            conversationId: 'thread_existing123',
          },
        },
      }

      render(<ChatTabContent {...propsWithConversation} />)
      // Should fetch existing conversation by ID
      expect(global.fetch).toHaveBeenCalledWith(
        '/api/aipane/conversations/thread_existing123'
      )
      expect(await screen.findByText('thread_existing123')).toBeInTheDocument()
    })
  })
})
