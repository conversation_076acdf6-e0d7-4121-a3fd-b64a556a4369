import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import React from 'react'
import { FiFile, FiImage } from 'react-icons/fi'
import {
  AssetTypeRegistry,
  assetTypeRegistry,
  registerAssetType,
  getAssetType,
  isValidAssetType,
  isAssetTab,
  type AssetTypeDefinition,
} from '@/lib/asset-types/registry'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'

// Mock component for testing
const MockTabComponent: React.FC<{ tab: Tab; dragTreeId: string }> = () => {
  return React.createElement('div', {}, 'Mock Component')
}

// Test asset type definitions
const testAssetType: AssetTypeDefinition = {
  id: 'test-type',
  displayName: 'Test Type',
  icon: FiFile,
  tabComponent: MockTabComponent,
  getAssetTypeColor: () => 'text-blue-600',
  createTabTitle: (asset: any) => `Test - ${asset.title}`,
  isAssetType: (type: string) => type === 'test-type',
  createAssetTabData: (asset: any) => ({
    type: 'test-type',
    model: asset.model,
    prompt: asset.prompt,
    contextIds: asset.contextIds || [],
    settings: {},
  }),
}

const anotherTestAssetType: AssetTypeDefinition = {
  id: 'another-type',
  displayName: 'Another Type',
  icon: FiImage,
  tabComponent: MockTabComponent,
  getAssetTypeColor: () => 'text-green-600',
  createTabTitle: (asset: any) => `Another - ${asset.title}`,
  isAssetType: (type: string) => type === 'another-type',
}

describe('AssetTypeRegistry', () => {
  let registry: AssetTypeRegistry

  beforeEach(() => {
    registry = new AssetTypeRegistry()
  })

  afterEach(() => {
    // Clean up global registry
    assetTypeRegistry.clear()
  })

  describe('Basic Registry Operations', () => {
    it('should register and retrieve asset types', () => {
      registry.register(testAssetType)

      const retrieved = registry.get('test-type')
      expect(retrieved).toBe(testAssetType)
      expect(retrieved?.displayName).toBe('Test Type')
    })

    it('should return undefined for non-existent asset types', () => {
      const retrieved = registry.get('non-existent')
      expect(retrieved).toBeUndefined()
    })

    it('should check if asset type exists', () => {
      registry.register(testAssetType)

      expect(registry.has('test-type')).toBe(true)
      expect(registry.has('non-existent')).toBe(false)
    })

    it('should validate asset types', () => {
      registry.register(testAssetType)

      expect(registry.isValidAssetType('test-type')).toBe(true)
      expect(registry.isValidAssetType('invalid-type')).toBe(false)
    })

    it('should get all registered types', () => {
      registry.register(testAssetType)
      registry.register(anotherTestAssetType)

      const types = registry.getRegisteredTypes()
      expect(types).toContain('test-type')
      expect(types).toContain('another-type')
      expect(types).toHaveLength(2)
    })

    it('should get all definitions', () => {
      registry.register(testAssetType)
      registry.register(anotherTestAssetType)

      const definitions = registry.getAllDefinitions()
      expect(definitions).toHaveLength(2)
      expect(definitions).toContain(testAssetType)
      expect(definitions).toContain(anotherTestAssetType)
    })
  })

  describe('Registry Management', () => {
    it('should warn when overwriting existing asset type', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation()

      registry.register(testAssetType)
      registry.register({ ...testAssetType, displayName: 'Updated Test' })

      expect(consoleSpy).toHaveBeenCalledWith(
        "Asset type 'test-type' is already registered. Overwriting."
      )

      consoleSpy.mockRestore()
    })

    it('should unregister asset types', () => {
      registry.register(testAssetType)
      expect(registry.has('test-type')).toBe(true)

      const result = registry.unregister('test-type')
      expect(result).toBe(true)
      expect(registry.has('test-type')).toBe(false)
    })

    it('should return false when unregistering non-existent type', () => {
      const result = registry.unregister('non-existent')
      expect(result).toBe(false)
    })

    it('should clear all registrations', () => {
      registry.register(testAssetType)
      registry.register(anotherTestAssetType)
      expect(registry.getRegisteredTypes()).toHaveLength(2)

      registry.clear()
      expect(registry.getRegisteredTypes()).toHaveLength(0)
    })

    it('should get with fallback', () => {
      const fallback = anotherTestAssetType

      // Should return registered type
      registry.register(testAssetType)
      expect(registry.getWithFallback('test-type', fallback)).toBe(
        testAssetType
      )

      // Should return fallback for non-existent type
      expect(registry.getWithFallback('non-existent', fallback)).toBe(fallback)

      // Should return undefined when no fallback provided
      expect(registry.getWithFallback('non-existent')).toBeUndefined()
    })
  })
})

describe('Global Registry Functions', () => {
  beforeEach(() => {
    assetTypeRegistry.clear()
  })

  it('should register asset type globally', () => {
    registerAssetType(testAssetType)

    expect(getAssetType('test-type')).toBe(testAssetType)
    expect(isValidAssetType('test-type')).toBe(true)
  })

  it('should validate asset types globally', () => {
    registerAssetType(testAssetType)

    expect(isValidAssetType('test-type')).toBe(true)
    expect(isValidAssetType('invalid-type')).toBe(false)
  })
})

describe('isAssetTab Type Guard', () => {
  beforeEach(() => {
    assetTypeRegistry.clear()
    registerAssetType(testAssetType)
  })

  it('should identify asset tabs correctly', () => {
    const assetTab: Tab = {
      id: 'tab-1',
      title: 'Test Tab',
      fullTitle: 'Test Tab',
      type: 'test-type',
      isClosable: true,
      aiPaneData: {
        type: 'test-type',
        model: 'gpt-4',
        prompt: 'test prompt',
        contextIds: [],
        settings: {},
      },
    }

    const coreTab: Tab = {
      id: 'main',
      title: 'Main',
      fullTitle: 'Main',
      type: 'main',
      isClosable: false,
    }

    const invalidAssetTab: Tab = {
      id: 'tab-2',
      title: 'Invalid Tab',
      fullTitle: 'Invalid Tab',
      type: 'invalid-type',
      isClosable: true,
      aiPaneData: {
        type: 'invalid-type',
        model: 'gpt-4',
        prompt: 'test prompt',
        contextIds: [],
        settings: {},
      },
    }

    expect(isAssetTab(assetTab)).toBe(true)
    expect(isAssetTab(coreTab)).toBe(false)
    expect(isAssetTab(invalidAssetTab)).toBe(false)
  })

  it('should return false for tabs without aiPaneData', () => {
    const tabWithoutAiData: Tab = {
      id: 'tab-1',
      title: 'Test Tab',
      fullTitle: 'Test Tab',
      type: 'test-type',
      isClosable: true,
    }

    expect(isAssetTab(tabWithoutAiData)).toBe(false)
  })
})
