import { SubscriptionTier } from '@prisma/client'
import {
  TierAccess,
  getTierPermissions,
  getResourceLimits,
  getAccessPermissions,
  hasPaidFeatures,
  hasFeature,
  getFeatureLimit,
  canPerformAction,
  hasPermission,
  getBatchResearchLimit,
} from '@/app/configs/tier-permissions'

describe('TierAccess config', () => {
  describe('Resource Limits', () => {
    test('FREE tier has reasonable resource limits', () => {
      const limits = getResourceLimits(SubscriptionTier.FREE)
      expect(limits.maxDragTrees).toBeGreaterThan(0)
      expect(limits.aiPaneGeneratePerTree).toBeGreaterThan(0)
      expect(limits.aiPaneChatPerTree).toBeGreaterThan(0)
      expect(limits.maxBatchResearchItems).toBeGreaterThan(0)
    })

    test('VIEWER tier has zero limits', () => {
      const limits = getResourceLimits(SubscriptionTier.VIEWER)
      expect(limits.maxDragTrees).toBe(0)
      expect(limits.aiPaneGeneratePerTree).toBe(0)
      expect(limits.aiPaneChatPerTree).toBe(0)
      expect(limits.maxBatchResearchItems).toBe(0)
    })

    test('PRO tier has higher limits', () => {
      const freeLimits = getResourceLimits(SubscriptionTier.FREE)
      const proLimits = getResourceLimits(SubscriptionTier.PRO)
      expect(proLimits.maxDragTrees).toBeGreaterThan(freeLimits.maxDragTrees)
      expect(proLimits.aiPaneGeneratePerTree).toBeGreaterThan(
        freeLimits.aiPaneGeneratePerTree
      )
      expect(proLimits.aiPaneChatPerTree).toBeGreaterThan(
        freeLimits.aiPaneChatPerTree
      )
      expect(proLimits.maxBatchResearchItems).toBeGreaterThan(
        freeLimits.maxBatchResearchItems
      )
    })
  })

  describe('Access Permissions', () => {
    test('FREE tier has most permissions enabled', () => {
      const permissions = getAccessPermissions(SubscriptionTier.FREE)
      expect(permissions.canEditDragTreeTitle).toBe(true)
      expect(permissions.canCreateDragTree).toBe(true)
      expect(permissions.canEditDragTreeNodeTitle).toBe(true)
      expect(permissions.canDeleteDragTreeNode).toBe(true)
      expect(permissions.canCreateAiChat).toBe(true)
      expect(permissions.canCreateAiMessage).toBe(true)
      expect(permissions.canDeleteAiChat).toBe(true)
      expect(permissions.canCreateAiGeneration).toBe(true)
      expect(permissions.canEditAiGenerationTitle).toBe(true)
      expect(permissions.canEditAiGenerationContent).toBe(true)
      expect(permissions.canDeleteAiGeneration).toBe(true)
      expect(permissions.canCreateQuickResearch).toBe(true)
      expect(permissions.canCreateDiagramExport).toBe(false) // Only restriction for FREE
      expect(permissions.canDeleteAsset).toBe(true)
    })

    test('VIEWER tier has all permissions disabled', () => {
      const permissions = getAccessPermissions(SubscriptionTier.VIEWER)
      expect(permissions.canEditDragTreeTitle).toBe(false)
      expect(permissions.canCreateDragTree).toBe(false)
      expect(permissions.canEditDragTreeNodeTitle).toBe(false)
      expect(permissions.canDeleteDragTreeNode).toBe(false)
      expect(permissions.canCreateAiChat).toBe(false)
      expect(permissions.canCreateAiMessage).toBe(false)
      expect(permissions.canDeleteAiChat).toBe(false)
      expect(permissions.canCreateAiGeneration).toBe(false)
      expect(permissions.canEditAiGenerationTitle).toBe(false)
      expect(permissions.canEditAiGenerationContent).toBe(false)
      expect(permissions.canDeleteAiGeneration).toBe(false)
      expect(permissions.canCreateQuickResearch).toBe(false)
      expect(permissions.canCreateDiagramExport).toBe(false)
      expect(permissions.canDeleteAsset).toBe(false)
    })

    test('PRO tier has all permissions enabled', () => {
      const permissions = getAccessPermissions(SubscriptionTier.PRO)
      expect(permissions.canEditDragTreeTitle).toBe(true)
      expect(permissions.canCreateDragTree).toBe(true)
      expect(permissions.canEditDragTreeNodeTitle).toBe(true)
      expect(permissions.canDeleteDragTreeNode).toBe(true)
      expect(permissions.canCreateAiChat).toBe(true)
      expect(permissions.canCreateAiMessage).toBe(true)
      expect(permissions.canDeleteAiChat).toBe(true)
      expect(permissions.canCreateAiGeneration).toBe(true)
      expect(permissions.canEditAiGenerationTitle).toBe(true)
      expect(permissions.canEditAiGenerationContent).toBe(true)
      expect(permissions.canDeleteAiGeneration).toBe(true)
      expect(permissions.canCreateQuickResearch).toBe(true)
      expect(permissions.canCreateDiagramExport).toBe(true)
      expect(permissions.canDeleteAsset).toBe(true)
    })
  })

  describe('Permission Helper Functions', () => {
    test('hasPermission function works correctly', () => {
      expect(hasPermission(SubscriptionTier.FREE, 'canCreateAiChat')).toBe(true)
      expect(hasPermission(SubscriptionTier.VIEWER, 'canCreateAiChat')).toBe(
        false
      )
      expect(
        hasPermission(SubscriptionTier.PRO, 'canCreateDiagramExport')
      ).toBe(true)
      expect(
        hasPermission(SubscriptionTier.FREE, 'canCreateDiagramExport')
      ).toBe(false)
    })

    test('canPerformAction with owner bypass', () => {
      // Owner should always be able to perform actions
      expect(
        canPerformAction(
          SubscriptionTier.VIEWER,
          'canEditDragTreeNodeTitle',
          true
        )
      ).toBe(true)
      expect(
        canPerformAction(SubscriptionTier.VIEWER, 'canDeleteDragTreeNode', true)
      ).toBe(true)

      // Non-owner should follow tier permissions
      expect(
        canPerformAction(
          SubscriptionTier.VIEWER,
          'canEditDragTreeNodeTitle',
          false
        )
      ).toBe(false)
      expect(
        canPerformAction(
          SubscriptionTier.FREE,
          'canEditDragTreeNodeTitle',
          false
        )
      ).toBe(true)
    })

    test('getBatchResearchLimit function', () => {
      const viewerLimit = getBatchResearchLimit(SubscriptionTier.VIEWER)
      const freeLimit = getBatchResearchLimit(SubscriptionTier.FREE)
      const proLimit = getBatchResearchLimit(SubscriptionTier.PRO)
      expect(viewerLimit).toBe(0)
      expect(freeLimit).toBeGreaterThan(viewerLimit)
      expect(proLimit).toBeGreaterThanOrEqual(freeLimit)
    })
  })

  describe('Legacy Compatibility', () => {
    test('getTierPermissions maintains backward compatibility', () => {
      const p = getTierPermissions(SubscriptionTier.FREE)
      expect(p.maxDragTrees).toBe(3)
      expect(p.aiPaneGeneratePerTree).toBe(3)
      expect(p.aiPaneChatPerTree).toBe(3)
      expect(p.quickResearch).toBe(true)
      expect(p.canExportDiagram).toBe(false)
    })

    test('hasFeature function works with legacy features', () => {
      expect(hasFeature(SubscriptionTier.FREE, 'quickResearch')).toBe(true)
      expect(hasFeature(SubscriptionTier.FREE, 'canExportDiagram')).toBe(false)
      expect(hasFeature(SubscriptionTier.PRO, 'canExportDiagram')).toBe(true)
    })

    test('getFeatureLimit function works correctly', () => {
      expect(getFeatureLimit(SubscriptionTier.FREE, 'maxDragTrees')).toBe(3)
      expect(getFeatureLimit(SubscriptionTier.PRO, 'maxDragTrees')).toBe(100)
      expect(getFeatureLimit(SubscriptionTier.VIEWER, 'maxDragTrees')).toBe(0)
    })

    test('hasPaidFeatures function', () => {
      expect(hasPaidFeatures(SubscriptionTier.FREE)).toBe(false)
      expect(hasPaidFeatures(SubscriptionTier.VIEWER)).toBe(false)
      expect(hasPaidFeatures(SubscriptionTier.DUMMY)).toBe(false)
      expect(hasPaidFeatures(SubscriptionTier.PRO)).toBe(true)
      expect(hasPaidFeatures(SubscriptionTier.GUEST)).toBe(true)
      expect(hasPaidFeatures(SubscriptionTier.ULTRA)).toBe(true)
      expect(hasPaidFeatures(SubscriptionTier.BUSINESS)).toBe(true)
    })
  })

  describe('All Tiers Configuration', () => {
    test('All subscription tiers are configured', () => {
      const allTiers = Object.values(SubscriptionTier)
      allTiers.forEach(tier => {
        expect(TierAccess[tier]).toBeDefined()
        expect(TierAccess[tier].resourceLimits).toBeDefined()
        expect(TierAccess[tier].accessPermissions).toBeDefined()
      })
    })

    test('DUMMY tier has restricted permissions for testing', () => {
      const permissions = getAccessPermissions(SubscriptionTier.DUMMY)
      expect(permissions.canEditDragTreeTitle).toBe(false)
      expect(permissions.canCreateDragTree).toBe(false)
      expect(permissions.canEditDragTreeNodeTitle).toBe(false)
      expect(permissions.canDeleteDragTreeNode).toBe(false)
      expect(permissions.canCreateAiChat).toBe(false)
      expect(permissions.canCreateAiMessage).toBe(false)
      expect(permissions.canDeleteAiChat).toBe(false)
      expect(permissions.canCreateAiGeneration).toBe(false)
      expect(permissions.canEditAiGenerationTitle).toBe(false)
      expect(permissions.canEditAiGenerationContent).toBe(false)
      expect(permissions.canDeleteAiGeneration).toBe(false)
      expect(permissions.canCreateQuickResearch).toBe(true) // Only this is allowed
      expect(permissions.canCreateDiagramExport).toBe(false)
      expect(permissions.canDeleteAsset).toBe(false)
    })
  })
})
