import {
  DragTreeNodeContentStatus,
  DragTreeNodeContentType,
  DragTreeNodeType,
} from '@prisma/client'
import {
  convertTipTapJsonToPlainText,
  generateResearchPreviewMarkdown,
  generateResearchPreviewMarkdownFromSnapshot,
  getCategoryContextByCategoryId,
  getQuestionContextByContentId,
  markdownToPlainText,
  truncateToTokenLimit,
} from '@/app/api/dragtree/shared/context-tools'
import { getContextToolDefinitions } from '@/app/api/dragtree/shared/context-tools/tool-definitions'
import type { DragTreeResearchSnapshot } from '@/app/api/dragtree/shared/context-tools/types'
import { getResearchPreviewData } from '@/app/server-actions/drag-tree/get-preview-snapshot'

jest.mock('@/app/server-actions/drag-tree/get-preview-snapshot', () => ({
  getResearchPreviewData: jest.fn(),
}))

describe('Research Context Toolkit utilities', () => {
  const createBaseSnapshot = (): DragTreeResearchSnapshot => ({
    dragTreeId: 'tree_1',
    title: 'Launching a Yoga Studio in San Francisco',
    hierarchy: {
      rootId: 'cat_root',
      children: {
        cat_root: ['cat_market', 'cat_operations'],
        cat_market: ['que_competitors'],
        cat_operations: ['que_staffing'],
        que_competitors: [],
        que_staffing: [],
      },
    },
    nodes: [
      {
        id: 'cat_root',
        label: 'Root Category',
        nodeType: DragTreeNodeType.CATEGORY,
      },
      {
        id: 'cat_market',
        label: 'Market Opportunity Assessment',
        nodeType: DragTreeNodeType.CATEGORY,
      },
      {
        id: 'cat_operations',
        label: 'Operational Planning',
        nodeType: DragTreeNodeType.CATEGORY,
      },
      {
        id: 'que_competitors',
        label: 'Who are the top boutique competitors within five miles?',
        nodeType: DragTreeNodeType.QUESTION,
      },
      {
        id: 'que_staffing',
        label: 'What staffing model keeps labor costs under 30% of revenue?',
        nodeType: DragTreeNodeType.QUESTION,
      },
    ],
    contents: [
      {
        id: 'content_active_1',
        nodeId: 'que_competitors',
        status: DragTreeNodeContentStatus.ACTIVE,
        contentText:
          'Competitors include FlowHouse SF, Mission Yoga Loft, and Soma Core Studio.',
      },
      {
        id: 'content_processing_1',
        nodeId: 'que_staffing',
        status: DragTreeNodeContentStatus.PROCESSING,
        contentText: JSON.stringify({
          type: 'doc',
          content: [
            {
              type: 'paragraph',
              content: [
                {
                  type: 'text',
                  text: 'Staffing research still in progress.',
                },
              ],
            },
          ],
        }),
      },
    ],
  })

  it('generates markdown preview limited to active research content', () => {
    const snapshot = createBaseSnapshot()
    const preview = generateResearchPreviewMarkdownFromSnapshot(snapshot)

    expect(preview).toContain(
      '# Launching a Yoga Studio in San Francisco [cat_root]'
    )
    expect(preview).toContain('## Market Opportunity Assessment [cat_market]')
    expect(preview).toContain(
      '- Who are the top boutique competitors within five miles? [content_active_1]'
    )
    expect(preview).not.toContain('Operational Planning')
    expect(preview).not.toContain('content_processing_1')
  })

  it('retrieves a single question with optional token truncation', () => {
    const snapshot = createBaseSnapshot()
    const result = getQuestionContextByContentId(snapshot, 'content_active_1', {
      tokenLimit: 8,
    })

    expect(result.questionText).toBe(
      'Who are the top boutique competitors within five miles?'
    )
    expect(result.contentId).toBe('content_active_1')
    expect(result.truncated).toBe(true)
    expect(result.tokenCount).toBeLessThanOrEqual(8)
  })

  it('converts TipTap JSON to plain text and respects strict token budgets', () => {
    const snapshot = createBaseSnapshot()
    snapshot.contents.push({
      id: 'content_active_2',
      nodeId: 'que_staffing',
      status: DragTreeNodeContentStatus.ACTIVE,
      contentText: JSON.stringify({
        type: 'doc',
        content: [
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Hire 4 full-time instructors and 6 contractors.',
              },
            ],
          },
          {
            type: 'paragraph',
            content: [
              {
                type: 'text',
                text: 'Schedule mix keeps utilization above 72% and payroll at 28%.',
              },
            ],
          },
        ],
      }),
    })

    const categoryResult = getCategoryContextByCategoryId(
      snapshot,
      'cat_root',
      {
        totalTokens: 10,
      }
    )

    expect(categoryResult.items).toHaveLength(2)
    expect(categoryResult.totalTokensUsed).toBeLessThanOrEqual(10)
    categoryResult.items.forEach(item => {
      expect(item.tokensUsed).toBeLessThanOrEqual(5)
      expect(item.responseText.length).toBeGreaterThan(0)
    })
  })

  it('gracefully handles markdown to plain text conversion helpers', () => {
    const markdown = `# Heading\n\n- First item\n- Second item`
    const plain = markdownToPlainText(markdown)
    expect(plain).toContain('Heading')
    expect(plain).toContain('First item')
    expect(plain.startsWith('#')).toBe(false)
  })

  it('truncates text at token boundaries without breaking readability', () => {
    const longText = Array.from({ length: 100 }, (_, i) => `chunk${i}`).join(
      ' '
    )
    const { text, tokensUsed, truncated } = truncateToTokenLimit(longText, 20)
    expect(truncated).toBe(true)
    expect(tokensUsed).toBeLessThanOrEqual(20)
    expect(text.endsWith('…')).toBe(true)
  })

  it('throws clear errors when ids are invalid', () => {
    const snapshot = createBaseSnapshot()
    expect(() =>
      getQuestionContextByContentId(snapshot, 'missing-content-id')
    ).toThrow('No research content found for id missing-content-id')

    expect(() =>
      getCategoryContextByCategoryId(snapshot, 'que_competitors')
    ).toThrow('Node que_competitors is not a category')
  })

  it('validates OpenAI function tool definitions', () => {
    const tools = getContextToolDefinitions()
    expect(tools).toHaveLength(3)
    tools.forEach(tool => {
      expect(tool.type).toBe('function')
      expect(tool.function.name).toMatch(/^\w+/)
      expect(tool.function.parameters.type).toBe('object')
      expect(tool.function.parameters.properties).toBeDefined()
    })

    const questionTool = tools[1]
    expect(questionTool.function.parameters.required).toContain('contentId')
    expect(questionTool.function.parameters.properties).toHaveProperty(
      'tokenLimit'
    )
  })

  it('converts stringified TipTap payloads into clean text', () => {
    const doc = {
      type: 'doc',
      content: [
        {
          type: 'paragraph',
          content: [
            { type: 'text', text: 'Plain text extraction works as expected.' },
          ],
        },
      ],
    }

    const output = convertTipTapJsonToPlainText(JSON.stringify(doc))
    expect(output).toBe('Plain text extraction works as expected.')
  })
})

describe('generateResearchPreviewMarkdown (database fetch)', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('fetches minimal data and returns markdown outline', async () => {
    ;(getResearchPreviewData as jest.Mock).mockResolvedValue({
      dragTreeId: 'tree_1',
      title: 'Yoga Studio Research',
      treeStructure: {
        root_id: 'cat_root',
        hierarchy: {
          cat_root: ['que_competitors'],
        },
      },
      nodes: [
        {
          id: 'cat_root',
          label: 'Root Category',
          node_type: DragTreeNodeType.CATEGORY,
        },
        {
          id: 'que_competitors',
          label: 'Who are the top boutique competitors?',
          node_type: DragTreeNodeType.QUESTION,
        },
      ],
      contents: [
        {
          id: 'content_active_1',
          drag_tree_node_id: 'que_competitors',
          status: DragTreeNodeContentStatus.ACTIVE,
          content_type: DragTreeNodeContentType.QUICK_RESEARCH,
          content_text: 'Competitor summary',
          content_metadata: {},
          created_at: new Date('2025-01-01T00:00:00Z'),
          updated_at: new Date('2025-01-02T00:00:00Z'),
        },
      ],
    })

    const markdown = await generateResearchPreviewMarkdown('tree_1')

    expect(markdown).toContain('Yoga Studio Research')
    expect(markdown).toContain('Who are the top boutique competitors?')
    expect(markdown).toContain('[content_active_1]')
    expect(getResearchPreviewData).toHaveBeenCalledWith('tree_1')
  })

  it('throws when unauthorized', async () => {
    ;(getResearchPreviewData as jest.Mock).mockRejectedValue(
      new Error('Unauthorized')
    )

    await expect(
      generateResearchPreviewMarkdown('tree_unauthorized')
    ).rejects.toThrow('Unauthorized')
    expect(getResearchPreviewData).toHaveBeenCalledWith('tree_unauthorized')
  })
})
