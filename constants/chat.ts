/**
 * Chat-related constants and configuration
 * Centralized constants to avoid string literals in components
 */

// ─────────────────────────────────────────────
// Tool Names
// ─────────────────────────────────────────────

export const TOOL_NAMES = {
  WEB_SEARCH: 'web_search',
} as const

export type ToolName = (typeof TOOL_NAMES)[keyof typeof TOOL_NAMES]

// ─────────────────────────────────────────────
// Execution Step Types
// ─────────────────────────────────────────────

export enum ExecutionStepType {
  THOUGHT = 'THOUGHT',
  TOOL_CALL = 'TOOL_CALL',
  TOOL_RESULT = 'TOOL_RESULT',
  REASONING_SUMMARY = 'REASONING_SUMMARY',
  SUB_AGENT_INVOCATION = 'SUB_AGENT_INVOCATION',
}

// ─────────────────────────────────────────────
// Rate Limiting
// ─────────────────────────────────────────────

export const RATE_LIMIT_CONFIG = {
  WINDOW_MS: 60000, // 1 minute window
  MAX_REQUESTS: 5, // 5 requests per minute
} as const

// ─────────────────────────────────────────────
// Model Configuration
// ─────────────────────────────────────────────
// Note: Model selection is now controlled server-side based on user subscription tier

export const MODEL_CONFIG = {
  MAX_STEPS: 10,
  TEMPERATURE: 0.7,
  MAX_TOKENS: 4000,
} as const

// Removed: Hardcoded model lists - models are now dynamically assigned server-side

// ─────────────────────────────────────────────
// Retry Configuration
// ─────────────────────────────────────────────

export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  BACKOFF_MS: 50,
} as const

// ─────────────────────────────────────────────
// Response Configuration
// ─────────────────────────────────────────────

export const RESPONSE_CONFIG = {
  MAX_CONTEXT_LENGTH: 8000,
  DEFAULT_ERROR_MESSAGE: 'An error occurred while processing your request.',
} as const

// ─────────────────────────────────────────────
// System Prompts
// ─────────────────────────────────────────────

export const SYSTEM_PROMPT_CONFIG = {
  DEFAULT_SYSTEM_PROMPT: `You are a helpful AI assistant. You have access to web search capabilities to help answer questions with up-to-date information.

When using web search:
1. Use it when you need current information or when the user asks about recent events
2. Search for specific, relevant terms
3. Provide sources for the information you find
4. Synthesize information from multiple sources when appropriate

Always be helpful, accurate, and cite your sources when using web search results.`,
} as const

// ─────────────────────────────────────────────
// Cache Configuration
// ─────────────────────────────────────────────

export const CACHE_CONFIG = {
  DEFAULT_TTL_MS: 60 * 60 * 1000, // 1 hour
  MAX_TTL_MS: 24 * 60 * 60 * 1000, // 24 hours
  MAX_ENTRIES: 1000,
} as const

// ─────────────────────────────────────────────
// UI Constants
// ─────────────────────────────────────────────

export const UI_CONFIG = {
  THINKING_COLLAPSE_THRESHOLD: 100, // Auto-collapse thinking sections longer than this
  MAX_STEPS_DISPLAY: 50, // Maximum steps to show in timeline
  SCROLL_DEBOUNCE_MS: 100,
} as const

// Global UI stream throttle for useChat/DefaultChatTransport to reduce re-renders
export const UI_STREAM_THROTTLE_MS = 300

// ─────────────────────────────────────────────
// Message Roles
// ─────────────────────────────────────────────

export const MESSAGE_ROLES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system',
} as const

export type MessageRole = (typeof MESSAGE_ROLES)[keyof typeof MESSAGE_ROLES]

// ─────────────────────────────────────────────
// Error Codes
// ─────────────────────────────────────────────

export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  MODEL_ERROR: 'MODEL_ERROR',
  PERSISTENCE_ERROR: 'PERSISTENCE_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  TOOL_ERROR: 'TOOL_ERROR',
} as const

export type ErrorCode = (typeof ERROR_CODES)[keyof typeof ERROR_CODES]
