'use client'

import { useControllableState } from '@radix-ui/react-use-controllable-state'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { BrainIcon, ChevronDownIcon } from 'lucide-react'
import type { ComponentProps } from 'react'
import { createContext, memo, useContext, useEffect, useState } from 'react'
import { cn } from '@/lib/utils'
import { Response } from './response'

export type ExtractReasoningTitleResult = {
  title: string
  body: string
}

const FALLBACK_REASONING_TITLE = 'Analysis'

const stripMarkdown = (value: string): string => {
  const linkStripped = value.replace(/\[(.+?)\]\((.+?)\)/g, '$1')
  return linkStripped
    .replace(/^#+\s+/, '')
    .replace(/^>\s*/, '')
    .replace(/^\d+\.\s+/, '')
    .replace(/^[-*+]\s+/, '')
    .replace(/[`*_~]/g, '')
    .trim()
}

export const extractReasoningTitle = (
  text: string | undefined | null
): ExtractReasoningTitleResult => {
  if (!text) {
    return { title: FALLBACK_REASONING_TITLE, body: '' }
  }

  const normalized = text.replace(/\r\n?/g, '\n')
  const trimmed = normalized.trim()

  if (!trimmed) {
    return { title: FALLBACK_REASONING_TITLE, body: '' }
  }

  const [firstLineRaw, ...restLines] = trimmed.split('\n')
  const cleanTitle = stripMarkdown(firstLineRaw)
  const title = cleanTitle || FALLBACK_REASONING_TITLE

  const restRaw = restLines.join('\n')
  const body = restRaw.replace(/^\n+/, '')

  return { title, body }
}

type ReasoningContextValue = {
  isStreaming: boolean
  isOpen: boolean
  setIsOpen: (open: boolean) => void
  duration: number
}

const ReasoningContext = createContext<ReasoningContextValue | null>(null)

const useReasoning = () => {
  const context = useContext(ReasoningContext)
  if (!context) {
    throw new Error("'Reasoning components must be used within Reasoning'")
  }
  return context
}

export type ReasoningProps = ComponentProps<typeof Collapsible> & {
  isStreaming?: boolean
  open?: boolean
  defaultOpen?: boolean
  onOpenChange?: (open: boolean) => void
  duration?: number
  variant?: 'default' | 'subtle'
}

export const Reasoning = memo(
  ({
    className,
    isStreaming = false,
    open,
    defaultOpen = false,
    onOpenChange,
    duration: durationProp,
    variant = 'default',
    children,
    ...props
  }: ReasoningProps) => {
    const [isOpen, setIsOpen] = useControllableState({
      prop: open,
      defaultProp: defaultOpen,
      onChange: onOpenChange,
    })
    const [duration, setDuration] = useControllableState({
      prop: durationProp,
      defaultProp: 0,
    })

    const [hasAutoClosedRef, setHasAutoClosedRef] = useState(false)
    const [startTime, setStartTime] = useState<number | null>(null)

    // Track duration when streaming starts and ends
    useEffect(() => {
      if (isStreaming) {
        if (startTime === null) {
          setStartTime(Date.now())
        }
      } else if (startTime !== null) {
        setDuration(Math.round((Date.now() - startTime) / 1000))
        setStartTime(null)
      }
    }, [isStreaming, startTime, setDuration])

    // Auto-open when streaming starts, auto-close when streaming ends (once only)
    useEffect(() => {
      if (isStreaming && !isOpen) {
        // Reset for new stream
        setDuration(0)
        setHasAutoClosedRef(false)
        setIsOpen(true)
      } else if (!isStreaming && isOpen && !defaultOpen && !hasAutoClosedRef) {
        // Add a small delay before closing to allow user to see the content
        const timer = setTimeout(() => {
          setIsOpen(false)
          setHasAutoClosedRef(true)
        }, 1000)
        return () => clearTimeout(timer)
      }
    }, [isStreaming, isOpen, defaultOpen, setIsOpen, hasAutoClosedRef])

    const handleOpenChange = (open: boolean) => {
      setIsOpen(open)
    }

    return (
      <ReasoningContext.Provider
        value={{ isStreaming, isOpen, setIsOpen, duration }}
      >
        <div
          className={cn(
            'not-prose mb-2 block w-full max-w-[720px]',
            variant === 'default' &&
              'rounded-lg border border-gray-200 bg-gray-50 p-3 shadow-sm dark:border-gray-800 dark:bg-gray-900/20',
            variant === 'subtle' && 'p-0 border-0 bg-transparent shadow-none'
          )}
        >
          <Collapsible
            className={cn('', className)}
            onOpenChange={handleOpenChange}
            open={isOpen}
            {...props}
          >
            {children}
          </Collapsible>
        </div>
      </ReasoningContext.Provider>
    )
  }
)

export type ReasoningTriggerProps = ComponentProps<
  typeof CollapsibleTrigger
> & {
  title?: string
}

export const ReasoningTrigger = memo(
  ({
    className,
    title = FALLBACK_REASONING_TITLE,
    children,
    ...props
  }: ReasoningTriggerProps) => {
    const { isStreaming, isOpen } = useReasoning()
    const label = isStreaming ? 'Analyzing…' : title

    return (
      <CollapsibleTrigger
        className={cn(
          'flex w-full items-center gap-2 rounded-md px-2 py-1 text-xs font-medium text-gray-600 transition-colors hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-gray-800/30',
          isStreaming && 'animate-pulse',
          className
        )}
        {...props}
      >
        <ChevronDownIcon
          className={cn(
            'size-3.5 transition-transform duration-200',
            isOpen ? 'rotate-180' : 'rotate-0'
          )}
        />
        <BrainIcon
          className={cn('h-3 w-3 text-gray-500', isStreaming && 'animate-spin')}
        />
        <span className="font-medium">{label}</span>
      </CollapsibleTrigger>
    )
  }
)

export type ReasoningContentProps = ComponentProps<
  typeof CollapsibleContent
> & {
  children: string
}

export const ReasoningContent = memo(
  ({ className, children, ...props }: ReasoningContentProps) => (
    <CollapsibleContent
      className={cn(
        'mt-2 text-xs',
        'text-popover-foreground outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:slide-out-to-top-1 data-[state=open]:slide-in-from-top-1',
        className
      )}
      {...props}
    >
      <div className="rounded-md border border-blue-100/70 bg-white p-2 text-slate-700 dark:border-blue-800/50 dark:bg-blue-950/30 dark:text-slate-300">
        <Response className="grid gap-2 text-xs leading-relaxed">
          {children}
        </Response>
      </div>
    </CollapsibleContent>
  )
)

Reasoning.displayName = "'Reasoning'"
ReasoningTrigger.displayName = "'ReasoningTrigger'"
ReasoningContent.displayName = "'ReasoningContent'"
