'use client'

import { useCallback } from 'react'
import ReactFlow, {
  type Node,
  type Edge,
  addEdge,
  ConnectionMode,
  useNodesState,
  useEdgesState,
  Background,
  Controls,
} from 'reactflow'
import 'reactflow/dist/style.css'

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'default',
    position: { x: 250, y: 0 },
    data: { label: 'Launch B2B SaaS in Europe' },
    style: {
      background: 'rgb(59, 130, 246)',
      color: 'white',
      border: '1px solid rgb(59, 130, 246)',
      borderRadius: '8px',
      fontSize: '14px',
      fontWeight: '600',
    },
  },
  {
    id: '2',
    position: { x: 50, y: 100 },
    data: { label: 'Market Research' },
    style: {
      background: 'rgb(30, 30, 30)',
      color: 'rgb(245, 245, 245)',
      border: '1px solid rgb(60, 60, 60)',
      borderRadius: '8px',
      fontSize: '12px',
    },
  },
  {
    id: '3',
    position: { x: 250, y: 100 },
    data: { label: 'Product Development' },
    style: {
      background: 'rgb(30, 30, 30)',
      color: 'rgb(245, 245, 245)',
      border: '1px solid rgb(60, 60, 60)',
      borderRadius: '8px',
      fontSize: '12px',
    },
  },
  {
    id: '4',
    position: { x: 450, y: 100 },
    data: { label: 'Legal & Compliance' },
    style: {
      background: 'rgb(30, 30, 30)',
      color: 'rgb(245, 245, 245)',
      border: '1px solid rgb(60, 60, 60)',
      borderRadius: '8px',
      fontSize: '12px',
    },
  },
  {
    id: '5',
    position: { x: 0, y: 200 },
    data: { label: 'Target Segments' },
    style: {
      background: 'rgb(20, 20, 20)',
      color: 'rgb(160, 160, 160)',
      border: '1px solid rgb(40, 40, 40)',
      borderRadius: '6px',
      fontSize: '11px',
    },
  },
  {
    id: '6',
    position: { x: 120, y: 200 },
    data: { label: 'Competitive Analysis' },
    style: {
      background: 'rgb(20, 20, 20)',
      color: 'rgb(160, 160, 160)',
      border: '1px solid rgb(40, 40, 40)',
      borderRadius: '6px',
      fontSize: '11px',
    },
  },
  {
    id: '7',
    position: { x: 200, y: 200 },
    data: { label: 'MVP Features' },
    style: {
      background: 'rgb(20, 20, 20)',
      color: 'rgb(160, 160, 160)',
      border: '1px solid rgb(40, 40, 40)',
      borderRadius: '6px',
      fontSize: '11px',
    },
  },
  {
    id: '8',
    position: { x: 300, y: 200 },
    data: { label: 'Tech Stack' },
    style: {
      background: 'rgb(20, 20, 20)',
      color: 'rgb(160, 160, 160)',
      border: '1px solid rgb(40, 40, 40)',
      borderRadius: '6px',
      fontSize: '11px',
    },
  },
]

const initialEdges: Edge[] = [
  {
    id: 'e1-2',
    source: '1',
    target: '2',
    style: { stroke: 'rgb(60, 60, 60)' },
  },
  {
    id: 'e1-3',
    source: '1',
    target: '3',
    style: { stroke: 'rgb(60, 60, 60)' },
  },
  {
    id: 'e1-4',
    source: '1',
    target: '4',
    style: { stroke: 'rgb(60, 60, 60)' },
  },
  {
    id: 'e2-5',
    source: '2',
    target: '5',
    style: { stroke: 'rgb(40, 40, 40)' },
  },
  {
    id: 'e2-6',
    source: '2',
    target: '6',
    style: { stroke: 'rgb(40, 40, 40)' },
  },
  {
    id: 'e3-7',
    source: '3',
    target: '7',
    style: { stroke: 'rgb(40, 40, 40)' },
  },
  {
    id: 'e3-8',
    source: '3',
    target: '8',
    style: { stroke: 'rgb(40, 40, 40)' },
  },
]

export default function IssueTreeVisualization() {
  const [nodes, _setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)

  const onConnect = useCallback(
    (params: any) => setEdges(eds => addEdge(params, eds)),
    [setEdges]
  )

  return (
    <div className="bg-slate-100/30 rounded-2xl p-8 border border-slate-200 dark:bg-slate-800/30 dark:border-slate-800">
      <div className="h-80 bg-white rounded-lg border border-slate-200 overflow-hidden dark:bg-slate-950 dark:border-slate-800">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          connectionMode={ConnectionMode.Loose}
          fitView
          attributionPosition="bottom-left"
        >
          <Background color="rgb(40, 40, 40)" />
          <Controls />
        </ReactFlow>
      </div>
      <p className="text-slate-500 text-center mt-4 text-sm dark:text-slate-400">
        Interactive issue tree - scroll and zoom to explore
      </p>
    </div>
  )
}
