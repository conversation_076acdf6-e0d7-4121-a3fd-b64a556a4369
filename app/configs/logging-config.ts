/**
 * Centralized Logging Configuration
 *
 * This file contains all event names, descriptive labels, logging service flags,
 * event categorization and metadata for the logging system.
 *
 * This is the single source of truth for all logging-related settings.
 */

// ============================================================================
// LOGGING SERVICE FLAGS
// ============================================================================

export type LoggingFlags = {
  logPosthog: boolean
  logMemory: boolean // for future database logging
}

export const LOGGING_FLAGS: LoggingFlags = {
  logPosthog: true,
  logMemory: false, // Not implemented yet
}

// ============================================================================
// DEBOUNCING CONFIGURATION
// ============================================================================

/**
 * Debouncing configuration for event logging
 * Prevents duplicate events from being logged when triggered multiple times rapidly
 */
export type DebounceConfig = {
  enabled: boolean
  defaultIntervalMs: number
  includeParametersInKey: boolean // Whether to include event parameters in debounce key
  maxDebounceEntries: number // Memory management - max concurrent debounce entries
  cleanupIntervalMs: number // How often to clean up expired debounce entries
}

export const DEBOUNCE_CONFIG: DebounceConfig = {
  enabled: true,
  defaultIntervalMs: 2000, // 2 seconds default debounce
  includeParametersInKey: false, // By default, only debounce by event name
  maxDebounceEntries: 1000, // Prevent memory leaks
  cleanupIntervalMs: 30000, // Clean up every 30 seconds
}

/**
 * Per-event debounce interval overrides
 * Events not listed here will use the default interval
 */
export const EVENT_DEBOUNCE_INTERVALS: Partial<Record<EventName, number>> = {
  // High-frequency UI events - shorter debounce
  click_reactflow_hierarchical: 1000, // 1 second for layout toggles
  click_reactflow_circular: 1000,
  click_force_graph: 1000,
  click_force_graph_3d: 1000,
  edit_outline_title: 3000, // 3 seconds for text editing

  // Form submissions and critical actions - longer debounce
  submit_feedback_widget: 5000, // 5 seconds for form submissions
  start_subscription_checkout: 10000, // 10 seconds for payment flows

  // Research and AI operations - moderate debounce
  click_outline_quickResearch: 2000,
  click_reactflow_quickResearch: 2000,
  click_aipane_startGenerate: 3000,
  click_aipane_startChat: 3000,
  click_dragtree_batchResearch: 5000, // Longer for batch operations
  // New DragTree outline UI interactions
  click_outline_batchResearch: 2000,
  click_outline_assetSidebar: 1000,
  click_outline_filter: 1000,
  click_tab_quickResearchReasoningSteps: 1500,
  click_copy_quickResearch: 1500,
  click_copy_quickResearch_failure: 1500,
  click_copy_quickResearch_empty: 1500,
  // Subscription success events are rare; default debounce is fine
}

// ============================================================================
// PROVIDER CONFIGURATION
// ============================================================================

/**
 * Provider Configuration
 *
 * To swap analytics providers, simply change the ACTIVE_PROVIDER setting
 * and update the corresponding service implementation in app/libs/logging.ts
 *
 * Supported providers:
 * - 'posthog': PostHog Analytics (default)
 * - 'amplitude': Amplitude Analytics
 * - 'mixpanel': Mixpanel Analytics
 * - 'custom': Custom analytics implementation
 */
export type AnalyticsProvider = 'posthog' | 'amplitude' | 'mixpanel' | 'custom'

export const ACTIVE_PROVIDER: AnalyticsProvider = 'posthog'

/**
 * Provider-specific configuration
 * Add new providers here as needed
 */
export const PROVIDER_CONFIG = {
  posthog: {
    name: 'PostHog',
    requiresApiKey: true,
    envVar: 'NEXT_PUBLIC_POSTHOG_KEY',
  },
  amplitude: {
    name: 'Amplitude',
    requiresApiKey: true,
    envVar: 'NEXT_PUBLIC_AMPLITUDE_API_KEY',
  },
  mixpanel: {
    name: 'Mixpanel',
    requiresApiKey: true,
    envVar: 'NEXT_PUBLIC_MIXPANEL_PROJECT_TOKEN',
  },
  custom: {
    name: 'Custom Analytics',
    requiresApiKey: false,
    envVar: null,
  },
} as const

// ============================================================================
// EVENT CATEGORIES
// ============================================================================

export const EVENT_CATEGORIES = {
  SCREENING: 'screening',
  DRAGTREE: 'dragtree',
  AI_PANE: 'aipane',
  SETTINGS: 'settings',
  NAVIGATION: 'navigation',
  SUBSCRIPTION: 'subscription',
  FEEDBACK: 'feedback',
} as const

export type EventCategory =
  (typeof EVENT_CATEGORIES)[keyof typeof EVENT_CATEGORIES]

// ============================================================================
// EVENT DEFINITIONS
// ============================================================================

export type EventName =
  // Screening Module Events
  | 'click_screening_paraphraseAndAnalyze'
  | 'click_screening_startClarification'

  // DragTree Module Events
  | 'click_outline_quickResearch'
  | 'click_reactflow_quickResearch'
  | 'click_reactflow_hierarchical'
  | 'click_reactflow_circular'
  | 'click_force_graph'
  | 'click_force_graph_3d'
  | 'edit_outline_title'
  | 'click_dragtree_batchResearch'
  | 'click_outline_batchResearch'
  | 'click_outline_assetSidebar'
  | 'click_outline_filter'
  | 'click_tab_quickResearchReasoningSteps'
  | 'click_copy_quickResearch'
  | 'click_copy_quickResearch_failure'
  | 'click_copy_quickResearch_empty'
  | 'click_settings_copyOriginal'
  | 'click_settings_copyMarkdown'

  // AI Pane Module Events
  | 'click_aipane_startGenerate'
  | 'click_aipane_copyGenerated'
  | 'click_aipane_startChat'
  | 'click_aipane_sendMessage'

  // Subscription Module Events
  | 'click_upgrade_sidebar'
  | 'click_manage_subscription_profile'
  | 'click_subscribe_profile'
  | 'click_refresh_account_profile'
  | 'start_subscription_checkout'
  | 'success_subscription_checkout'
  | 'error_subscription_checkout'
  | 'click_subscription_manage'

  // Unified Upgrade System Events
  | 'click_upgrade_button'
  | 'click_upgrade_hint'
  | 'click_upgrade_banner'
  | 'dismiss_upgrade_banner'

  // Feedback Module Events
  | 'click_feedback_widget_opened'
  | 'submit_feedback_widget'

// ============================================================================
// EVENT METADATA
// ============================================================================

export type EventMetadata = {
  name: EventName
  label: string
  description: string
  category: EventCategory
  requiresUserId: boolean
  requiresDragTreeId: boolean
  optionalProperties?: string[]
}

export const EVENT_DEFINITIONS: Record<EventName, EventMetadata> = {
  // Screening Module
  click_screening_paraphraseAndAnalyze: {
    name: 'click_screening_paraphraseAndAnalyze',
    label: 'Screening: Paraphrase & Analyze Click',
    description: 'User clicked on paragraphs in screening module',
    category: EVENT_CATEGORIES.SCREENING,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['description_length', 'selected_language'],
  },

  click_screening_startClarification: {
    name: 'click_screening_startClarification',
    label: 'Screening: Start Clarification Click',
    description: 'User clicked "start clarify" button',
    category: EVENT_CATEGORIES.SCREENING,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['description_length', 'selected_suggestion'],
  },

  // DragTree Module
  click_outline_quickResearch: {
    name: 'click_outline_quickResearch',
    label: 'Outline: Quick Research Click',
    description: 'User clicked quick research in outline view',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['question_id', 'question_text'],
  },

  // Outline UI: Open Batch Research dialog (distinct from starting batch processing)
  click_outline_batchResearch: {
    name: 'click_outline_batchResearch',
    label: 'Outline: Open Batch Research Dialog',
    description:
      'User clicked to open the Batch Quick Research dialog in outline',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: [],
  },

  // Outline UI: Open Asset Sidebar
  click_outline_assetSidebar: {
    name: 'click_outline_assetSidebar',
    label: 'Outline: Open Asset Sidebar',
    description: 'User clicked to open the asset sidebar from outline header',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['unviewed_count'],
  },

  // Outline UI: Filter selection
  click_outline_filter: {
    name: 'click_outline_filter',
    label: 'Outline: Filter Selected',
    description: 'User selected a filter option in the outline header dropdown',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['filter'],
  },

  // Quick Research Tab: Reasoning Steps button
  click_tab_quickResearchReasoningSteps: {
    name: 'click_tab_quickResearchReasoningSteps',
    label: 'Quick Research: Open Reasoning Steps',
    description:
      'User opened the reasoning steps dialog from quick research tab',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['node_id', 'content_id', 'steps_count'],
  },

  click_copy_quickResearch: {
    name: 'click_copy_quickResearch',
    label: 'Quick Research: Copy Content',
    description:
      'User copied quick research content from the header copy control',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: [
      'node_id',
      'content_id',
      'copy_origin',
      'copied_length',
    ],
  },

  click_copy_quickResearch_failure: {
    name: 'click_copy_quickResearch_failure',
    label: 'Quick Research: Copy Failure',
    description: 'Copy to clipboard failed from the quick research header',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: [
      'node_id',
      'content_id',
      'copy_origin',
      'error_message',
    ],
  },

  click_copy_quickResearch_empty: {
    name: 'click_copy_quickResearch_empty',
    label: 'Quick Research: Copy Attempt With No Content',
    description:
      'User attempted to copy quick research before content was available',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['node_id', 'content_id'],
  },

  click_reactflow_quickResearch: {
    name: 'click_reactflow_quickResearch',
    label: 'React Flow: Quick Research Click',
    description: 'User clicked quick research in React Flow view',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['question_id', 'question_text'],
  },

  click_reactflow_hierarchical: {
    name: 'click_reactflow_hierarchical',
    label: 'React Flow: Hierarchical Plot Click',
    description: 'User clicked hierarchical plot in React Flow view',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  click_reactflow_circular: {
    name: 'click_reactflow_circular',
    label: 'React Flow: Circular Plot Click',
    description: 'User clicked circular plot in React Flow view',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  click_force_graph: {
    name: 'click_force_graph',
    label: 'Force Graph: View Click',
    description: 'User switched to Force Graph visualization',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
  },
  click_force_graph_3d: {
    name: 'click_force_graph_3d',
    label: 'Force Graph 3D: View Click',
    description: 'User switched to Force Graph 3D visualization',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  edit_outline_title: {
    name: 'edit_outline_title',
    label: 'Outline: Title Edit',
    description: 'User successfully edited dragTree node title',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['question_id', 'original_text', 'final_text'],
  },

  click_dragtree_batchResearch: {
    name: 'click_dragtree_batchResearch',
    label: 'DragTree: Batch Research Click',
    description: 'User batch selected questions and clicked "start research"',
    category: EVENT_CATEGORIES.DRAGTREE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['question_ids', 'question_texts', 'batch_count'],
  },

  click_settings_copyOriginal: {
    name: 'click_settings_copyOriginal',
    label: 'Settings: Copy Original Click',
    description: 'User copied original question in settings',
    category: EVENT_CATEGORIES.SETTINGS,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  click_settings_copyMarkdown: {
    name: 'click_settings_copyMarkdown',
    label: 'Settings: Copy Markdown Click',
    description: 'User copied markdown in settings',
    category: EVENT_CATEGORIES.SETTINGS,
    requiresUserId: true,
    requiresDragTreeId: true,
  },

  // AI Pane Module
  click_aipane_startGenerate: {
    name: 'click_aipane_startGenerate',
    label: 'AI Pane: Start Generate Click',
    description: 'User clicked "start generate" in AI pane generate tab',
    category: EVENT_CATEGORIES.AI_PANE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['prompt_length', 'context_count'],
  },

  click_aipane_copyGenerated: {
    name: 'click_aipane_copyGenerated',
    label: 'AI Pane: Copy Generated Click',
    description: 'User clicked copy button in AI pane generate tab',
    category: EVENT_CATEGORIES.AI_PANE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['content_length', 'generation_state', 'asset_id'],
  },

  click_aipane_startChat: {
    name: 'click_aipane_startChat',
    label: 'AI Pane: Start Chat Click',
    description: 'User clicked "start chat" in AI pane chat tab',
    category: EVENT_CATEGORIES.AI_PANE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['initial_message_length', 'context_count'],
  },

  click_aipane_sendMessage: {
    name: 'click_aipane_sendMessage',
    label: 'AI Pane: Send Message Click',
    description: 'User sent a message in AI pane chat',
    category: EVENT_CATEGORIES.AI_PANE,
    requiresUserId: true,
    requiresDragTreeId: true,
    optionalProperties: ['message_text', 'message_length'],
  },

  // Subscription Module
  click_upgrade_sidebar: {
    name: 'click_upgrade_sidebar',
    label: 'Subscription: Upgrade Click (Sidebar)',
    description: 'User clicked upgrade button in sidebar',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['source', 'current_tier'],
  },

  click_manage_subscription_profile: {
    name: 'click_manage_subscription_profile',
    label: 'Subscription: Manage Subscription Click (Profile)',
    description: 'User clicked manage subscription in profile dialog',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['current_tier', 'customer_id'],
  },

  click_subscribe_profile: {
    name: 'click_subscribe_profile',
    label: 'Subscription: Subscribe Click (Profile)',
    description: 'User clicked subscribe button in profile dialog',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['current_tier'],
  },

  click_refresh_account_profile: {
    name: 'click_refresh_account_profile',
    label: 'Subscription: Refresh Account Profile Click',
    description: 'User clicked refresh account button in profile dialog',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['current_tier'],
  },

  start_subscription_checkout: {
    name: 'start_subscription_checkout',
    label: 'Subscription: Checkout Started',
    description: 'User started subscription checkout process',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['plan', 'billing_cycle'],
  },

  success_subscription_checkout: {
    name: 'success_subscription_checkout',
    label: 'Subscription: Checkout Success',
    description: 'Subscription checkout completed successfully',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['session_id', 'refresh_success'],
  },

  error_subscription_checkout: {
    name: 'error_subscription_checkout',
    label: 'Subscription: Checkout Error',
    description: 'Error occurred during subscription checkout',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['plan', 'billing_cycle', 'error'],
  },

  click_subscription_manage: {
    name: 'click_subscription_manage',
    label: 'Subscription: Manage Subscription Click',
    description: 'User clicked manage subscription button',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['current_tier'],
  },

  // Unified Upgrade System Events
  click_upgrade_button: {
    name: 'click_upgrade_button',
    label: 'Upgrade: Button Click',
    description: 'User clicked unified upgrade button component',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['context', 'current_tier', 'button_text'],
  },

  click_upgrade_hint: {
    name: 'click_upgrade_hint',
    label: 'Upgrade: Hint Click',
    description: 'User clicked upgrade hint component',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['context', 'current_tier', 'message'],
  },

  click_upgrade_banner: {
    name: 'click_upgrade_banner',
    label: 'Upgrade: Banner Click',
    description: 'User clicked upgrade banner component',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['context', 'current_tier', 'title', 'message'],
  },

  dismiss_upgrade_banner: {
    name: 'dismiss_upgrade_banner',
    label: 'Upgrade: Banner Dismissed',
    description: 'User dismissed upgrade banner component',
    category: EVENT_CATEGORIES.SUBSCRIPTION,
    requiresUserId: true,
    requiresDragTreeId: false,
    optionalProperties: ['context', 'current_tier', 'title'],
  },

  // Feedback Module
  click_feedback_widget_opened: {
    name: 'click_feedback_widget_opened',
    label: 'Feedback: Widget Opened',
    description: 'User opened the feedback widget modal',
    category: EVENT_CATEGORIES.FEEDBACK,
    requiresUserId: false,
    requiresDragTreeId: true,
    optionalProperties: [],
  },
  submit_feedback_widget: {
    name: 'submit_feedback_widget',
    label: 'Feedback: Widget Submitted',
    description: 'User submitted feedback through the feedback widget',
    category: EVENT_CATEGORIES.FEEDBACK,
    requiresUserId: false,
    requiresDragTreeId: true,
    optionalProperties: [
      'feedbackType',
      'engagementLevel',
      'hasRatings',
      'feedbackLength',
      'feedbackRecordId',
    ],
  },
}

// ============================================================================
// VALIDATION HELPERS
// ============================================================================

export function isValidEventName(eventName: string): eventName is EventName {
  return eventName in EVENT_DEFINITIONS
}

export function getEventMetadata(eventName: EventName): EventMetadata {
  return EVENT_DEFINITIONS[eventName]
}

export function getEventsByCategory(category: EventCategory): EventMetadata[] {
  return Object.values(EVENT_DEFINITIONS).filter(
    event => event.category === category
  )
}

// ============================================================================
// TYPE EXPORTS
// ============================================================================

// Types are already exported above, no need to re-export
