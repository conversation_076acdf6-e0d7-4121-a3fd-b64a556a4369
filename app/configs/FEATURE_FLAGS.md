# Feature Flags System

## Overview

The feature flags system allows for controlled rollout of new features and A/B testing. Feature flags are implemented as compile-time constants (not environment variables) for better performance and type safety.

## Chat System

The chat system has been consolidated into a single implementation. Versioned
flags (v1/v2) were removed. Use `getChatApiEndpoint()` to reference the canonical
endpoint for the chat feature.

## Usage

```typescript
import { getChatApiEndpoint } from '@/app/configs/feature-flags'

const api = getChatApiEndpoint() // '/api/aipane/chat'
```

## Architecture Improvements in Chat v2

### Message Flow

1. Frontend sends only new user message + conversation ID
2. API retrieves conversation history internally from database
3. API reconstructs full context server-side
4. Context included only on first message or when changed
5. System messages hidden from UI display

### API Request Format

```typescript
// First message (includes context)
POST /api/aipane/chat
{
  "message": "What's the weather in San Francisco?",
  "conversationId": "thread_abc123",
  "context": "User is asking about weather. Provide current conditions.",
  "model": "gpt-4.1"
}

// Subsequent messages (no context needed)
POST /api/aipane/chat
{
  "message": "What about tomorrow's forecast?",
  "conversationId": "thread_abc123",
  "model": "gpt-4.1"
}
```

### Benefits

- **Reduced Network Traffic**: Only new messages sent, not entire conversation
- **Better Performance**: Shorter request payloads for long conversations
- **Improved Separation**: Frontend focuses on UI, backend handles conversation logic
- **Enhanced Security**: Sensitive context handling server-side
- **Scalability**: More efficient for high-volume usage

## Testing

Feature flags are validated at startup and tested in the test suite.

## Implementation Details

The chat UI uses `ChatTabContent` directly.

### Files Modified

- `app/configs/feature-flags.ts` - Chat endpoint constant and helpers
- `lib/asset-types/chat.ts` - Asset registry referencing consolidated component

### Testing

All tests pass with both v1 and v2 systems. The build system validates TypeScript compatibility and the test suite ensures functionality across both implementations.
