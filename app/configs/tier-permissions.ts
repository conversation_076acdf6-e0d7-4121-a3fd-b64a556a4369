import { SubscriptionTier } from '@prisma/client'

// -----------------------------
// Strongly-typed tier access config (acts like Python ABC)
// -----------------------------

// Resource limits for numerical constraints (e.g., max items, storage limits)
export type ResourceLimits = {
  maxDragTrees: number
  aiPaneGeneratePerTree: number
  aiPaneChatPerTree: number
  maxBatchResearchItems: number
  quickResearchPerTree: number
}

// Access permissions using consistent naming: can[Create|Edit|Delete][Entity]
export type AccessPermissions = {
  // DragTree permissions
  canEditDragTreeTitle: boolean
  canCreateDragTree: boolean

  // DragTree Node permissions
  canEditDragTreeNodeTitle: boolean
  canDeleteDragTreeNode: boolean
  canCreateDragTreeNode: boolean

  // AI Chat permissions
  canCreateAiChat: boolean
  canCreateAiMessage: boolean
  canDeleteAiChat: boolean

  // AI Generation permissions
  canCreateAiGeneration: boolean
  canEditAiGenerationTitle: boolean
  canEditAiGenerationContent: boolean
  canDeleteAiGeneration: boolean

  // Research permissions
  canCreateQuickResearch: boolean
  /** Whether user can edit quick research content (rich-text/markdown). */
  canEditQuickResearch: boolean

  // Export permissions
  canCreateDiagramExport: boolean

  // Asset permissions
  canDeleteAsset: boolean

  // User preferences permissions
  canEditLanguagePreference: boolean
}

// Legacy type for backward compatibility
export type CollaboratorPermissions = AccessPermissions

export type TierAccessConfig = {
  resourceLimits: ResourceLimits
  accessPermissions: AccessPermissions
}

const HUGE_LIMIT = 10000

// Single source of truth for tier capabilities
export const TierAccess: Record<SubscriptionTier, TierAccessConfig> = {
  // VIEWER: read-only access with no capabilities – convenient for eyeballing
  // This config intentionally disables all features and sets tiny limits.
  [SubscriptionTier.VIEWER]: {
    resourceLimits: {
      maxDragTrees: 0,
      aiPaneGeneratePerTree: 0,
      aiPaneChatPerTree: 0,
      maxBatchResearchItems: 0,
      quickResearchPerTree: 0,
    },
    accessPermissions: {
      canEditDragTreeTitle: false,
      canCreateDragTree: false,
      canEditDragTreeNodeTitle: false,
      canDeleteDragTreeNode: false,
      canCreateDragTreeNode: false,
      canCreateAiChat: false,
      canCreateAiMessage: false,
      canDeleteAiChat: false,
      canCreateAiGeneration: false,
      canEditAiGenerationTitle: false,
      canEditAiGenerationContent: false,
      canDeleteAiGeneration: false,
      canCreateQuickResearch: false,
      canEditQuickResearch: false,
      canCreateDiagramExport: false,
      canDeleteAsset: false,
      canEditLanguagePreference: false,
    },
  },
  [SubscriptionTier.FREE]: {
    resourceLimits: {
      maxDragTrees: 3,
      aiPaneGeneratePerTree: 3,
      aiPaneChatPerTree: 3,
      maxBatchResearchItems: 3,
      // Maximum number of quick research items allowed per drag tree
      quickResearchPerTree: 15,
    },
    accessPermissions: {
      canEditDragTreeTitle: true,
      canCreateDragTree: true,
      canEditDragTreeNodeTitle: true,
      canDeleteDragTreeNode: true,
      canCreateDragTreeNode: true,
      canCreateAiChat: true,
      canCreateAiMessage: true,
      canDeleteAiChat: true,
      canCreateAiGeneration: true,
      canEditAiGenerationTitle: true,
      canEditAiGenerationContent: true,
      canDeleteAiGeneration: true,
      canCreateQuickResearch: true,
      canEditQuickResearch: true,
      canCreateDiagramExport: false,
      canDeleteAsset: true,
      canEditLanguagePreference: true,
    },
  },
  // DUMMY: temporary tier for testing – restricted permissions for testing
  [SubscriptionTier.DUMMY]: {
    resourceLimits: {
      maxDragTrees: 3,
      aiPaneGeneratePerTree: 3,
      aiPaneChatPerTree: 3,
      maxBatchResearchItems: 5,
      quickResearchPerTree: 20,
    },
    accessPermissions: {
      canEditDragTreeTitle: false,
      canCreateDragTree: false,
      canEditDragTreeNodeTitle: false,
      canDeleteDragTreeNode: false,
      canCreateDragTreeNode: false,
      canCreateAiChat: false,
      canCreateAiMessage: false,
      canDeleteAiChat: false,
      canCreateAiGeneration: false,
      canEditAiGenerationTitle: false,
      canEditAiGenerationContent: false,
      canDeleteAiGeneration: false,
      canCreateQuickResearch: true,
      canEditQuickResearch: false,
      canCreateDiagramExport: false,
      canDeleteAsset: false,
      canEditLanguagePreference: false,
    },
  },
  [SubscriptionTier.PRO]: {
    resourceLimits: {
      maxDragTrees: 100,
      aiPaneGeneratePerTree: 100,
      aiPaneChatPerTree: 100,
      maxBatchResearchItems: 10,
      quickResearchPerTree: 999,
    },
    accessPermissions: {
      canEditDragTreeTitle: true,
      canCreateDragTree: true,
      canEditDragTreeNodeTitle: true,
      canDeleteDragTreeNode: true,
      canCreateDragTreeNode: true,
      canCreateAiChat: true,
      canCreateAiMessage: true,
      canDeleteAiChat: true,
      canCreateAiGeneration: true,
      canEditAiGenerationTitle: true,
      canEditAiGenerationContent: true,
      canDeleteAiGeneration: true,
      canCreateQuickResearch: true,
      canEditQuickResearch: true,
      canCreateDiagramExport: true,
      canDeleteAsset: true,
      canEditLanguagePreference: true,
    },
  },
  [SubscriptionTier.GUEST]: {
    resourceLimits: {
      maxDragTrees: 10,
      aiPaneGeneratePerTree: 100,
      aiPaneChatPerTree: 100,
      maxBatchResearchItems: HUGE_LIMIT,
      quickResearchPerTree: 999,
    },
    accessPermissions: {
      canEditDragTreeTitle: true,
      canCreateDragTree: true,
      canEditDragTreeNodeTitle: true,
      canDeleteDragTreeNode: true,
      canCreateDragTreeNode: true,
      canCreateAiChat: true,
      canCreateAiMessage: true,
      canDeleteAiChat: true,
      canCreateAiGeneration: true,
      canEditAiGenerationTitle: true,
      canEditAiGenerationContent: true,
      canDeleteAiGeneration: true,
      canCreateQuickResearch: true,
      canEditQuickResearch: true,
      canCreateDiagramExport: true,
      canDeleteAsset: true,
      canEditLanguagePreference: true,
    },
  },
  [SubscriptionTier.ULTRA]: {
    resourceLimits: {
      maxDragTrees: 200,
      aiPaneGeneratePerTree: HUGE_LIMIT,
      aiPaneChatPerTree: HUGE_LIMIT,
      maxBatchResearchItems: HUGE_LIMIT,
      quickResearchPerTree: 999,
    },
    accessPermissions: {
      canEditDragTreeTitle: true,
      canCreateDragTree: true,
      canEditDragTreeNodeTitle: true,
      canDeleteDragTreeNode: true,
      canCreateDragTreeNode: true,
      canCreateAiChat: true,
      canCreateAiMessage: true,
      canDeleteAiChat: true,
      canCreateAiGeneration: true,
      canEditAiGenerationTitle: true,
      canEditAiGenerationContent: true,
      canDeleteAiGeneration: true,
      canCreateQuickResearch: true,
      canEditQuickResearch: true,
      canCreateDiagramExport: true,
      canDeleteAsset: true,
      canEditLanguagePreference: true,
    },
  },
  [SubscriptionTier.BUSINESS]: {
    resourceLimits: {
      maxDragTrees: 1000,
      aiPaneGeneratePerTree: HUGE_LIMIT,
      aiPaneChatPerTree: HUGE_LIMIT,
      maxBatchResearchItems: HUGE_LIMIT,
      quickResearchPerTree: 999,
    },
    accessPermissions: {
      canEditDragTreeTitle: true,
      canCreateDragTree: true,
      canEditDragTreeNodeTitle: true,
      canDeleteDragTreeNode: true,
      canCreateDragTreeNode: true,
      canCreateAiChat: true,
      canCreateAiMessage: true,
      canDeleteAiChat: true,
      canCreateAiGeneration: true,
      canEditAiGenerationTitle: true,
      canEditAiGenerationContent: true,
      canDeleteAiGeneration: true,
      canCreateQuickResearch: true,
      canEditQuickResearch: true,
      canCreateDiagramExport: true,
      canDeleteAsset: true,
      canEditLanguagePreference: true,
    },
  },
} as const

export type TierAccessName = keyof typeof TierAccess
export type TierAccessEntry = (typeof TierAccess)[TierAccessName]

// Legacy type for backward compatibility
export type CombinedTierPermissions = ResourceLimits & {
  quickResearch: boolean
  canEditQuickResearch: boolean
  canExportDiagram: boolean
  canEditLanguagePreference: boolean
}

// Get resource limits for a tier
export const getResourceLimits = (tier: SubscriptionTier): ResourceLimits => {
  return TierAccess[tier].resourceLimits
}

// Get access permissions for a tier
export const getAccessPermissions = (
  tier: SubscriptionTier
): AccessPermissions => {
  return TierAccess[tier].accessPermissions
}

// Legacy function for backward compatibility
export const getTierPermissions = (
  tier: SubscriptionTier
): CombinedTierPermissions => {
  const permissions = TierAccess[tier].accessPermissions
  return {
    ...TierAccess[tier].resourceLimits,
    quickResearch: permissions.canCreateQuickResearch,
    canEditQuickResearch: permissions.canEditQuickResearch,
    canExportDiagram: permissions.canCreateDiagramExport,
    canEditLanguagePreference: permissions.canEditLanguagePreference,
  }
}

export const hasPaidFeatures = (tier: SubscriptionTier): boolean => {
  const paidTiers: SubscriptionTier[] = [
    SubscriptionTier.PRO,
    SubscriptionTier.GUEST,
    SubscriptionTier.ULTRA,
    SubscriptionTier.BUSINESS,
  ]
  return paidTiers.includes(tier)
}

// Check if a tier has a specific feature (legacy compatibility)
export const hasFeature = (
  tier: SubscriptionTier,
  feature: keyof (ResourceLimits & {
    quickResearch: boolean
    canExportDiagram: boolean
  })
): boolean => {
  const permissions = getTierPermissions(tier)
  return Boolean((permissions as any)[feature])
}

// Get a resource limit value for a tier
export const getFeatureLimit = (
  tier: SubscriptionTier,
  feature: keyof ResourceLimits
): number => {
  const limits = getResourceLimits(tier)
  const value = (limits as any)[feature]
  return typeof value === 'number' ? value : 0
}

// Type for access permission actions
export type AccessAction = keyof AccessPermissions
// Legacy type for backward compatibility
export type CollaboratorAction = AccessAction

// Check if a user can perform a specific action
export function canPerformAction(
  tier: SubscriptionTier,
  action: AccessAction,
  isOwner: boolean
): boolean {
  if (isOwner) return true
  return TierAccess[tier].accessPermissions[action]
}

// Helper function to check specific permissions
export function hasPermission(
  tier: SubscriptionTier,
  permission: keyof AccessPermissions
): boolean {
  return TierAccess[tier].accessPermissions[permission]
}

export function getBatchResearchLimit(tier: SubscriptionTier): number {
  return TierAccess[tier].resourceLimits.maxBatchResearchItems
}

export function getQuickResearchLimit(tier: SubscriptionTier): number {
  return TierAccess[tier].resourceLimits.quickResearchPerTree
}
