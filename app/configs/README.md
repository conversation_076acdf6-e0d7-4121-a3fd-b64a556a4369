# Configuration System

This directory contains application configuration files organized by domain.

Note: The canonical path is `app/configs/` (plural). Older references to `app/config/` in docs or code comments should be read as `app/configs/`.

## Configuration Files

### LLM Model Configuration (`llm-models.ts`)

Centralized configuration system for LLM models based on user subscription tiers.

### Tier Permissions (`tier-permissions.ts`)

Access permissions and resource limits for different subscription tiers.

### Feature Flags (`features.ts`)

Feature flag configuration for enabling/disabling functionality.

### Logging Configuration (`logging.ts`)

Centralized logging configuration with PostHog integration.

### General Configuration (`index.ts`)

General application configuration and constants.

## Overview

The LLM model configuration system provides:

- **Centralized model management**: All model configurations are defined in one place
- **Subscription-based model assignment**: Different models for different subscription tiers
- **Type safety**: Full TypeScript support with compile-time validation
- **Easy extensibility**: Simple to add new tiers, routes, or model providers
- **Caching**: User subscription tier caching to reduce database queries

## Architecture

### Core Files

- `app/configs/llm-models.ts` - Model configuration definitions and types
- `app/libs/model-config.ts` - Utility functions for retrieving model configs, session-aware helpers, and small tier cache

### Configuration Structure

```typescript
type ModelConfig = {
  model: string
  model_provider: 'azure' | 'openai' | 'anthropic' | 'google'
  model_provider_api?: 'azure_standard' | 'azure_eastus2'
  temperature?: number
  maxOutputTokens?: number
  throttle?: number // streaming throttle hint (ms)
  rateLimitWindowMs?: number // per user+route window
  rateLimitMaxRequests?: number // allowed within window
  maxPromptChars?: number // hard input clamp
}

type SubscriptionModelConfig = {
  [K in SubscriptionTier]: {
    [K in APIRoute]: ModelConfig
  }
}
```

## Current Configuration

### Subscription Tiers

- **FREE**: Cost-effective models for basic functionality
  - Uses `gpt-4.1-mini` for all routes
  - Lower token limits (2000)

- **GUEST**: Premium models for enhanced experience
  - Uses `gpt-4.1` for all routes
  - Higher token limits (4000)

### Supported API Routes

- `dragtree_generate_questions` - DragTree question generation
- `dragtree_research_generate_v2` - DragTree research with OpenAI native web search (single + two-step)
- `dragtree_research_generate` - **Retired** Brave-based research endpoint (kept in history for reference)
- `dragtree_generate_similar_questions` - Generate similar questions for DragTree nodes
- `dragtree_generate_similar_categories` - Generate similar categories for DragTree nodes
- `screening_diagnose` - Problem analysis and screening
- `screening_rephrase` - Problem statement rephrasing
- `aipane_generate` - AI Pane content generation
- `aipane_chat` - AI Pane chat conversations
- `title_generation` - Automatic conversation title generation (uses gpt-4.1-mini for all tiers)

## Usage

### In API Routes

```typescript
import {
  getModelConfigFromSession,
  getModelFromConfig,
  getProviderNameForUsage,
} from '@/app/libs/model-config'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import { AIUsageType } from '@prisma/client'

export async function POST(req: NextRequest) {
  const session = await auth()
  if (!session?.user?.id) return new Response('Unauthorized', { status: 401 })

  // 1-2) Resolve tier from session (no DB) and get route config
  const modelConfig = await getModelConfigFromSession(
    session,
    'dragtree_generate_questions'
  )
  // 3) Provider-agnostic model instance
  const model = getModelFromConfig(modelConfig)

  const result = streamText({
    model,
    temperature: modelConfig.temperature,
    maxOutputTokens: modelConfig.maxOutputTokens,
    onFinish: async r => {
      await createAIUsage({
        userId: session.user.id,
        entityType: 'drag_tree',
        entityId: dragTreeId,
        aiProvider: getProviderNameForUsage(modelConfig),
        modelName: modelConfig.model,
        usageType: AIUsageType.GENERATE_QUESTION,
      })
    },
  })
  return result.toTextStreamResponse()
}
```

### Utility Functions

```typescript
// DB-based (uses 5-min in-memory cache under the hood)
await getUserSubscriptionTier(userId)
await getModelConfig(userId, 'screening_diagnose')

// Session-based (no DB when session has subscription_tier)
await getModelConfigFromSession(session, 'aipane_chat')

// Direct tier path (no DB)
getModelConfigForTier(SubscriptionTier.GUEST, 'screening_rephrase')

// Model instance + usage mapping
const model = getModelFromConfig(config)
const providerName = getProviderNameForUsage(config)

// Input clamp constant
DEFAULT_MAX_PROMPT_CHARS // 1,000,000 by default

// Tier cache controls
clearUserTierCache(userId) // Clear one
clearUserTierCache() // Clear all
getTierCacheStats() // { size, oldestEntryAgeMs, ttlMs }
```

## Environment Variables

The model provider system requires specific environment variables to be configured for each AI provider:

### Azure OpenAI (Required)

**Azure Standard Instance:**

- `AZURE_RESOURCE_NAME` - Azure OpenAI resource name
- `AZURE_API_KEY` - Azure OpenAI API key

**Azure East US 2 Instance (for GPT-5 models):**

- `AZURE_EASTUS2_RESOURCE_NAME` - Azure OpenAI East US 2 resource name
- `AZURE_EASTUS2_API_KEY` - Azure OpenAI East US 2 API key

### OpenAI (Optional)

- `OPENAI_API_KEY` - OpenAI API key (only needed if using OpenAI models)

### Environment Validation

The system includes comprehensive environment variable validation:

- **Runtime Checks**: Missing variables will throw descriptive errors
- **Unit Tests**: Automated tests verify environment variable requirements
- **Error Messages**: Clear instructions for missing configurations

```bash
# Run environment validation tests
npm test app/configs/__tests__/llm-models.test.ts
```

## Adding New Features

### Adding a New API Route

1. Add the route to the `APIRoute` type in `llm-models.ts`:

```typescript
export type APIRoute =
  | 'dragtree_generate_questions'
  | 'dragtree_research_generate_v2'
  | 'screening_diagnose'
  | 'screening_rephrase'
  | 'aipane_generate'
  | 'aipane_chat'
  | 'new_route_name' // Add here
```

2. Add configurations for all subscription tiers:

```typescript
export const LLM_MODEL_CONFIG: SubscriptionModelConfig = {
  FREE: {
    // ... existing configs
    new_route_name: {
      model: 'gpt-4.1-mini',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 2000,
    },
  },
  GUEST: {
    // ... existing configs
    new_route_name: {
      model: 'gpt-4.1',
      model_provider: 'azure',
      temperature: 0.7,
      maxTokens: 4000,
    },
  },
}
```

3. Update the validation in `validateModelConfig()` function

### Adding a New Subscription Tier

1. Add the tier to the Prisma schema enum
2. Add configuration in `LLM_MODEL_CONFIG`
3. Update the validation function

### Adding a New Model Provider

1. Add to the `ModelProvider` type
2. Update API routes to handle the new provider
3. Add provider-specific configuration options to `ModelConfig`

## Caching Strategy

- Provider instances (Azure/OpenAI) are cached in-process (module-level) for reuse.
- User subscription tiers are cached in-memory for 5 minutes to reduce DB calls.
- Prefer `getModelConfigFromSession()` to avoid DB completely when the session contains `subscription_tier`.
- Manual cache clearing helpers are available; consider clearing cache after admin-driven tier changes.

## Error Handling

- Clear runtime errors for missing provider environment variables.
- Rate limiting returns 429 with `Retry-After` header.
- Title generation requires an authenticated user and a conversation; no anonymous fallback.

## Migration Notes

This system replaces hardcoded model assignments in API routes. The migration:

1. ✅ Maintains backward compatibility
2. ✅ Preserves existing functionality
3. ✅ Adds subscription-based differentiation
4. ✅ Improves maintainability and type safety

## Complete API Coverage

All LLM-using APIs are centrally managed (9 routes):

1. `dragtree_generate_questions`
2. `dragtree_research_generate_v2`
3. `dragtree_generate_similar_questions`
4. `dragtree_generate_similar_categories`
5. `screening_diagnose`
6. `screening_rephrase`
7. `aipane_generate`
8. `aipane_chat`
9. `title_generation`

## Build-Time Validation

The system includes tests that ensure:

- All subscription tiers have complete configurations for all routes
- Provider API invariants (e.g., GPT‑5 → `azure_eastus2` only)
- Valid provider API names for Azure models

Tests are located at `app/configs/__tests__/llm-models.test.ts` and will fail the build on violations.

## Future Enhancements

- Support for multiple model providers per route
- Dynamic model selection based on load/availability
- A/B testing framework for model performance
- Usage analytics and cost optimization
- Admin interface for configuration management
