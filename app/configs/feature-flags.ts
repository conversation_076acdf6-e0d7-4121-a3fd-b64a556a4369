/**
 * Feature Flags Configuration
 *
 * This file contains boolean flags to control feature rollouts and A/B testing.
 * These are compile-time constants, not environment variables.
 */

// =============================================================================
// CHAT SYSTEM (Consolidated)
// =============================================================================
/**
 * Consolidated chat system configuration.
 * The project previously supported multiple chat implementations ("v1"/"v2").
 * This has been consolidated into a single, stable implementation.
 * Use `getChatApiEndpoint` for the canonical API path.
 */
export const CHAT_API_ENDPOINT: string = '/api/aipane/chat'

// =============================================================================
// AI PANE FLAGS
// =============================================================================
// Note: Versioned flags for the chat system were removed as part of consolidation.

// =============================================================================
// RESEARCH SYSTEM FLAGS
// =============================================================================

/**
 * Enable Two-Stage Research System
 *
 * When true: Uses two-stage research generation (template assessment + custom flow)
 * When false: Uses existing single-template research approach
 *
 * Two-Stage Research Benefits:
 * - Template assessment analyzes research question suitability
 * - Custom flow generation for specialized research scenarios
 * - Better research quality for diverse question types
 * - Maintains backward compatibility with existing template
 */
export const ENABLE_TWO_STAGE_RESEARCH = true

/**
 * Brave search flow retired (2024-06) so research now always routes through
 * the OpenAI implementation. `ENABLE_TWO_STAGE_RESEARCH` is the only flag left
 * to toggle between one-step and two-step orchestration.
 */

// =============================================================================
// DEVELOPMENT FLAGS
// =============================================================================

/**
 * Enable Debug Logging for Chat Systems
 *
 * When true: Enables detailed console logging for chat operations
 * When false: Uses production logging levels
 */
export const ENABLE_CHAT_DEBUG_LOGGING = true // Temporarily enabled for debugging

/**
 * Enable Performance Monitoring
 *
 * When true: Tracks and logs performance metrics for chat operations
 * When false: Skips performance tracking
 */
export const ENABLE_CHAT_PERFORMANCE_MONITORING = true

// =============================================================================
// FEATURE FLAG UTILITIES
// =============================================================================

// =============================================================================
// DRAGTREE VISUAL FLOW FLAGS
// =============================================================================
/**
 * Enable ReactFlow Issue Tree (hierarchical + radial) diagram modes
 * This controls whether the ReactFlow-based Issue Tree views are available.
 * Default: enabled (requested as default primary view alongside 3D graph).
 */
export const ENABLE_DRAGTREE_ISSUE_TREE: boolean = true

/**
 * Enable the radial (circular) layout inside Issue Tree modes
 * When false, users can only use hierarchical (linear) layout.
 * Default: enabled.
 */
export const ENABLE_DRAGTREE_ISSUE_TREE_RADIAL: boolean = true

/**
 * Enable 2D force graph view
 * Default per request: do not show 2D force graph.
 */
export const ENABLE_DRAGTREE_FORCE_GRAPH_2D: boolean = false

/**
 * Enable 3D force graph view
 * Default per request: show 3D force graph.
 */
export const ENABLE_DRAGTREE_FORCE_GRAPH_3D: boolean = true

/**
 * Enable node selection mode in Issue Tree (shows selection UI and logic)
 * When false, the "Select to Use" UI and related selection logic are hidden.
 */
export const ENABLE_DRAGTREE_NODE_SELECTION: boolean = false

/**
 * Get the API endpoint for the chat system (consolidated).
 */
export const getChatApiEndpoint = (): string => CHAT_API_ENDPOINT

/**
 * Get feature flag status for debugging
 */
export const getFeatureFlagStatus = () => {
  return {
    twoStageResearch: ENABLE_TWO_STAGE_RESEARCH,
    debugLogging: ENABLE_CHAT_DEBUG_LOGGING,
    performanceMonitoring: ENABLE_CHAT_PERFORMANCE_MONITORING,
    currentApiEndpoint: getChatApiEndpoint(),
  }
}

// =============================================================================
// FEATURE FLAG VALIDATION
// =============================================================================

/**
 * Validate feature flag configuration at startup
 * This helps catch configuration issues early
 */
export const validateFeatureFlags = (): {
  valid: boolean
  errors: string[]
} => {
  const errors: string[] = []

  return {
    valid: errors.length === 0,
    errors,
  }
}

// Log feature flag status in development
if (ENABLE_CHAT_DEBUG_LOGGING) {
  console.log(
    '🚩 [Feature Flags] Current configuration:',
    getFeatureFlagStatus()
  )

  const validation = validateFeatureFlags()
  if (!validation.valid) {
    console.error('❌ [Feature Flags] Configuration errors:', validation.errors)
  } else {
    console.log('✅ [Feature Flags] Configuration is valid')
  }
}
