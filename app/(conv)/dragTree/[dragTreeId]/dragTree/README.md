## Visual Flow Feature Flags

These boolean flags control which diagram modes are available.

Defined in `app/configs/feature-flags.ts`:

- `ENABLE_DRAGTREE_ISSUE_TREE`: controls ReactFlow Issue Tree views (`linear`/`radial`). Default: `true`.
- `ENABLE_DRAGTREE_ISSUE_TREE_RADIAL`: controls radial layout within Issue Tree. Default: `true`.
- `ENABLE_DRAGTREE_FORCE_GRAPH_2D`: controls 2D force graph. Default: `false`.
- `ENABLE_DRAGTREE_FORCE_GRAPH_3D`: controls 3D force graph. Default: `true`.

Usage:

- `LayoutModeToggle` hides buttons for disabled modes.
- `DiagramView` enforces flags at render-time and falls back in order: Issue Tree → 3D → 2D.
- `useVisualFlowSetup` selects a default layout respecting the flags (prefers Issue Tree, then 3D, then 2D).
