// 'use client'

// import { create } from 'zustand'

// export type FilterMode = 'none' | 'researchable' | 'researched' | 'unread'

// export type FilterState = {
//   mode: FilterMode
//   setMode: (mode: FilterMode) => void
//   clear: () => void
// }

// export const useFilterStore = create<FilterState>(set => ({
//   mode: 'none',
//   setMode: (mode: FilterMode) => set({ mode }),
//   clear: () => set({ mode: 'none' }),
// }))
