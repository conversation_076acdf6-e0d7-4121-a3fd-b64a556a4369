// import { create } from 'zustand'
// import { subscribeWithSelector } from 'zustand/middleware'
// import { isValidAssetType } from '@/lib/asset-types'
// import { TabPrefixUtils } from '@/app/(conv)/dragTree/[dragTreeId]/constants/tab-prefixes'
// import type { Asset } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'

// // Core tab types (non-extensible)
// export type CoreTabType = 'main' | 'research'

// // Extensible tab type that includes core types + any registered asset types
// export type TabType = CoreTabType | string

// // Type guards for core tab types
// export const isCoreTabType = (type: string): type is CoreTabType => {
//   return type === 'main' || type === 'research'
// }

// export type GenerationPhase = 'idle' | 'generating' | 'completed' | 'failed'

// export interface Tab {
//   id: string
//   title: string
//   fullTitle: string
//   type: TabType
//   nodeId?: string
//   contentId?: string
//   isClosable: boolean
//   // AI pane specific data
//   aiPaneData?: {
//     type: string // Extensible asset type
//     model: string
//     prompt: string
//     contextIds: string[]
//     settings: Record<string, any>
//     language?: string // Language override for this generation
//     /**
//      * For chat tabs, the persisted conversation ID in ai_conversations table.
//      * Allows resuming conversations across sessions with correct pagination.
//      */
//     conversationId?: string
//     // Asset-specific fields
//     assetContent?: string
//     assetId?: string
//     /**
//      * For chat assets, a list of messages to populate the chat UI.
//      */
//     assetMessages?: { role: 'user' | 'assistant'; content: string }[]
//     /**
//      * Generation phase tracking for robust state management.
//      * Replaces simple boolean flag with explicit state machine.
//      * Maps to DB AIGenerationStatus: idle→INITIALIZED, generating→GENERATING,
//      * completed→ACTIVE, failed→INACTIVE
//      */
//     generationPhase?: GenerationPhase
//     /**
//      * Immediate flag to prevent duplicate generation starts (race condition protection)
//      */
//     generationStarted?: boolean
//   }
// }

// /**
//  * The lightweight representation of a tab that is persisted to localStorage.
//  * Excludes heavy data like asset content or chat messages.
//  */
// type StoredTab = {
//   id: string
//   title: string
//   fullTitle: string
//   type: TabType
//   nodeId?: string
//   contentId?: string
//   isClosable: boolean
//   // Lightweight AI data for restoration. Heavy assets are fetched on-demand.
//   aiPaneData?: {
//     type: string // Extensible asset type
//     model: string
//     prompt: string
//     contextIds: string[]
//     settings: Record<string, any>
//     language?: string // Language override for this generation
//     conversationId?: string
//     assetId?: string
//     generationPhase?: GenerationPhase
//   }
// }

// interface TabState {
//   tabs: Tab[]
//   activeTabId: string
//   dragTreeId: string | null

//   // Actions
//   addTab: (tab: Omit<Tab, 'id'>) => string
//   removeTab: (tabId: string) => void
//   setActiveTab: (tabId: string) => void
//   updateTabTitle: (tabId: string, title: string) => void
//   reorderTabs: (oldIndex: number, newIndex: number) => void
//   getActiveTab: () => Tab | undefined
//   getTabByNodeId: (nodeId: string) => Tab | undefined
//   initializeMainTab: () => void
//   resetTabs: () => void

//   // AI pane specific actions
//   addAiTab: (aiData: {
//     type: string // Extensible asset type
//     model: string
//     prompt: string
//     contextIds: string[]
//     settings: Record<string, any>
//     language?: string // Language override for this generation
//     conversationId?: string // For chat tabs, pre-generated conversation ID
//   }) => string

//   // Asset specific actions
//   addAssetTab: (asset: {
//     id: string
//     title: string
//     content: string
//     type: string // Extensible asset type
//     model: string
//     prompt: string
//     contextIds: string[]
//     messages?: { role: 'user' | 'assistant'; content: string }[]
//   }) => string

//   /**
//    * Merge partial updates into the aiPaneData for a specific tab.
//    * Useful to store additional metadata (e.g. assetId) after initial creation
//    * without triggering duplicate tabs.
//    */
//   updateTabAiPaneData: (
//     tabId: string,
//     updates: Partial<Tab['aiPaneData']>
//   ) => void

//   /**
//    * Atomically tries to start generation for a tab. Returns true if generation
//    * was started successfully, false if already started.
//    */
//   tryStartGeneration: (tabId: string) => boolean

//   // Persistence actions
//   setDragTreeId: (dragTreeId: string) => void
//   loadTabsFromLocalStorage: (dragTreeId: string) => void
//   flushTabsToLocalStorage: () => void

//   // Type validation actions
//   isValidTabType: (type: string) => boolean
//   validateAssetType: (type: string) => boolean

//   // Tab validation actions
//   validateAndCleanupTabs: (availableAssets: Asset[]) => void
// }

// const getStorageKey = (dragTreeId: string) => `clarifyai:tabs:${dragTreeId}`

// export const useTabStore = create<TabState>()(
//   subscribeWithSelector((set, get) => ({
//     // Initialize with a default, non-closable "Main" tab so the tab bar is always visible
//     tabs: [
//       {
//         id: 'main',
//         title: 'Main',
//         fullTitle: 'Main',
//         type: 'main',
//         isClosable: false,
//       },
//     ],
//     activeTabId: 'main',
//     dragTreeId: null,

//     addTab: tabData => {
//       const state = get()

//       // Check if tab with same nodeId already exists
//       if (tabData.nodeId) {
//         const existingTab = state.tabs.find(
//           tab => tab.nodeId === tabData.nodeId
//         )
//         if (existingTab) {
//           // Switch to existing tab instead of creating duplicate
//           set({ activeTabId: existingTab.id })
//           return existingTab.id
//         }
//       }

//       const newTab: Tab = {
//         ...tabData,
//         id: `${tabData.nodeId}-${tabData.contentId || 'tab'}`,
//       }

//       set(state => ({
//         tabs: [...state.tabs, newTab],
//         activeTabId: newTab.id,
//       }))

//       return newTab.id
//     },

//     removeTab: tabId => {
//       const state = get()
//       const tabIndex = state.tabs.findIndex(tab => tab.id === tabId)

//       if (tabIndex === -1) return

//       const tab = state.tabs[tabIndex]
//       if (!tab.isClosable) return // Don't allow closing main tab

//       const newTabs = state.tabs.filter(tab => tab.id !== tabId)
//       let newActiveTabId = state.activeTabId

//       // If we're removing the active tab, switch to another tab
//       if (state.activeTabId === tabId) {
//         if (newTabs.length > 0) {
//           // Try to activate the previous tab, or the first tab if this was the first
//           const newActiveIndex = Math.max(0, tabIndex - 1)
//           newActiveTabId = newTabs[newActiveIndex]?.id || newTabs[0].id
//         } else {
//           newActiveTabId = ''
//         }
//       }

//       set({
//         tabs: newTabs,
//         activeTabId: newActiveTabId,
//       })
//     },

//     setActiveTab: tabId => {
//       const state = get()
//       const tabExists = state.tabs.some(tab => tab.id === tabId)

//       if (tabExists) {
//         set({ activeTabId: tabId })
//       }
//     },

//     updateTabTitle: (tabId, title) => {
//       set(state => ({
//         tabs: state.tabs.map(tab =>
//           tab.id === tabId ? { ...tab, title } : tab
//         ),
//       }))
//     },

//     reorderTabs: (oldIndex, newIndex) => {
//       set(state => {
//         // Ensure indices valid and not moving main (index 0)
//         if (oldIndex === 0 || newIndex === 0) return state
//         const tabsCopy = [...state.tabs]
//         const [moved] = tabsCopy.splice(oldIndex, 1)
//         tabsCopy.splice(newIndex, 0, moved)
//         return { tabs: tabsCopy }
//       })
//     },

//     getActiveTab: () => {
//       const state = get()
//       return state.tabs.find(tab => tab.id === state.activeTabId)
//     },

//     getTabByNodeId: nodeId => {
//       const state = get()
//       return state.tabs.find(tab => tab.nodeId === nodeId)
//     },

//     initializeMainTab: () => {
//       const state = get()
//       const mainTabExists = state.tabs.some(tab => tab.type === 'main')

//       if (!mainTabExists) {
//         const mainTab: Tab = {
//           id: 'main',
//           title: 'Main',
//           fullTitle: 'Main',
//           type: 'main',
//           isClosable: false,
//         }

//         set({
//           tabs: [mainTab],
//           activeTabId: mainTab.id,
//         })
//       }
//     },

//     resetTabs: () => {
//       console.log('🧹 [TabStore] Resetting tabs to main tab only')
//       set({
//         tabs: [
//           {
//             id: 'main',
//             title: 'Main',
//             fullTitle: 'Main',
//             type: 'main',
//             isClosable: false,
//           },
//         ],
//         activeTabId: 'main',
//       })
//     },

//     addAiTab: aiData => {
//       // Validate asset type
//       if (!get().validateAssetType(aiData.type)) {
//         throw new Error(
//           `Cannot create AI tab: Invalid asset type '${aiData.type}'`
//         )
//       }

//       const timestamp = new Date()
//         .toISOString()
//         .replace(/T/, '_')
//         .replace(/:/g, '')
//         .replace(/\..+/, '')
//         .slice(0, 15) // Format: 20251111_123456

//       const tabId = TabPrefixUtils.createTempTabId(aiData.type)

//       const newTab: Tab = {
//         id: tabId,
//         title: timestamp,
//         fullTitle: `${aiData.type === 'generate' ? 'Generate' : 'Chat'} - ${timestamp}`,
//         type: aiData.type,
//         isClosable: true,
//         aiPaneData: {
//           ...aiData,
//           // Include conversationId if provided (for chat tabs)
//           ...(aiData.conversationId && {
//             conversationId: aiData.conversationId,
//           }),
//         },
//       }

//       set(state => ({
//         tabs: [...state.tabs, newTab],
//         activeTabId: newTab.id,
//       }))

//       return newTab.id
//     },

//     addAssetTab: asset => {
//       // Validate asset type
//       if (!get().validateAssetType(asset.type)) {
//         throw new Error(
//           `Cannot create asset tab: Invalid asset type '${asset.type}'`
//         )
//       }

//       const tabId = `asset-${asset.type}-${asset.id}`

//       // Check if a tab for this asset already exists either by exact ID (asset-* prefix)
//       // OR by having the same assetId in its aiPaneData (e.g. the original AI tab
//       // that created the asset). This prevents opening duplicate tabs for the same
//       // chat/generation session.
//       const state = get()
//       const existingTab = state.tabs.find(
//         tab => tab.id === tabId || tab.aiPaneData?.assetId === asset.id
//       )

//       if (existingTab) {
//         // Simply switch focus to the existing tab
//         set({ activeTabId: existingTab.id })
//         return existingTab.id
//       }

//       const newTab: Tab = {
//         id: tabId,
//         title: asset.title,
//         fullTitle: `${asset.type === 'generate' ? 'Generate' : 'Chat'} - ${asset.title}`,
//         type: asset.type,
//         isClosable: true,
//         aiPaneData: {
//           type: asset.type,
//           model: asset.model,
//           prompt: asset.prompt,
//           contextIds: asset.contextIds,
//           settings: {},
//           // If this is a chat asset, wire up the existing conversation ID so the
//           // chat tab loads historical messages instead of initializing a new thread.
//           ...(asset.type === 'chat' && { conversationId: asset.id }),
//           // Add asset data for immediate display
//           assetContent: asset.content,
//           assetMessages: asset.messages,
//           assetId: asset.id,
//           // For generate assets mark as completed to skip streaming UI
//           ...(asset.type === 'generate' && { generationPhase: 'completed' }),
//         },
//       }

//       set(state => ({
//         tabs: [...state.tabs, newTab],
//         activeTabId: newTab.id,
//       }))

//       return newTab.id
//     },

//     updateTabAiPaneData: (tabId, updates) => {
//       if (!updates) return

//       set(state => ({
//         tabs: state.tabs.map(tab => {
//           if (tab.id !== tabId) return tab

//           // If this update includes assetId and tab has temp prefix, transform to permanent ID
//           const shouldTransformId =
//             updates.assetId &&
//             TabPrefixUtils.isTempTab(tab.id) &&
//             tab.aiPaneData?.type

//           const newTabId = shouldTransformId
//             ? TabPrefixUtils.createAssetTabId(
//                 tab.aiPaneData!.type,
//                 updates.assetId!
//               )
//             : tab.id

//           // Create new Tab object with updated aiPaneData and potentially new ID
//           return {
//             ...tab,
//             id: newTabId,
//             aiPaneData: {
//               ...(tab.aiPaneData ?? {}),
//               ...updates,
//             } as Tab['aiPaneData'],
//           }
//         }),
//       }))

//       // If we transformed the ID, update activeTabId if it was the active tab
//       const state = get()
//       if (updates.assetId && state.activeTabId === tabId) {
//         const updatedTab = state.tabs.find(
//           t => t.aiPaneData?.assetId === updates.assetId
//         )
//         if (updatedTab && updatedTab.id !== tabId) {
//           set({ activeTabId: updatedTab.id })
//         }
//       }
//     },

//     tryStartGeneration: tabId => {
//       const state = get()
//       const tab = state.tabs.find(t => t.id === tabId)

//       // Check if tab exists and has aiPaneData
//       if (!tab?.aiPaneData) return false

//       // Check if generation is already started or in progress
//       if (
//         tab.aiPaneData.generationStarted ||
//         tab.aiPaneData.generationPhase === 'generating'
//       ) {
//         return false
//       }

//       // Atomically set the generation started flag and phase
//       set(state => ({
//         tabs: state.tabs.map(t => {
//           if (t.id !== tabId || !t.aiPaneData) return t
//           return {
//             ...t,
//             aiPaneData: {
//               ...t.aiPaneData,
//               generationStarted: true,
//               generationPhase: 'generating' as GenerationPhase,
//             },
//           }
//         }),
//       }))

//       return true
//     },

//     setDragTreeId: dragTreeId => {
//       set({ dragTreeId })
//     },

//     // --- LocalStorage Persistence ---

//     loadTabsFromLocalStorage: dragTreeId => {
//       // Prevent loading if we already have the correct state
//       if (get().dragTreeId === dragTreeId) return

//       console.log(
//         `📖 [TabStore] Loading tabs from localStorage for tree: ${dragTreeId}`
//       )
//       try {
//         const storageKey = getStorageKey(dragTreeId)
//         const raw = localStorage.getItem(storageKey)
//         if (raw) {
//           const storedState = JSON.parse(raw)
//           if (storedState.tabs && storedState.activeTabId) {
//             set({
//               tabs: storedState.tabs,
//               activeTabId: storedState.activeTabId,
//               dragTreeId,
//             })
//             console.log(
//               '✅ [TabStore] Tabs loaded successfully from localStorage'
//             )
//             return
//           }
//         }
//         // If no data found, reset to a clean state for the new tree
//         console.log(
//           '📝 [TabStore] No saved tabs found, initializing clean state.'
//         )
//         set({
//           tabs: [
//             {
//               id: 'main',
//               title: 'Main',
//               fullTitle: 'Main',
//               type: 'main',
//               isClosable: false,
//             },
//           ],
//           activeTabId: 'main',
//           dragTreeId,
//         })
//       } catch (error) {
//         console.error(
//           '💥 [TabStore] Failed to load tabs from localStorage:',
//           error
//         )
//         // Fallback to a clean state on error
//         get().resetTabs()
//         set({ dragTreeId })
//       }
//     },

//     flushTabsToLocalStorage: () => {
//       const { dragTreeId, tabs, activeTabId } = get()
//       if (!dragTreeId) return

//       try {
//         // Serialize only lightweight tab data
//         const tabsToStore: StoredTab[] = tabs.map(tab => ({
//           id: tab.id,
//           title: tab.title,
//           fullTitle: tab.fullTitle,
//           type: tab.type,
//           nodeId: tab.nodeId,
//           contentId: tab.contentId,
//           isClosable: tab.isClosable,
//           aiPaneData: tab.aiPaneData
//             ? {
//                 type: tab.aiPaneData.type,
//                 model: tab.aiPaneData.model,
//                 prompt: tab.aiPaneData.prompt,
//                 contextIds: tab.aiPaneData.contextIds,
//                 settings: tab.aiPaneData.settings,
//                 language: tab.aiPaneData.language,
//                 conversationId: tab.aiPaneData.conversationId,
//                 assetId: tab.aiPaneData.assetId,
//                 generationPhase: tab.aiPaneData.generationPhase,
//               }
//             : undefined,
//         }))

//         const payload = {
//           tabs: tabsToStore,
//           activeTabId,
//         }

//         const storageKey = getStorageKey(dragTreeId)
//         localStorage.setItem(storageKey, JSON.stringify(payload))
//       } catch (error) {
//         console.error(
//           '💥 [TabStore] Failed to save tabs to localStorage:',
//           error
//         )
//       }
//     },

//     // Type validation functions
//     isValidTabType: (type: string) => {
//       return isCoreTabType(type) || isValidAssetType(type)
//     },

//     validateAssetType: (type: string) => {
//       if (!isValidAssetType(type)) {
//         console.error(
//           `Invalid asset type: ${type}. Must be registered in asset type registry.`
//         )
//         return false
//       }
//       return true
//     },

//     validateAndCleanupTabs: (availableAssets: Asset[]) => {
//       const state = get()
//       const validTabs: Tab[] = []
//       const removedTabs: Tab[] = []

//       // Create a map of available assets for quick lookup
//       const assetMap = new Map(availableAssets.map(asset => [asset.id, asset]))

//       for (const tab of state.tabs) {
//         // Always keep core tabs (main, research)
//         if (isCoreTabType(tab.type)) {
//           validTabs.push(tab)
//           continue
//         }

//         // For asset-based tabs, validate against available assets
//         if (tab.aiPaneData?.assetId) {
//           const asset = assetMap.get(tab.aiPaneData.assetId)

//           if (asset) {
//             // Asset exists - check if it has content OR is persisted in DB (content will be loaded lazily)
//             const hasValidContent = asset.content && asset.content.trim() !== ''
//             const isPersistedInDb = asset.persistedInDb === true

//             if (hasValidContent || isPersistedInDb) {
//               // Asset exists and either has content or is persisted in DB - keep the tab
//               validTabs.push(tab)
//             } else {
//               // Asset exists but has no content and is not persisted - remove the tab
//               removedTabs.push(tab)
//               console.warn(
//                 `🧹 [TabStore] Removing invalid tab "${tab.title}" - asset ${tab.aiPaneData.assetId} has no content and is not persisted`
//               )
//             }
//           } else {
//             // Asset missing entirely - remove the tab
//             removedTabs.push(tab)
//             console.warn(
//               `🧹 [TabStore] Removing invalid tab "${tab.title}" - asset ${tab.aiPaneData.assetId} not found`
//             )
//           }
//         } else if (isValidAssetType(tab.type)) {
//           // Asset-type tab without assetId - check if it's a temp tab or broken
//           const isTempTab = TabPrefixUtils.isTempTab(tab.id)
//           const hasPrompt = tab.aiPaneData?.prompt
//           const hasAssetId = tab.aiPaneData?.assetId

//           if (isTempTab && hasPrompt) {
//             if (hasAssetId) {
//               // Temp tab with assetId after browser reload = generation completed but tab not transformed
//               // This means the asset exists in DB but tab is stale temp state - remove it
//               removedTabs.push(tab)
//               console.warn(
//                 `🧹 [TabStore] Removing stale temp tab "${tab.title}" - generation completed, asset available in sidebar`
//               )
//             } else {
//               // Temp tab without assetId = fresh temp state waiting for generation, keep it
//               validTabs.push(tab)
//               console.log(
//                 `✅ [TabStore] Keeping fresh temp tab "${tab.title}" - waiting for generation`
//               )
//             }
//           } else {
//             // Asset-type tab without assetId and not a temp tab - likely broken, remove it
//             removedTabs.push(tab)
//             console.warn(
//               `🧹 [TabStore] Removing broken asset tab "${tab.title}" - no assetId reference and not a temp tab`
//             )
//           }
//         } else {
//           // Unknown tab type - keep it for now (might be a future extension)
//           validTabs.push(tab)
//         }
//       }

//       // Update state if any tabs were removed
//       if (removedTabs.length > 0) {
//         console.log(
//           `🧹 [TabStore] Cleaned up ${removedTabs.length} invalid tabs:`,
//           removedTabs.map(t => t.title)
//         )

//         // Ensure we have at least the main tab
//         if (validTabs.length === 0 || !validTabs.some(t => t.type === 'main')) {
//           validTabs.unshift({
//             id: 'main',
//             title: 'Main',
//             fullTitle: 'Main',
//             type: 'main',
//             isClosable: false,
//           })
//         }

//         // Check if active tab was removed
//         const activeTabStillExists = validTabs.some(
//           t => t.id === state.activeTabId
//         )
//         const newActiveTabId = activeTabStillExists
//           ? state.activeTabId
//           : validTabs[0]?.id || 'main'

//         set({
//           tabs: validTabs,
//           activeTabId: newActiveTabId,
//         })

//         console.log(
//           `✅ [TabStore] Tab validation complete - ${validTabs.length} valid tabs remaining`
//         )
//       } else {
//         console.log(`✅ [TabStore] All ${validTabs.length} tabs are valid`)
//       }
//     },
//   }))
// )

// // --- Automatic Persistence ---

// // Subscribe to state changes and flush to localStorage
// useTabStore.subscribe(
//   state => ({ tabs: state.tabs, activeTabId: state.activeTabId }),
//   () => {
//     useTabStore.getState().flushTabsToLocalStorage()
//   }
// )

// // Flush once more on tab close/refresh to catch any pending changes
// if (typeof window !== 'undefined') {
//   window.addEventListener('beforeunload', () => {
//     useTabStore.getState().flushTabsToLocalStorage()
//   })
// }
