'use client'

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { isValidAssetType } from '@/lib/asset-types'
import { TabPrefixUtils } from '@/app/(conv)/dragTree/[dragTreeId]/constants/tab-prefixes'
import type { Asset } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import type { SupportedLanguageCode } from '@/app/constants/languages'
import { useDragTreeStore } from '@/app/stores/dragtree_store/store'

// Constants
export const MAX_TOKENS = 20000

// Re-export types from original stores for backward compatibility
export type CoreTabType = 'main' | 'research'
export type TabType = CoreTabType | string
export type GenerationPhase = 'idle' | 'generating' | 'completed' | 'failed'
export type AiPaneMode = 'overlay' | 'compress' | 'pin'
export type AiPaneType = 'generate' | 'chat'
export type AiModel = string
export type FilterMode = 'none' | 'researchable' | 'researched' | 'unread'
export type PromptTemplate = string
export type LanguageOption = SupportedLanguageCode

// Type guards
export const isCoreTabType = (type: string): type is CoreTabType => {
  return type === 'main' || type === 'research'
}

// Tab types
export type Tab = {
  id: string
  title: string
  fullTitle: string
  type: TabType
  nodeId?: string
  contentId?: string
  isClosable: boolean
  aiPaneData?: {
    type: string
    model: string
    prompt: string
    contextIds: string[]
    settings: Record<string, any>
    language?: string
    conversationId?: string
    assetId?: string
    assetContent?: string
    assetMessages?: { role: 'user' | 'assistant'; content: string }[]
    generationPhase?: GenerationPhase
    generationStarted?: boolean
  }
}

export type ContextItem = {
  id: string
  title: string
  type: 'category' | 'question'
  parentId?: string
  categoryId?: string
  content?: string
  selected: boolean
}

export type AiPaneSettings = {
  type: AiPaneType
  model: AiModel
  context: ContextItem[]
  prompt: string
  promptTemplate?: string
  files: File[]
  images: File[]
  language?: SupportedLanguageCode
}

// Slice types
type TabsSlice = {
  tabs: Tab[]
  activeTabId: string
  dragTreeId: string | null

  // Tab actions
  addTab: (tab: Omit<Tab, 'id'>) => string
  removeTab: (tabId: string) => void
  setActiveTab: (tabId: string) => void
  updateTabTitle: (tabId: string, title: string) => void
  reorderTabs: (oldIndex: number, newIndex: number) => void
  getActiveTab: () => Tab | undefined
  getTabByNodeId: (nodeId: string) => Tab | undefined
  initializeMainTab: () => void
  resetTabs: () => void
  addAiTab: (aiData: NonNullable<Tab['aiPaneData']>) => string
  updateTabAiPaneData: (
    tabId: string,
    updates: Partial<Tab['aiPaneData']>
  ) => void
  tryStartGeneration: (tabId: string) => boolean

  // Persistence actions
  setDragTreeId: (dragTreeId: string) => void
  loadTabsFromLocalStorage: (dragTreeId: string) => void
  flushTabsToLocalStorage: () => void

  // Validation actions
  isValidTabType: (type: string) => boolean
  validateAssetType: (type: string) => boolean
  validateAndCleanupTabs: (availableAssets: Asset[]) => void
}

type AiPaneSlice = {
  // Panel state
  isOpen: boolean
  mode: AiPaneMode
  width: number
  minWidth: number
  maxWidth: number

  // Settings
  settings: AiPaneSettings
  contextTokenCount: number
  loadingContextIds: Set<string>
  isLoading: boolean

  // Panel actions
  setIsOpen: (isOpen: boolean) => void
  setMode: (mode: AiPaneMode) => void
  setWidth: (width: number) => void
  setContextTokenCount: (count: number) => void

  // Settings actions
  setType: (type: AiPaneType) => void
  setModel: (model: AiModel) => void
  setContext: (context: ContextItem[]) => void
  toggleContextItem: (id: string) => void
  toggleCategoryWithChildren: (
    categoryId: string,
    childrenIds: string[]
  ) => void
  selectAllContext: () => void
  clearAllContext: () => void
  setPrompt: (prompt: string) => void
  setPromptTemplate: (template?: string) => void
  setLanguage: (language?: SupportedLanguageCode) => void
  addFile: (file: File) => void
  removeFile: (index: number) => void
  addImage: (image: File) => void
  removeImage: (index: number) => void

  // Context loading actions
  setContextLoading: (id: string, loading: boolean) => void
  isContextLoading: (id: string) => boolean
  isAllSelectedContentLoaded: () => boolean

  // Reset actions
  resetSettings: () => void
  resetFiles: () => void
  resetImages: () => void
  resetContext: () => void
}

type FilterSlice = {
  filterMode: FilterMode
  setFilterMode: (mode: FilterMode) => void
  clearFilter: () => void
}

// Combined store type
type WorkspaceLayoutStore = TabsSlice &
  AiPaneSlice &
  FilterSlice & {
    // Cross-slice actions
    closeAiPane: (switchToMainTab?: boolean) => void
    removeTabWithCleanup: (tabId: string) => void
    applyFilterAndFocus: (tabId: string) => void
    startAiGeneration: (aiData: {
      type: AiPaneType
      model: string
      prompt: string
      contextIds: string[]
      language?: string
      settings: Record<string, any>
      conversationId?: string
    }) => string
  }

// Default settings
const defaultAiPaneSettings: AiPaneSettings = {
  type: 'generate',
  model: '',
  context: [],
  prompt: '',
  files: [],
  images: [],
}

// Storage key helpers
const getStorageKey = (dragTreeId: string) => `clarifyai:tabs:${dragTreeId}`
const getChatFirstUserKey = (conversationId: string) =>
  `chat:firstUser:${conversationId}`

// SessionStorage utilities for chat handoff
export const sessionStorageUtils = {
  setChatFirstMessage: (conversationId: string, message: string) => {
    try {
      sessionStorage.setItem(getChatFirstUserKey(conversationId), message)
    } catch (error) {
      console.warn(
        '[WorkspaceLayoutStore] Failed to set chat first message:',
        error
      )
    }
  },

  getChatFirstMessage: (conversationId: string): string | null => {
    try {
      return sessionStorage.getItem(getChatFirstUserKey(conversationId))
    } catch (error) {
      console.warn(
        '[WorkspaceLayoutStore] Failed to get chat first message:',
        error
      )
      return null
    }
  },

  clearChatFirstMessage: (conversationId: string) => {
    try {
      sessionStorage.removeItem(getChatFirstUserKey(conversationId))
    } catch (error) {
      console.warn(
        '[WorkspaceLayoutStore] Failed to clear chat first message:',
        error
      )
    }
  },
}

// Create the consolidated store
export const useWorkspaceLayoutStore = create<WorkspaceLayoutStore>()(
  subscribeWithSelector((set, get) => ({
    // === TABS SLICE ===
    tabs: [
      {
        id: 'main',
        title: 'Main',
        fullTitle: 'Main',
        type: 'main',
        isClosable: false,
      },
    ],
    activeTabId: 'main',
    dragTreeId: null,

    // === AI PANE SLICE ===
    isOpen: false,
    mode: 'compress',
    width: 400,
    minWidth: 300,
    maxWidth: 800,
    contextTokenCount: 0,
    loadingContextIds: new Set<string>(),
    settings: defaultAiPaneSettings,
    isLoading: false,

    // === FILTER SLICE ===
    filterMode: 'none',

    // === TABS SLICE IMPLEMENTATION ===
    addTab: (tab: Omit<Tab, 'id'>) => {
      const state = get()

      // --- Asset-aware deduplication ---
      if (tab.aiPaneData?.assetId) {
        const expectedAssetTabId = TabPrefixUtils.createAssetTabId(
          tab.type,
          tab.aiPaneData.assetId
        )

        const existingAssetTab = state.tabs.find(candidate => {
          if (candidate.id === expectedAssetTabId) {
            return true
          }
          return (
            candidate.aiPaneData?.assetId !== undefined &&
            candidate.aiPaneData.assetId === tab.aiPaneData?.assetId
          )
        })

        if (existingAssetTab) {
          set({ activeTabId: existingAssetTab.id })
          return existingAssetTab.id
        }
      }

      // --- Node/content deduplication (research tabs, etc.) ---
      const existingTab = state.tabs.find(candidate => {
        if (tab.nodeId && candidate.nodeId === tab.nodeId) {
          if (!tab.contentId) {
            return true
          }
          return candidate.contentId === tab.contentId
        }
        if (tab.contentId && candidate.contentId === tab.contentId) {
          return true
        }
        return false
      })
      if (existingTab) {
        set({ activeTabId: existingTab.id })
        return existingTab.id
      }

      const timestamp = Date.now().toString(36)
      const entropy = Math.random().toString(36).substring(2, 8)

      let baseId: string
      if (tab.aiPaneData?.assetId) {
        baseId = TabPrefixUtils.createAssetTabId(
          tab.type,
          tab.aiPaneData.assetId
        )
      } else if (!isCoreTabType(tab.type) && tab.aiPaneData) {
        // AI tabs that reach this path should still follow temp prefix conventions
        baseId = TabPrefixUtils.createTempTabId(tab.type)
      } else if (isCoreTabType(tab.type)) {
        baseId = tab.type
      } else if (tab.nodeId) {
        baseId = `${tab.nodeId}-${tab.contentId ?? 'tab'}`
      } else if (tab.contentId) {
        baseId = `content-${tab.contentId}`
      } else {
        baseId = `tab-${timestamp}`
      }

      let id = baseId
      if (state.tabs.some(existing => existing.id === id)) {
        id = `${baseId}-${entropy}`
      }

      const newTab: Tab = { ...tab, id }
      set(current => ({
        tabs: [...current.tabs, newTab],
        activeTabId: id,
      }))
      return id
    },

    removeTab: (tabId: string) => {
      set(state => {
        const newTabs = state.tabs.filter(tab => tab.id !== tabId)
        // Ensure we have at least the main tab
        if (newTabs.length === 0 || !newTabs.some(t => t.type === 'main')) {
          newTabs.unshift({
            id: 'main',
            title: 'Main',
            fullTitle: 'Main',
            type: 'main',
            isClosable: false,
          })
        }

        // Update active tab if removed tab was active
        const newActiveTabId =
          state.activeTabId === tabId
            ? newTabs[0]?.id || 'main'
            : state.activeTabId

        return {
          tabs: newTabs,
          activeTabId: newActiveTabId,
        }
      })
    },

    setActiveTab: (tabId: string) => {
      const state = get()
      if (state.tabs.some(tab => tab.id === tabId)) {
        set({ activeTabId: tabId })
      }
    },

    updateTabTitle: (tabId: string, title: string) => {
      set(state => ({
        tabs: state.tabs.map(tab =>
          tab.id === tabId ? { ...tab, title, fullTitle: title } : tab
        ),
      }))
    },

    reorderTabs: (oldIndex: number, newIndex: number) => {
      set(state => {
        const newTabs = [...state.tabs]
        const [removed] = newTabs.splice(oldIndex, 1)
        newTabs.splice(newIndex, 0, removed)
        return { tabs: newTabs }
      })
    },

    getActiveTab: () => {
      const state = get()
      return state.tabs.find(tab => tab.id === state.activeTabId)
    },

    getTabByNodeId: (nodeId: string) => {
      const state = get()
      return state.tabs.find(tab => tab.nodeId === nodeId)
    },

    initializeMainTab: () => {
      set(state => {
        if (!state.tabs.some(tab => tab.type === 'main')) {
          return {
            tabs: [
              {
                id: 'main',
                title: 'Main',
                fullTitle: 'Main',
                type: 'main',
                isClosable: false,
              },
              ...state.tabs,
            ],
            activeTabId: 'main',
          }
        }
        return state
      })
    },

    resetTabs: () => {
      set({
        tabs: [
          {
            id: 'main',
            title: 'Main',
            fullTitle: 'Main',
            type: 'main',
            isClosable: false,
          },
        ],
        activeTabId: 'main',
      })
    },

    addAiTab: (aiData: NonNullable<Tab['aiPaneData']>) => {
      // Validate asset type
      if (!get().validateAssetType(aiData.type)) {
        throw new Error(
          `Cannot create AI tab: Invalid asset type '${aiData.type}'`
        )
      }

      const timestamp = new Date()
        .toISOString()
        .replace(/T/, '_')
        .replace(/:/g, '')
        .replace(/\..+/, '')
        .slice(0, 15) // Format: 20251111_123456

      const tabId = TabPrefixUtils.createTempTabId(aiData.type)

      const newTab: Tab = {
        id: tabId,
        title: timestamp,
        fullTitle: `${aiData.type === 'generate' ? 'Generate' : 'Chat'} - ${timestamp}`,
        type: aiData.type,
        isClosable: true,
        aiPaneData: {
          ...aiData,
          ...(aiData.conversationId && {
            conversationId: aiData.conversationId,
          }),
        },
      }

      set(state => ({
        tabs: [...state.tabs, newTab],
        activeTabId: newTab.id,
      }))

      return newTab.id
    },

    updateTabAiPaneData: (
      tabId: string,
      updates: Partial<Tab['aiPaneData']>
    ) => {
      if (!updates) return

      set(state => ({
        tabs: state.tabs.map(tab => {
          if (tab.id !== tabId) return tab

          // If this update includes assetId and tab has temp prefix, transform to permanent ID
          const shouldTransformId =
            updates.assetId &&
            TabPrefixUtils.isTempTab(tab.id) &&
            tab.aiPaneData?.type

          const newTabId = shouldTransformId
            ? TabPrefixUtils.createAssetTabId(
                tab.aiPaneData!.type,
                updates.assetId!
              )
            : tab.id

          // Create new Tab object with updated aiPaneData and potentially new ID
          return {
            ...tab,
            id: newTabId,
            aiPaneData: {
              ...(tab.aiPaneData ?? {}),
              ...updates,
            } as Tab['aiPaneData'],
          }
        }),
      }))

      // Update activeTabId if it was transformed
      if (updates.assetId && TabPrefixUtils.isTempTab(tabId)) {
        const state = get()
        if (
          state.activeTabId === tabId &&
          state.tabs.find(t => t.aiPaneData?.assetId === updates.assetId)
        ) {
          const newTab = state.tabs.find(
            t => t.aiPaneData?.assetId === updates.assetId
          )
          if (newTab) {
            set({ activeTabId: newTab.id })
          }
        }
      }
    },

    tryStartGeneration: (tabId: string) => {
      const state = get()
      const tab = state.tabs.find(t => t.id === tabId)

      if (!tab?.aiPaneData) return false

      // Check if generation is already started
      if (tab.aiPaneData.generationStarted) return false

      // Atomically set the flag to prevent race conditions
      set(prevState => ({
        tabs: prevState.tabs.map(t =>
          t.id === tabId
            ? {
                ...t,
                aiPaneData: {
                  ...t.aiPaneData!,
                  generationStarted: true,
                  generationPhase: 'generating' as const,
                },
              }
            : t
        ),
      }))

      return true
    },

    // Persistence actions
    setDragTreeId: (dragTreeId: string) => {
      set({ dragTreeId })
    },

    loadTabsFromLocalStorage: (dragTreeId: string) => {
      try {
        const storageKey = getStorageKey(dragTreeId)
        const raw = localStorage.getItem(storageKey)
        if (raw) {
          const storedState = JSON.parse(raw)
          if (storedState.tabs && storedState.activeTabId) {
            set({
              tabs: storedState.tabs,
              activeTabId: storedState.activeTabId,
              dragTreeId,
            })
            console.log(
              '✅ [WorkspaceLayoutStore] Tabs loaded successfully from localStorage'
            )
            return
          }
        }
        // If no data found, reset to a clean state for the new tree
        console.log(
          '📝 [WorkspaceLayoutStore] No saved tabs found, initializing clean state.'
        )
        set({
          tabs: [
            {
              id: 'main',
              title: 'Main',
              fullTitle: 'Main',
              type: 'main',
              isClosable: false,
            },
          ],
          activeTabId: 'main',
          dragTreeId,
        })
      } catch (error) {
        console.error(
          '💥 [WorkspaceLayoutStore] Failed to load tabs from localStorage:',
          error
        )
        // Reset to clean state on error
        get().resetTabs()
        set({ dragTreeId })
      }
    },

    flushTabsToLocalStorage: () => {
      const state = get()
      if (!state.dragTreeId) return

      try {
        const storageKey = getStorageKey(state.dragTreeId)
        const dataToStore = {
          tabs: state.tabs,
          activeTabId: state.activeTabId,
        }
        localStorage.setItem(storageKey, JSON.stringify(dataToStore))
      } catch (error) {
        console.error(
          '💥 [WorkspaceLayoutStore] Failed to flush tabs to localStorage:',
          error
        )
      }
    },

    // Validation actions
    isValidTabType: (type: string) => {
      return isCoreTabType(type) || isValidAssetType(type)
    },

    validateAssetType: (type: string) => {
      return isValidAssetType(type)
    },

    validateAndCleanupTabs: (availableAssets: Asset[]) => {
      const state = get()
      const validTabs: Tab[] = []
      const removedTabs: Tab[] = []

      // Create a map of available assets for quick lookup
      const assetMap = new Map(availableAssets.map(asset => [asset.id, asset]))

      for (const tab of state.tabs) {
        // Always keep core tabs (main, research)
        if (isCoreTabType(tab.type)) {
          validTabs.push(tab)
          continue
        }

        // For asset-based tabs, validate against available assets
        if (tab.aiPaneData?.assetId) {
          const asset = assetMap.get(tab.aiPaneData.assetId)
          if (asset && asset.type === tab.type) {
            // Additional validation: remove tabs with empty content that are not persisted
            if (asset.content.trim() === '' && !asset.persistedInDb) {
              removedTabs.push(tab)
            } else {
              validTabs.push(tab)
            }
          } else {
            removedTabs.push(tab)
          }
        } else {
          // Check if this is a broken asset tab (has asset-like ID but no assetId)
          if (tab.id.startsWith('asset-')) {
            // Asset tabs must have assetId - if missing, it's broken
            removedTabs.push(tab)
          } else {
            // For tabs without assetId, check if they're legitimate new AI tabs
            const hasValidPrompt =
              tab.aiPaneData?.prompt && tab.aiPaneData.prompt.trim() !== ''
            const hasValidContext =
              tab.aiPaneData?.contextIds && tab.aiPaneData.contextIds.length > 0
            const isLegitimateNewTab = hasValidPrompt || hasValidContext

            if (isLegitimateNewTab) {
              // Temp tabs without assetId are kept (active generation)
              validTabs.push(tab)
            } else {
              // Broken tabs without proper prompt/context are removed
              removedTabs.push(tab)
            }
          }
        }
      }

      // Update state if any tabs were removed
      if (removedTabs.length > 0) {
        console.log(
          `🧹 [WorkspaceLayoutStore] Cleaned up ${removedTabs.length} invalid tabs:`,
          removedTabs.map(t => t.title)
        )

        // Ensure we have at least the main tab
        if (validTabs.length === 0 || !validTabs.some(t => t.type === 'main')) {
          validTabs.unshift({
            id: 'main',
            title: 'Main',
            fullTitle: 'Main',
            type: 'main',
            isClosable: false,
          })
        }

        // Check if active tab was removed
        const activeTabStillExists = validTabs.some(
          t => t.id === state.activeTabId
        )
        const newActiveTabId = activeTabStillExists
          ? state.activeTabId
          : validTabs[0]?.id || 'main'

        set({
          tabs: validTabs,
          activeTabId: newActiveTabId,
        })

        console.log(
          `✅ [WorkspaceLayoutStore] Tab validation complete - ${validTabs.length} valid tabs remaining`
        )
      } else {
        console.log(
          `✅ [WorkspaceLayoutStore] All ${validTabs.length} tabs are valid`
        )
      }
    },

    // === AI PANE SLICE IMPLEMENTATION ===
    setIsOpen: (isOpen: boolean) => set({ isOpen }),

    setMode: (mode: AiPaneMode) => set({ mode }),

    setWidth: (width: number) => {
      const { minWidth, maxWidth } = get()
      const clampedWidth = Math.max(minWidth, Math.min(maxWidth, width))
      set({ width: clampedWidth })
    },

    setContextTokenCount: (count: number) => set({ contextTokenCount: count }),

    // Settings actions
    setType: (type: AiPaneType) =>
      set(state => ({
        settings: { ...state.settings, type, prompt: '' },
      })),

    setModel: (model: AiModel) =>
      set(state => ({
        settings: { ...state.settings, model },
      })),

    setContext: (context: ContextItem[]) =>
      set(state => ({
        settings: { ...state.settings, context },
      })),

    toggleContextItem: (id: string) => {
      set(state => ({
        settings: {
          ...state.settings,
          context: state.settings.context.map(item =>
            item.id === id ? { ...item, selected: !item.selected } : item
          ),
        },
      }))
    },

    toggleCategoryWithChildren: (categoryId: string, childrenIds: string[]) => {
      set(state => {
        const category = state.settings.context.find(
          item => item.id === categoryId
        )
        if (!category) return state

        const newSelected = !category.selected
        return {
          settings: {
            ...state.settings,
            context: state.settings.context.map(item => {
              if (item.id === categoryId || childrenIds.includes(item.id)) {
                return { ...item, selected: newSelected }
              }
              return item
            }),
          },
        }
      })
    },

    selectAllContext: () => {
      set(state => ({
        settings: {
          ...state.settings,
          context: state.settings.context.map(item => ({
            ...item,
            selected: true,
          })),
        },
      }))
    },

    clearAllContext: () => {
      set(state => ({
        settings: {
          ...state.settings,
          context: state.settings.context.map(item => ({
            ...item,
            selected: false,
          })),
        },
      }))
    },

    setPrompt: (prompt: string) =>
      set(state => ({
        settings: { ...state.settings, prompt },
      })),

    setPromptTemplate: (template?: string) =>
      set(state => ({
        settings: { ...state.settings, promptTemplate: template },
      })),

    setLanguage: (language?: SupportedLanguageCode) =>
      set(state => ({
        settings: { ...state.settings, language },
      })),

    addFile: (file: File) => {
      set(state => ({
        settings: {
          ...state.settings,
          files: [...state.settings.files, file],
        },
      }))
    },

    removeFile: (index: number) => {
      set(state => ({
        settings: {
          ...state.settings,
          files: state.settings.files.filter((_, i) => i !== index),
        },
      }))
    },

    addImage: (image: File) => {
      set(state => ({
        settings: {
          ...state.settings,
          images: [...state.settings.images, image],
        },
      }))
    },

    removeImage: (index: number) => {
      set(state => ({
        settings: {
          ...state.settings,
          images: state.settings.images.filter((_, i) => i !== index),
        },
      }))
    },

    // Context loading actions
    setContextLoading: (id: string, loading: boolean) => {
      set(state => {
        const newLoadingIds = new Set(state.loadingContextIds)
        if (loading) {
          newLoadingIds.add(id)
        } else {
          newLoadingIds.delete(id)
        }
        return { loadingContextIds: newLoadingIds }
      })
    },

    isContextLoading: (id: string) => {
      const state = get()
      return state.loadingContextIds.has(id)
    },

    isAllSelectedContentLoaded: () => {
      const state = get()
      const selectedIds = state.settings.context
        .filter(item => item.selected)
        .map(item => item.id)

      // Check both loading state and actual content existence
      return selectedIds.every(id => {
        // First check if still loading
        if (state.loadingContextIds.has(id)) {
          return false
        }

        // Then check if content actually exists in drag tree store
        const dragTreeState = useDragTreeStore.getState()
        const nodeContent = dragTreeState.nodeContent.get(id)

        // Content is loaded if we have at least one content item for this node
        return nodeContent && nodeContent.size > 0
      })
    },

    // Reset actions
    resetSettings: () => {
      set({ settings: defaultAiPaneSettings })
    },

    resetFiles: () => {
      set(state => ({
        settings: { ...state.settings, files: [] },
      }))
    },

    resetImages: () => {
      set(state => ({
        settings: { ...state.settings, images: [] },
      }))
    },

    resetContext: () => {
      set(state => ({
        settings: {
          ...state.settings,
          context: state.settings.context.map(item => ({
            ...item,
            selected: false,
          })),
        },
      }))
    },

    // === FILTER SLICE IMPLEMENTATION ===
    setFilterMode: (mode: FilterMode) => set({ filterMode: mode }),
    clearFilter: () => set({ filterMode: 'none' }),

    // === CROSS-SLICE ACTIONS ===

    /**
     * Atomic action to close AI pane and optionally handle tab switching
     * Replaces: setIsOpen(false) + manual tab switching logic
     */
    closeAiPane: (switchToMainTab = true) => {
      const state = get()

      set({ isOpen: false })

      if (switchToMainTab) {
        const activeTab = state.tabs.find(tab => tab.id === state.activeTabId)
        if (activeTab && !isCoreTabType(activeTab.type)) {
          const mainTab = state.tabs.find(tab => tab.type === 'main')
          if (mainTab) {
            set({ activeTabId: mainTab.id })
          }
        }
      }

      set(current => ({
        settings: { ...current.settings, prompt: '' },
      }))
    },

    /**
     * Atomic action to remove tab with AI pane cleanup
     * Replaces: removeTab() + manual pane state management
     */
    removeTabWithCleanup: (tabId: string) => {
      const state = get()
      const tabToRemove = state.tabs.find(tab => tab.id === tabId)

      // Remove the tab first
      get().removeTab(tabId)

      // Get fresh state after removal to check remaining AI tabs
      const freshState = get()
      const remainingAiTabs = freshState.tabs.filter(
        tab => !isCoreTabType(tab.type)
      )

      if (
        remainingAiTabs.length === 0 &&
        tabToRemove &&
        !isCoreTabType(tabToRemove.type)
      ) {
        set({
          isOpen: false,
          settings: defaultAiPaneSettings,
          loadingContextIds: new Set<string>(),
        })
      }
    },

    /**
     * Atomic action to apply filter and focus on specific tab
     * Handles filter mode changes with navigation coordination
     */
    applyFilterAndFocus: (tabId: string) => {
      const state = get()

      // Set the active tab
      get().setActiveTab(tabId)

      // If the tab is a research tab, we might want to set a specific filter mode
      const targetTab = state.tabs.find(tab => tab.id === tabId)
      if (targetTab?.type === 'research') {
        set({ filterMode: 'researched' })
      }

      // Schedule navigation if needed (this would integrate with navigation store)
      // For now, just ensure the tab is active
    },

    /**
     * Atomic action for the complete AI pane start sequence
     * Replaces: handleStart() multi-store coordination
     */
    startAiGeneration: (aiData: {
      type: AiPaneType
      model: string
      prompt: string
      contextIds: string[]
      language?: string
      settings: Record<string, any>
      conversationId?: string
    }) => {
      // For chat type, stash the first user message for stable handoff
      if (
        aiData.type === 'chat' &&
        aiData.conversationId &&
        aiData.prompt?.trim()
      ) {
        sessionStorageUtils.setChatFirstMessage(
          aiData.conversationId,
          aiData.prompt.trim()
        )
      }

      // Create AI tab with current settings
      const tabId = get().addAiTab(aiData)

      // Clear the prompt after starting
      get().setPrompt('')

      // Close AI pane after starting but keep the new tab active
      get().closeAiPane(false)

      return tabId
    },
  }))
)

// --- Automatic Persistence ---

// Subscribe to state changes and flush to localStorage
useWorkspaceLayoutStore.subscribe(
  state => ({ tabs: state.tabs, activeTabId: state.activeTabId }),
  () => {
    useWorkspaceLayoutStore.getState().flushTabsToLocalStorage()
  }
)

// Flush once more on tab close/refresh to catch any pending changes
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    useWorkspaceLayoutStore.getState().flushTabsToLocalStorage()
  })
}

// --- Typed Selectors for Consistent Usage ---

// Tabs selectors
export const selectTabs = (state: WorkspaceLayoutStore) => state.tabs
export const selectActiveTab = (state: WorkspaceLayoutStore) =>
  state.getActiveTab()
export const selectActiveTabId = (state: WorkspaceLayoutStore) =>
  state.activeTabId

// AI Pane selectors
export const selectIsAiPaneOpen = (state: WorkspaceLayoutStore) => state.isOpen
export const selectAiPaneSettings = (state: WorkspaceLayoutStore) =>
  state.settings
export const selectAiPaneMode = (state: WorkspaceLayoutStore) => state.mode
export const selectAiPaneIsLoading = (state: WorkspaceLayoutStore) =>
  state.isLoading
export const selectAiPaneType = (state: WorkspaceLayoutStore) =>
  state.settings.type
export const selectAiPanePrompt = (state: WorkspaceLayoutStore) =>
  state.settings.prompt
export const selectAiPaneFiles = (state: WorkspaceLayoutStore) =>
  state.settings.files
export const selectAiPaneImages = (state: WorkspaceLayoutStore) =>
  state.settings.images
export const selectAiPaneContext = (state: WorkspaceLayoutStore) =>
  state.settings.context
export const selectAiPaneLanguage = (state: WorkspaceLayoutStore) =>
  state.settings.language
export const selectAiPaneContextTokenCount = (state: WorkspaceLayoutStore) =>
  state.contextTokenCount

// Filter selectors
export const selectFilterMode = (state: WorkspaceLayoutStore) =>
  state.filterMode
