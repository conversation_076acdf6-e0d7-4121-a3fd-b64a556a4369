# Workspace Layout Store Architecture

## Overview

The `useWorkspaceLayoutStore` is a consolidated Zustand store that manages all workspace layout state for the drag tree UI. It replaces three separate stores (`useTabStore`, `useAiPaneStore`, `useFilterStore`) that were previously tightly coupled but treated as independent.

## Architecture

### Store Structure

The store is composed of three logical slices:

1. **TabsSlice** - Tab management and navigation
2. **AiPaneSlice** - AI pane settings and state
3. **FilterSlice** - Tree visualization filters

### Cross-Slice Actions

The store provides atomic actions that coordinate between slices to prevent state drift:

- `closeAiPane(switchToMainTab?: boolean)` - Close AI pane and switch back to the main tab by default (pass `false` to stay on the active AI tab)
- `removeTabWithCleanup(tabId: string)` - Remove tab with automatic AI pane cleanup
- `startAiGeneration(aiData)` - Complete AI generation start sequence
- `applyFilterAndFocus(tabId: string)` - Apply filter with tab navigation

## Key Features

### Tab Management

- **Deduplication**: Reuses tabs for the same nodeId/contentId _and_ for repeated assetIds
- **Deterministic IDs**: Uses `TabPrefixUtils` so temp and asset tabs share the same ID scheme as the registry
- **Asset-aware Lifecycle**: `addTab` and the asset tab manager now funnel through the same creation pathway, ensuring persisted assets resolve to `asset-` IDs automatically
- **Validation**: Automatic cleanup of broken/stale tabs
- **Persistence**: Automatic localStorage sync with per-dragTree isolation

### AI Pane Integration

- **Context Loading**: Tracks loading state and validates actual content existence
- **Settings Management**: Type-safe settings with validation
- **Cross-Tab Coordination**: Automatic pane state management when tabs change

### Filter Integration

- **Visual Filters**: Tree pruning based on research status
- **Navigation Coordination**: Filter changes trigger appropriate tab focus

## Usage Patterns

### Component Integration

Always use selectors to prevent unnecessary re-renders:

```typescript
// ✅ Good - composed selectors for state, direct subscription for actions
const activeTabId = useWorkspaceLayoutStore(selectActiveTabId)
const aiPaneSettings = useWorkspaceLayoutStore(selectAiPaneSettings)
const isAiPaneOpen = useWorkspaceLayoutStore(selectIsAiPaneOpen)
const addTab = useWorkspaceLayoutStore(state => state.addTab)

// ❌ Bad - whole store subscription
const { activeTabId, addTab } = useWorkspaceLayoutStore()
```

### Predefined Selectors

Use the exported selectors for consistency:

```typescript
import {
  useWorkspaceLayoutStore,
  selectTabs,
  selectActiveTab,
  selectActiveTabId,
  selectIsAiPaneOpen,
  selectAiPaneSettings,
  selectAiPanePrompt,
  selectAiPaneFiles,
  selectAiPaneImages,
  selectAiPaneContextTokenCount,
  selectAiPaneType,
  selectAiPaneLanguage,
  selectFilterMode,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'

const tabs = useWorkspaceLayoutStore(selectTabs)
const activeTab = useWorkspaceLayoutStore(selectActiveTab)
const activeTabId = useWorkspaceLayoutStore(selectActiveTabId)
const aiPanePrompt = useWorkspaceLayoutStore(selectAiPanePrompt)
const aiPaneFiles = useWorkspaceLayoutStore(selectAiPaneFiles)
const aiPaneImages = useWorkspaceLayoutStore(selectAiPaneImages)
const contextTokenCount = useWorkspaceLayoutStore(selectAiPaneContextTokenCount)

// Other focused selectors are available for type, language, and context lists:
const aiPaneType = useWorkspaceLayoutStore(selectAiPaneType)
const aiPaneLanguage = useWorkspaceLayoutStore(selectAiPaneLanguage)
```

### Cross-Slice Operations

Use atomic actions instead of manual coordination:

```typescript
// ✅ Good - atomic action
const startGeneration = useWorkspaceLayoutStore(
  state => state.startAiGeneration
)
startGeneration(aiData)

// ❌ Bad - manual coordination
const addAiTab = useWorkspaceLayoutStore(state => state.addAiTab)
const setPrompt = useWorkspaceLayoutStore(state => state.setPrompt)
const closeAiPane = useWorkspaceLayoutStore(state => state.closeAiPane)

const tabId = addAiTab(aiData)
setPrompt('')
closeAiPane()
```

## Migration Notes

### From Previous Stores

The consolidated store maintains backward compatibility for most operations:

- `useTabStore` → `useWorkspaceLayoutStore` (tabs slice)
- `useAiPaneStore` → `useWorkspaceLayoutStore` (AI pane slice)
- `useFilterStore` → `useWorkspaceLayoutStore` (filter slice)
- **Legacy References**: The original store implementations now live in `stores/legacy/` for debug/backport purposes. Prefer the consolidated store for all new work.

### Breaking Changes

1. **Store Import**: Single import replaces three separate imports
2. **Cross-Store Actions**: Manual coordination replaced with atomic actions
3. **Selector Usage**: Whole store destructuring discouraged

## Performance Considerations

- **Selective Subscriptions**: Always use selectors to minimize re-renders
- **Atomic Updates**: Cross-slice actions prevent intermediate state inconsistencies
- **Persistence Optimization**: Debounced localStorage writes reduce I/O overhead

## Testing

The store includes comprehensive test coverage for:

- Tab validation and cleanup logic
- Cross-slice action coordination
- Tab deduplication (node/content and asset-aware flows)
- `closeAiPane` default switching behaviour
- Context loading checks via `isAllSelectedContentLoaded`
- Persistence and restoration
- Edge cases and error handling

Run tests with: `npm run test __tests__/stores/useWorkspaceLayoutStore-validation-cleanup.test.ts`

## Future Enhancements

- **Undo/Redo**: State history tracking for tab operations
- **Workspace Presets**: Save/restore entire workspace configurations
- **Performance Metrics**: Built-in performance monitoring for large workspaces
