import { create } from 'zustand'
import { getAssetType, isValidAssetType } from '@/lib/asset-types'
import {
  useWorkspaceLayoutStore,
  type Tab,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import type { Asset } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import { TabPrefixUtils } from '@/app/(conv)/dragTree/[dragTreeId]/constants/tab-prefixes'

/**
 * Asset Tab Manager Interface
 * Handles asset-specific tab operations using the asset type registry
 */
export interface AssetTabManager {
  /**
   * Add a new asset tab using registry-driven logic
   */
  addAssetTab: (asset: Asset) => string

  /**
   * Update asset-specific tab data
   */
  updateAssetTabData: (
    tabId: string,
    updates: Partial<Tab['aiPaneData']>
  ) => void

  /**
   * Get the tab component for an asset type
   */
  getAssetTabComponent: (
    type: string
  ) => React.ComponentType<{ tab: Tab; dragTreeId: string }> | null

  /**
   * Check if a tab is an asset tab
   */
  isAssetTab: (tab: Tab) => boolean

  /**
   * Get asset type definition for a tab
   */
  getAssetTypeForTab: (tab: Tab) => ReturnType<typeof getAssetType>

  /**
   * Create asset tab title using registry
   */
  createAssetTabTitle: (asset: Asset) => string

  /**
   * Create asset tab ID using registry
   */
  createAssetTabId: (asset: Asset) => string
}

/**
 * Asset Tab Manager Store
 * Provides registry-driven asset tab management
 */
export const useAssetTabManager = create<AssetTabManager>((_set, get) => ({
  addAssetTab: (asset: Asset) => {
    const assetType = getAssetType(asset.type)
    if (!assetType) {
      console.error(`Unknown asset type: ${asset.type}`)
      throw new Error(`Unknown asset type: ${asset.type}`)
    }

    const tabStore = useWorkspaceLayoutStore.getState()
    const expectedId = get().createAssetTabId(asset)

    const existingTab = tabStore.tabs.find(
      (tab: Tab) =>
        tab.id === expectedId || tab.aiPaneData?.assetId === asset.id
    )

    if (existingTab) {
      tabStore.setActiveTab(existingTab.id)
      return existingTab.id
    }

    const title = get().createAssetTabTitle(asset)

    const createdId = tabStore.addTab({
      title,
      fullTitle: title,
      type: asset.type,
      isClosable: true,
      aiPaneData: {
        ...assetType.createAssetTabData?.(asset),
        type: asset.type,
        model: asset.model,
        prompt: asset.prompt,
        contextIds: asset.contextIds,
        settings: {},
        assetId: asset.id,
      },
    })

    console.log(`✅ Created asset tab: ${asset.type} - ${asset.title}`)
    return createdId
  },

  updateAssetTabData: (tabId: string, updates: Partial<Tab['aiPaneData']>) => {
    const tabStore = useWorkspaceLayoutStore.getState()
    tabStore.updateTabAiPaneData(tabId, updates)
  },

  getAssetTabComponent: (type: string) => {
    const assetType = getAssetType(type)
    return assetType?.tabComponent || null
  },

  isAssetTab: (tab: Tab) => {
    return tab.aiPaneData !== undefined && isValidAssetType(tab.type)
  },

  getAssetTypeForTab: (tab: Tab) => {
    return getAssetType(tab.type)
  },

  createAssetTabTitle: (asset: Asset) => {
    const assetType = getAssetType(asset.type)
    if (assetType?.createTabTitle) {
      return assetType.createTabTitle(asset)
    }
    // Fallback to legacy logic
    return `${asset.type === 'generate' ? 'Generate' : 'Chat'} - ${asset.title}`
  },

  createAssetTabId: (asset: Asset) => {
    return TabPrefixUtils.createAssetTabId(asset.type, asset.id)
  },
}))

/**
 * Hook to get asset tab manager functions
 */
export const useAssetTabActions = () => {
  const manager = useAssetTabManager()
  return {
    addAssetTab: manager.addAssetTab,
    updateAssetTabData: manager.updateAssetTabData,
    isAssetTab: manager.isAssetTab,
    getAssetTypeForTab: manager.getAssetTypeForTab,
  }
}

/**
 * Hook to get asset tab component resolver
 */
export const useAssetTabComponentResolver = () => {
  const getAssetTabComponent = useAssetTabManager(
    state => state.getAssetTabComponent
  )
  return getAssetTabComponent
}
