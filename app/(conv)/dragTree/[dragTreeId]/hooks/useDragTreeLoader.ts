import { useState, useEffect, useCallback, useRef } from 'react'
import { getDragTreeStructure } from '@/app/server-actions/drag-tree/get-tree-structure'
import { checkTutorialStatusFromMetadata } from '@/app/(conv)/dragTree/[dragTreeId]/utils/tutorial-utils'
import { DragTreeStatus } from '@prisma/client'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useWorkspaceLayoutStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { performanceMonitor } from '@/app/utils/performance-monitor'

export function useDragTreeLoader(
  dragTreeId: string | null,
  userId: string | undefined,
  initialTreeData?: any // Pre-fetched tree data from RSC
) {
  const setFrontendTreeStructure = useDragTreeStore(
    state => state.setFrontendTreeStructure
  )
  const setScreeningQuestion = useDragTreeStore(
    state => state.setScreeningQuestion
  )
  const setDragTreeId = useDragTreeStore(state => state.setDragTreeId)
  const setDragTreeTitle = useDragTreeStore(state => state.setDragTreeTitle)
  const resetDragTreeData = useDragTreeStore(state => state.resetDragTreeData)
  const setInitialUnreadCounts = useDragTreeStore(
    state => state.setInitialUnreadCounts
  )

  const loadTabsFromLocalStorage = useWorkspaceLayoutStore(
    state => state.loadTabsFromLocalStorage
  )

  const hasInitialized = useRef<boolean>(false)
  const currentRequestId = useRef<string>('')

  const prevDragTreeId = useRef<string | null>(null)
  useEffect(() => {
    if (dragTreeId && dragTreeId !== prevDragTreeId.current) {
      console.log(
        '🔄 [useDragTreeLoader] Route changed, resetting store immediately to prevent mixed state'
      )
      resetDragTreeData()
      // Load tab state from localStorage for the new tree
      if (dragTreeId) {
        loadTabsFromLocalStorage(dragTreeId)
      }
      prevDragTreeId.current = dragTreeId
    }
  }, [dragTreeId, resetDragTreeData, loadTabsFromLocalStorage])

  const [isDragTreeLoading, setIsDragTreeLoading] =
    useState<boolean>(!initialTreeData) // Skip loading if we have initial data
  const [isGenerating, setIsGenerating] = useState<boolean>(false)
  const [errorMessage, setErrorMessage] = useState<string>('')

  // Tutorial state from user metadata (optimized - no separate API call)
  const [tutorialState, setTutorialState] = useState<{
    isCompleted: boolean
    isSkipped: boolean
    shouldShow: boolean
    checked: boolean
  }>({
    isCompleted: false,
    isSkipped: false,
    shouldShow: false,
    checked: false,
  })

  // Process initial tree data if provided (RSC pre-fetch optimization)
  const processTreeData = useCallback(
    (dragTree: any, userMetadata?: any) => {
      console.log(
        '✅ [useDragTreeLoader] Processing tree data with status:',
        dragTree.status
      )

      // Process user metadata for tutorial state (optimized approach)
      if (userMetadata) {
        console.log(
          '🔍 [useDragTreeLoader] User metadata received:',
          userMetadata
        )
        const tutorialStatus = checkTutorialStatusFromMetadata(userMetadata)
        if (tutorialStatus.success && tutorialStatus.data) {
          const { isCompleted, isSkipped, shouldShow } = tutorialStatus.data
          setTutorialState({
            isCompleted,
            isSkipped,
            shouldShow,
            checked: true,
          })
          console.log('✅ [useDragTreeLoader] Tutorial state:', {
            isCompleted,
            isSkipped,
            shouldShow,
          })
        }
      } else {
        console.log('⚠️ [useDragTreeLoader] No user metadata received')
        // If no metadata, assume tutorial should be shown
        setTutorialState({
          isCompleted: false,
          isSkipped: false,
          shouldShow: true,
          checked: true,
        })
      }

      // Set initial unread counts if provided (for immediate badge display)
      if ((dragTree as any)?.unreadCountsByNode) {
        setInitialUnreadCounts((dragTree as any).unreadCountsByNode)
        console.log(
          '📊 [useDragTreeLoader] Set initial unread counts:',
          (dragTree as any).unreadCountsByNode
        )
      }

      if (
        dragTree.status === DragTreeStatus.INITIALIZED ||
        dragTree.status === DragTreeStatus.GENERATING
      ) {
        setIsGenerating(true)
      } else if (dragTree.status === DragTreeStatus.ACTIVE) {
        setScreeningQuestion(
          dragTree.user_prompt || 'Clarification question not available.'
        )
        setDragTreeId(dragTreeId!, userId!)
        // Initial load – we don't want to immediately push the tree back to the DB
        setFrontendTreeStructure(dragTree, true)
        setDragTreeTitle(dragTree.title || null)
        setIsGenerating(false)
      } else {
        setErrorMessage(`Unhandled drag tree status: ${dragTree.status}`)
      }
    },
    [
      dragTreeId,
      userId,
      setDragTreeId,
      setFrontendTreeStructure,
      setScreeningQuestion,
      setDragTreeTitle,
      setInitialUnreadCounts,
    ]
  )

  const loadDragTree = useCallback(async () => {
    if (!dragTreeId || !userId) {
      setIsDragTreeLoading(false)
      if (!dragTreeId) {
        setErrorMessage(
          'No dragTreeId provided. Please start from the screening page.'
        )
      }
      return
    }

    const requestId = `${dragTreeId}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    currentRequestId.current = requestId

    console.log(
      '🌳 [useDragTreeLoader] Loading drag tree with optimized endpoint:',
      dragTreeId,
      'with requestId:',
      requestId
    )
    setIsDragTreeLoading(true)
    setErrorMessage('')

    // Start performance monitoring
    const endTiming = performanceMonitor.startTiming('loadDragTree.optimized')

    try {
      // Use lightweight structure endpoint for faster initial load
      const result = await getDragTreeStructure(dragTreeId)

      if (currentRequestId.current !== requestId) {
        console.log(
          '🚫 [useDragTreeLoader] Ignoring stale response for:',
          dragTreeId,
          'requestId:',
          requestId
        )
        return
      }

      if (result.success && result.data) {
        // Record performance metrics
        endTiming({
          payloadSize: result.metrics.payloadSize,
          nodeCount: result.data.nodes.length,
        })

        // Log performance comparison if available
        console.log(`📊 [useDragTreeLoader] Optimized load metrics:`, {
          payloadSize: `${(result.metrics.payloadSize / 1024).toFixed(2)}KB`,
          nodeCount: result.data.nodes.length,
          queryTime: `${result.metrics.queryTime.toFixed(2)}ms`,
          endpoint: 'getDragTreeStructure',
        })

        processTreeData(result.data, result.userMetadata)
      } else {
        endTiming({ errorCount: 1 })
        throw new Error(result.error || 'Failed to fetch drag tree.')
      }
    } catch (error) {
      if (currentRequestId.current !== requestId) {
        console.log(
          '🚫 [useDragTreeLoader] Ignoring stale error response for:',
          dragTreeId,
          'requestId:',
          requestId
        )
        return
      }

      console.error('❌ [useDragTreeLoader] Error loading drag tree:', error)
      endTiming({ errorCount: 1 })
      setErrorMessage(
        error instanceof Error ? error.message : 'An unknown error occurred'
      )
    } finally {
      if (currentRequestId.current === requestId) {
        setIsDragTreeLoading(false)
      }
    }
  }, [dragTreeId, userId, processTreeData])

  // Initialize with pre-fetched data if available
  useEffect(() => {
    if (initialTreeData && dragTreeId && userId && !hasInitialized.current) {
      console.log(
        '⚡ [useDragTreeLoader] Using pre-fetched tree data (RSC optimization)'
      )
      hasInitialized.current = true
      processTreeData(initialTreeData, initialTreeData.userMetadata)

      // Load tabs from localStorage now that we have the tree ID
      loadTabsFromLocalStorage(dragTreeId)

      setIsDragTreeLoading(false)
      return
    }

    if (userId && dragTreeId && !hasInitialized.current) {
      hasInitialized.current = true
      loadDragTree()
      // Load tabs from localStorage for non-RSC initialization
      loadTabsFromLocalStorage(dragTreeId)
    }
  }, [
    userId,
    dragTreeId,
    initialTreeData,
    processTreeData,
    loadDragTree,
    loadTabsFromLocalStorage,
  ])

  return {
    isDragTreeLoading,
    isGenerating,
    errorMessage,
    tutorialState, // Return tutorial state (optimized)
    clearError: () => setErrorMessage(''),
    reloadDragTree: loadDragTree,
  }
}
