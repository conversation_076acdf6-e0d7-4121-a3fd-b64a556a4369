import { useEffect, useMemo, useState } from 'react'
import { computeLayout } from '@/app/(conv)/dragTree/[dragTreeId]/utils/computeLayout'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useTreeToReactFlowWorker } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useTreeToReactFlowWorker'
import debounce from 'lodash/debounce'
import {
  useWorkspaceLayoutStore,
  selectFilterMode,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { pruneTreeByFilter } from '@/app/(conv)/dragTree/[dragTreeId]/utils/treeFilter'
import type { TreeNode } from '@/app/types'

export const useDiagramData = (layoutMode: 'linear' | 'radial') => {
  const tree = useDragTreeStore(state => state.frontendTreeStructure)
  const getNodeContent = useDragTreeStore(state => state.getNodeContent)
  const mode = useWorkspaceLayoutStore(selectFilterMode)

  // Apply visual filter to the tree before conversion
  const filteredTree = useMemo<TreeNode | null>(() => {
    return pruneTreeByFilter(tree, mode, getNodeContent)
  }, [tree, mode, getNodeContent])

  // ------------------------------------------------------------
  // Performance: Debounce expensive layout computations
  // ------------------------------------------------------------
  // Rapid successive updates to the tree (e.g., bulk node additions)
  // can trigger multiple expensive layout recalculations in quick
  // succession. We debounce the `filteredTree` and `layoutMode` inputs so
  // that layout only recalculates after the user (or automated
  // process) pauses updates for a short period.

  // Debounced copies of the filtered tree and layout mode
  const [debouncedTree, setDebouncedTree] = useState(filteredTree)
  const [debouncedLayoutMode, setDebouncedLayoutMode] = useState(layoutMode)

  useEffect(() => {
    const handler = debounce(() => {
      setDebouncedTree(filteredTree)
      setDebouncedLayoutMode(layoutMode)
    }, 300) // 300 ms debounce – adjust if necessary

    handler()

    // Cleanup to avoid memory leaks
    return () => {
      handler.cancel()
    }
  }, [filteredTree, layoutMode])

  // Off-main-thread conversion via Web Worker
  const diagramData = useTreeToReactFlowWorker(debouncedTree)

  // Memoize the layout calculation. This will re-run whenever the layout mode or the base data changes.
  const layoutPromise = useMemo(async () => {
    if (diagramData.nodes.length === 0) {
      return { nodes: [], edges: [] }
    }

    try {
      const result = await computeLayout(
        debouncedLayoutMode,
        diagramData.nodes,
        diagramData.edges
      )
      return result
    } catch (error) {
      console.error(`${debouncedLayoutMode} layout failed:`, error)
      // Return original diagram data as fallback
      return diagramData
    }
  }, [diagramData, debouncedLayoutMode])

  return { diagramData, layoutPromise }
}
