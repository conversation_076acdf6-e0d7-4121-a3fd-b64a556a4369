'use client'

import { useEffect } from 'react'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { useWorkspaceLayoutStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'

/**
 * Hook for handling selection state cleanup
 *
 * Ensures selection state is properly cleaned up when:
 * - Component unmounts
 * - AI pane is opened
 * - User navigates away
 * - Selection mode is disabled
 */
export const useSelectionCleanup = () => {
  const isSelectionMode = useSelectionStore(state => state.isSelectionMode)
  const clearAll = useSelectionStore(state => state.clearAll)
  const setSelectionMode = useSelectionStore(state => state.setSelectionMode)

  const isAiPaneOpen = useWorkspaceLayoutStore(state => state.isOpen)

  // Cleanup when AI pane opens (after context has been applied)
  useEffect(() => {
    if (isAiPaneOpen && !isSelectionMode) {
      // Clear selections after AI pane opens and selection mode is disabled
      const timeoutId = setTimeout(() => {
        clearAll()
      }, 500) // Increased delay to ensure context application completes

      return () => clearTimeout(timeoutId)
    }
  }, [isAiPaneOpen, isSelectionMode, clearAll])

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      // Clear all selection state when component unmounts
      clearAll()
      setSelectionMode(false)
    }
  }, [clearAll, setSelectionMode])

  // Cleanup when navigating away (beforeunload event)
  useEffect(() => {
    const handleBeforeUnload = () => {
      clearAll()
      setSelectionMode(false)
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [clearAll, setSelectionMode])

  // Removed visibilitychange cleanup to preserve selection across tab switches

  return {
    // Expose manual cleanup function if needed
    manualCleanup: () => {
      clearAll()
      setSelectionMode(false)
    },
  }
}
