'use client'

import { useEffect, useCallback, useState } from 'react'
import { useReactFlow } from 'reactflow'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { useWorkspaceLayoutStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { useSelectionToContext } from './useSelectionToContext'

// Global state for keyboard feedback
let keyboardFeedbackListeners: ((feedback: KeyboardFeedback) => void)[] = []

type KeyboardFeedback = {
  key: string
  timestamp: number
}

const notifyKeyboardFeedback = (key: string) => {
  const feedback: KeyboardFeedback = {
    key,
    timestamp: Date.now(),
  }
  keyboardFeedbackListeners.forEach(listener => listener(feedback))
}

/**
 * Hook for handling keyboard shortcuts in selection mode
 *
 * Keyboard shortcuts:
 * - Escape: Exit selection mode
 * - Delete/Backspace: Clear current selection
 * - Enter: Use selection as context (if any nodes selected)
 * - Arrow keys: Pan the diagram for navigation
 * - +/-: Zoom in/out
 * - Ctrl/Cmd + A: Select all researched nodes (future enhancement)
 */
export const useSelectionKeyboard = () => {
  const { setViewport, getViewport, zoomIn, zoomOut, fitView } = useReactFlow()

  const isSelectionMode = useSelectionStore(state => state.isSelectionMode)
  const setSelectionMode = useSelectionStore(state => state.setSelectionMode)
  const clearSelectedNodes = useSelectionStore(
    state => state.clearSelectedNodes
  )
  const selectedCount = useSelectionStore(state => state.getSelectedCount())

  const setIsAiPaneOpen = useWorkspaceLayoutStore(state => state.setIsOpen)

  const { applySelectionAsContext, getResearchedSelectionCount } =
    useSelectionToContext()

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Only handle keyboard shortcuts when in selection mode
      if (!isSelectionMode) return

      const panStep = 50 // pixels to pan per key press
      const currentViewport = getViewport()

      switch (event.key) {
        case 'Escape':
          event.preventDefault()
          setSelectionMode(false)
          break

        case 'Delete':
        case 'Backspace':
          event.preventDefault()
          clearSelectedNodes()
          break

        case 'Enter':
          event.preventDefault()
          if (selectedCount > 0 && getResearchedSelectionCount() > 0) {
            applySelectionAsContext()
            setIsAiPaneOpen(true)
            setSelectionMode(false)
          }
          break

        // Navigation keys for panning
        case 'ArrowUp':
          event.preventDefault()
          notifyKeyboardFeedback('ArrowUp')
          setViewport({
            ...currentViewport,
            y: currentViewport.y + panStep,
          })
          break

        case 'ArrowDown':
          event.preventDefault()
          notifyKeyboardFeedback('ArrowDown')
          setViewport({
            ...currentViewport,
            y: currentViewport.y - panStep,
          })
          break

        case 'ArrowLeft':
          event.preventDefault()
          notifyKeyboardFeedback('ArrowLeft')
          setViewport({
            ...currentViewport,
            x: currentViewport.x + panStep,
          })
          break

        case 'ArrowRight':
          event.preventDefault()
          notifyKeyboardFeedback('ArrowRight')
          setViewport({
            ...currentViewport,
            x: currentViewport.x - panStep,
          })
          break

        // Zoom keys
        case '+':
        case '=':
          event.preventDefault()
          notifyKeyboardFeedback('ZoomIn')
          zoomIn()
          break

        case '-':
          event.preventDefault()
          notifyKeyboardFeedback('ZoomOut')
          zoomOut()
          break

        case '0':
          event.preventDefault()
          notifyKeyboardFeedback('FitView')
          fitView()
          break

        default:
          // Don't prevent default for other keys
          break
      }
    },
    [
      isSelectionMode,
      setSelectionMode,
      clearSelectedNodes,
      selectedCount,
      getResearchedSelectionCount,
      applySelectionAsContext,
      setIsAiPaneOpen,
      getViewport,
      setViewport,
      zoomIn,
      zoomOut,
      fitView,
    ]
  )

  // Add keyboard event listeners
  useEffect(() => {
    if (isSelectionMode) {
      document.addEventListener('keydown', handleKeyDown)

      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [isSelectionMode, handleKeyDown])

  return {
    // Expose manual handlers if needed
    exitSelectionMode: () => setSelectionMode(false),
    clearSelection: clearSelectedNodes,
    useSelectionAsContext: () => {
      if (selectedCount > 0 && getResearchedSelectionCount() > 0) {
        applySelectionAsContext()
        setIsAiPaneOpen(true)
        setSelectionMode(false)
      }
    },
  }
}

// Export the keyboard feedback hook for use in navigation widgets
export const useKeyboardFeedback = () => {
  const [feedback, setFeedback] = useState<KeyboardFeedback | null>(null)

  useEffect(() => {
    const listener = (newFeedback: KeyboardFeedback) => {
      setFeedback(newFeedback)
      // Clear feedback after animation duration
      setTimeout(() => setFeedback(null), 200)
    }

    keyboardFeedbackListeners.push(listener)

    return () => {
      keyboardFeedbackListeners = keyboardFeedbackListeners.filter(
        l => l !== listener
      )
    }
  }, [])

  return feedback
}
