'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'

import { Rating } from '@smastrom/react-rating'
import { MessageCircle, Send } from 'lucide-react'
import { cn } from '@/lib/utils'
import toast from 'react-hot-toast'
import { useSession } from 'next-auth/react'
import { logEventWithContext } from '@/app/libs/logging'
import type {
  FeedbackType,
  EngagementLevel,
  RatingKeys,
  NullableRating,
  FeedbackTypeOption,
  EngagementOption,
} from './types'
import { getLatestUserFeedbackForEntity } from '@/app/server-actions/user-feedback'
import { UserFeedbackType } from '@prisma/client'

// Feedback type options configuration
const FEEDBACK_TYPE_OPTIONS: FeedbackTypeOption[] = [
  {
    value: 'bug',
    label: 'Report a bug',
    description: "Something isn't working as expected",
    icon: '🐛',
  },
  {
    value: 'suggestion',
    label: 'Give feedback/suggestions',
    description: 'Share ideas for improvements',
    icon: '💡',
  },
]

// Engagement level options configuration
const ENGAGEMENT_OPTIONS: EngagementOption[] = [
  {
    value: 'connect',
    label: "YES! Let's connect!",
    description: "I'd love to share more with a real human",
    icon: '🚀',
    size: 'large',
    colorScheme:
      'bg-gradient-to-r from-green-400 to-emerald-500 hover:from-green-500 hover:to-emerald-600',
  },
  {
    value: 'open',
    label: "I'm open to it",
    description: 'Happy to participate in user interviews or surveys',
    icon: '💬',
    size: 'medium',
    colorScheme:
      'bg-gradient-to-r from-blue-400 to-indigo-500 hover:from-blue-500 hover:to-indigo-600',
  },
  {
    value: 'listening',
    label: 'Just listening',
    description: "I've shared my thoughts, no follow-up needed",
    icon: '👂',
    size: 'small',
    colorScheme:
      'bg-gradient-to-r from-slate-400 to-gray-500 hover:from-slate-500 hover:to-gray-600',
  },
]

// Rating questions from existing feedback system
const questionTextMap: Record<RatingKeys, string> = {
  smoothness: 'Overall Flow; Smoothness of Journey',
  quality: 'Relevance and Clarity of Questions',
  effort:
    'Did we overwhelm you with the task load? [one: overwhelming; five: reasonable]',
}

type FeedbackWidgetProps = {
  dragTreeId: string
  className?: string
  displayMode?: 'floating' | 'inline'
}

/**
 * FeedbackWidget - A floating feedback collection widget for the drag tree page
 *
 * Features:
 * - Bottom-right floating button (Intercom-style)
 * - Modal with feedback type selection
 * - Star rating system (reused from existing feedback)
 * - Professional textarea with conditional enabling
 * - Development-only visibility via feature flag
 * - Success confirmation and error handling
 */
const FeedbackWidget: React.FC<FeedbackWidgetProps> = ({
  dragTreeId,
  className,
  displayMode = 'floating',
}) => {
  const { data: session } = useSession()

  // Modal state
  const [isOpen, setIsOpen] = useState<boolean>(false)

  // Form state
  const [selectedFeedbackType, setSelectedFeedbackType] =
    useState<FeedbackType | null>(null)
  const [ratings, setRatings] = useState<NullableRating>({
    smoothness: null,
    quality: null,
    effort: null,
  })
  const [selectedEngagementLevel, setSelectedEngagementLevel] =
    useState<EngagementLevel | null>(null)
  const [feedback, setFeedback] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false)

  // Collapsible section state
  const [isFeedbackTypeCollapsed, setIsFeedbackTypeCollapsed] =
    useState<boolean>(false)
  const [isSurveyCollapsed, setIsSurveyCollapsed] = useState<boolean>(false)

  // Track if user manually expanded sections to prevent auto-collapse
  const [feedbackTypeManuallyExpanded, setFeedbackTypeManuallyExpanded] =
    useState<boolean>(false)
  const [surveyManuallyExpanded, setSurveyManuallyExpanded] =
    useState<boolean>(false)

  // Check if survey is complete (all ratings provided)
  const isSurveyComplete = Object.values(ratings).every(
    rate => rate !== null && rate !== 0
  )

  // Auto-collapse sections when completed (but not if manually expanded)
  useEffect(() => {
    if (selectedFeedbackType && !feedbackTypeManuallyExpanded) {
      setIsFeedbackTypeCollapsed(true)
    }
  }, [selectedFeedbackType, feedbackTypeManuallyExpanded])

  useEffect(() => {
    if (isSurveyComplete && !surveyManuallyExpanded) {
      setIsSurveyCollapsed(true)
    }
  }, [isSurveyComplete, surveyManuallyExpanded])

  // Feature flag - only show in development for now. Flip feature flag when ready for prod.
  const isDevelopment = process.env.NODE_ENV === 'development'
  if (!isDevelopment) return null

  // Check if form is ready for submission
  const isFormComplete =
    selectedFeedbackType &&
    isSurveyComplete &&
    selectedEngagementLevel &&
    feedback.trim().length > 0

  // Generate specific validation message
  const getValidationMessage = () => {
    if (!selectedFeedbackType) {
      return 'Please select a feedback type to continue'
    }
    if (!isSurveyComplete) {
      return 'Please complete the survey ratings to continue'
    }
    if (!selectedEngagementLevel) {
      return 'Please select how we can follow up with you'
    }
    if (feedback.trim().length === 0) {
      return 'Please provide feedback details to submit'
    }
    return ''
  }

  const handleRating = (question: RatingKeys, rate: number) => {
    setRatings(prevRatings => ({
      ...prevRatings,
      [question]: rate,
    }))
    setSurveyManuallyExpanded(false) // Reset manual expansion flag when rating changes
  }

  const handleOpenModal = () => {
    setIsOpen(true)
    logEventWithContext(
      'click_feedback_widget_opened',
      session?.user?.id,
      dragTreeId,
      {}
    )
  }

  const handleCloseModal = () => {
    setIsOpen(false)
    // Reset form state when closing
    setSelectedFeedbackType(null)
    setRatings({
      smoothness: null,
      quality: null,
      effort: null,
    })
    setSelectedEngagementLevel(null)
    setFeedback('')
    // Reset collapsible states
    setIsFeedbackTypeCollapsed(false)
    setIsSurveyCollapsed(false)
    // Reset manual expansion flags
    setFeedbackTypeManuallyExpanded(false)
    setSurveyManuallyExpanded(false)
  }

  const handleSubmit = async () => {
    if (!isFormComplete) {
      toast.error('Please complete all fields before submitting')
      return
    }

    setIsSubmitting(true)

    try {
      // Build payload matching generic feedback schema
      const metadata = {
        feedback_reason: selectedFeedbackType, // 'bug' | 'suggestion'
        survey_scores: ratings,
        engagement_level: selectedEngagementLevel, // 'connect' | 'open' | 'listening'
        outreach_urgency:
          selectedEngagementLevel === 'connect'
            ? 'high'
            : selectedEngagementLevel === 'open'
              ? 'medium'
              : 'low',
        question_text_map: {
          smoothness: 'Overall Flow; Smoothness of Journey',
          quality: 'Relevance and Clarity of Questions',
          effort:
            'Did we overwhelm you with the task load? [one: overwhelming; five: reasonable]',
        },
      }

      // Submit to server action
      const { createUserFeedback } = await import(
        '@/app/server-actions/user-feedback'
      )
      const { UserFeedbackType } = await import('@prisma/client')

      const created = await createUserFeedback({
        entityType: 'DRAGTREE',
        entityId: dragTreeId,
        feedbackType: UserFeedbackType.DRAGTREE_FLOATING_BUTTON,
        canContact: selectedEngagementLevel !== 'listening',
        feedbackText: feedback,
        metadata,
      })
      const createdId =
        created && typeof created === 'object' && 'id' in created
          ? (created as any).id
          : undefined

      logEventWithContext(
        'submit_feedback_widget',
        session?.user?.id,
        dragTreeId,
        {
          feedbackType: selectedFeedbackType,
          engagementLevel: selectedEngagementLevel,
          hasRatings: isSurveyComplete,
          feedbackLength: feedback.length,
          feedbackRecordId: createdId,
        }
      )

      toast.success('Thanks! Your feedback was submitted successfully.', {
        duration: 2800,
      })
      handleCloseModal()
    } catch (_error) {
      console.error('Error submitting feedback:', _error)
      toast.error('Something went wrong. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const isFloating = displayMode === 'floating'
  const containerClasses = cn(
    isFloating ? 'fixed bottom-4 right-4 z-50' : 'relative flex items-center',
    className
  )
  const triggerClassName = isFloating
    ? cn(
        'h-10 w-10 rounded-full shadow-md hover:shadow-lg',
        'bg-gradient-to-br from-blue-400 via-blue-500 to-sky-400',
        'hover:from-blue-500 hover:via-blue-600 hover:to-sky-500',
        'text-white border border-white/30',
        'transition-all duration-1000 ease-in-out',
        'hover:scale-105 active:scale-95',
        'focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2',
        'animate-pulse hover:animate-none',
        '[animation-duration:6s]'
      )
    : cn(
        'flex items-center gap-1.5 rounded-md border border-cyan-200 bg-white px-3 py-1.5',
        'text-sm font-medium text-cyan-700 shadow-sm hover:bg-cyan-50 hover:text-cyan-800',
        'focus:outline-none focus:ring-2 focus:ring-cyan-300 focus:ring-offset-1'
      )
  const triggerVariant = isFloating ? 'ghost' : 'outline'
  const triggerSize = isFloating ? 'icon' : 'sm'

  return (
    <React.Fragment>
      {/* Trigger button */}
      <div className={containerClasses}>
        <Button
          onClick={handleOpenModal}
          variant={triggerVariant}
          size={triggerSize}
          className={triggerClassName}
          aria-label="Open feedback widget"
          title="Share your thoughts 💭"
        >
          <MessageCircle className="h-4 w-4" />
          {!isFloating && <span className="hidden md:inline">Feedback</span>}
        </Button>
      </div>

      {/* Feedback Modal */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-2xl w-[95vw] sm:w-full p-0 overflow-hidden max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <DialogTitle className="text-xl font-semibold text-gray-900">
              Share Your Feedback
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-1">
              Help us improve your experience
            </p>
            {/* Prior submission notice */}
            <PriorFeedbackNotice dragTreeId={dragTreeId} />
          </div>

          {/* Content */}
          <div className="px-6 py-4 space-y-6">
            {/* Step 1: Feedback Type Selection */}
            <div className="space-y-3">
              {isFeedbackTypeCollapsed && selectedFeedbackType ? (
                // Collapsed view - show selected option
                <div
                  onClick={() => {
                    setIsFeedbackTypeCollapsed(false)
                    setFeedbackTypeManuallyExpanded(true)
                  }}
                  className="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg cursor-pointer hover:bg-blue-100 transition-all"
                >
                  <span className="text-xl mr-3">
                    {
                      FEEDBACK_TYPE_OPTIONS.find(
                        opt => opt.value === selectedFeedbackType
                      )?.icon
                    }
                  </span>
                  <div className="flex-1">
                    <div className="font-medium text-blue-900 text-sm">
                      {
                        FEEDBACK_TYPE_OPTIONS.find(
                          opt => opt.value === selectedFeedbackType
                        )?.label
                      }{' '}
                      ✓
                    </div>
                    <div className="text-xs text-blue-700">
                      Click to change selection
                    </div>
                  </div>
                </div>
              ) : (
                // Expanded view - show all options
                <>
                  <h3
                    className="text-lg font-medium text-gray-900"
                    id="feedback-type-heading"
                  >
                    What type of feedback would you like to share?
                  </h3>
                  <fieldset
                    className="grid gap-3"
                    aria-labelledby="feedback-type-heading"
                  >
                    {FEEDBACK_TYPE_OPTIONS.map(option => (
                      <label
                        key={option.value}
                        className={cn(
                          'flex items-center p-3 sm:p-4 border rounded-lg cursor-pointer transition-all',
                          'focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2',
                          selectedFeedbackType === option.value
                            ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                            : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                        )}
                      >
                        <input
                          type="radio"
                          name="feedbackType"
                          value={option.value}
                          checked={selectedFeedbackType === option.value}
                          onChange={e => {
                            const newValue = e.target.value as FeedbackType
                            // If clicking the same option, collapse the section
                            if (selectedFeedbackType === newValue) {
                              setIsFeedbackTypeCollapsed(true)
                            } else {
                              setSelectedFeedbackType(newValue)
                            }
                            setFeedbackTypeManuallyExpanded(false) // Reset manual expansion flag
                          }}
                          className="sr-only"
                          aria-describedby={`feedback-${option.value}-description`}
                        />
                        <span
                          className="text-xl sm:text-2xl mr-3"
                          aria-hidden="true"
                        >
                          {option.icon}
                        </span>
                        <div className="flex-1">
                          <div className="font-medium text-gray-900 text-sm sm:text-base">
                            {option.label}
                          </div>
                          <div
                            className="text-xs sm:text-sm text-gray-600"
                            id={`feedback-${option.value}-description`}
                          >
                            {option.description}
                          </div>
                        </div>
                      </label>
                    ))}
                  </fieldset>
                </>
              )}
            </div>

            {/* Step 2: Survey Questions (shown after feedback type selection) */}
            {selectedFeedbackType && (
              <div className="space-y-4">
                {isSurveyCollapsed && isSurveyComplete ? (
                  // Collapsed view - show completion status
                  <div
                    onClick={() => {
                      setIsSurveyCollapsed(false)
                      setSurveyManuallyExpanded(true)
                    }}
                    className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg cursor-pointer hover:bg-green-100 transition-all"
                  >
                    <span className="text-xl mr-3">⭐</span>
                    <div className="flex-1">
                      <div className="font-medium text-green-900 text-sm">
                        Survey completed ✓
                      </div>
                      <div className="text-xs text-green-700">
                        Click to review your ratings
                      </div>
                    </div>
                  </div>
                ) : (
                  // Expanded view - show full survey
                  <>
                    <h3
                      className="text-lg font-medium text-gray-900"
                      id="survey-heading"
                    >
                      Please rate your experience
                    </h3>
                    <div
                      className={cn(
                        'p-3 sm:p-4 bg-gray-50 rounded-lg border-2 transition-all',
                        isSurveyComplete
                          ? 'border-green-200 bg-green-50'
                          : 'border-yellow-200 bg-yellow-50'
                      )}
                      role="group"
                      aria-labelledby="survey-heading"
                    >
                      {(
                        ['smoothness', 'quality', 'effort'] as RatingKeys[]
                      ).map((question, idx) => (
                        <div key={idx} className="mb-4 last:mb-0">
                          <label
                            className="block text-sm font-medium text-gray-700 mb-2"
                            id={`rating-${question}-label`}
                          >
                            {questionTextMap[question]}
                          </label>
                          <Rating
                            value={ratings[question] ?? 0}
                            onChange={(rate: number) =>
                              handleRating(question, rate)
                            }
                            className="max-w-xs"
                            aria-labelledby={`rating-${question}-label`}
                          />
                        </div>
                      ))}
                      {!isSurveyComplete && (
                        <p className="text-sm text-yellow-700 mt-2">
                          Please complete all ratings to continue
                        </p>
                      )}
                    </div>
                  </>
                )}
              </div>
            )}

            {/* Step 3: Detailed Feedback (enabled after survey completion) */}
            {selectedFeedbackType && isSurveyComplete && (
              <div className="space-y-3">
                <h3
                  className="text-lg font-medium text-gray-900"
                  id="feedback-details-heading"
                >
                  Tell us more about your experience ✨
                </h3>
                <Textarea
                  value={feedback}
                  onChange={e => setFeedback(e.target.value)}
                  placeholder={`Please share your ${selectedFeedbackType === 'bug' ? 'bug report' : 'suggestions'} details...`}
                  className="min-h-[100px] sm:min-h-[120px] resize-none transition-all border-2 border-gray-200 focus:border-blue-500"
                  rows={4}
                  aria-labelledby="feedback-details-heading"
                />
                {feedback.trim().length === 0 && (
                  <p className="text-sm text-gray-500">
                    Please provide some details about your feedback to continue.
                  </p>
                )}
              </div>
            )}

            {/* Step 4: Engagement Level Selection (shown after survey completion) */}
            {selectedFeedbackType && isSurveyComplete && (
              <div className="space-y-4">
                <h3
                  className="text-lg font-medium text-gray-900"
                  id="engagement-heading"
                >
                  How can we follow up with you? 🤝
                </h3>
                <div
                  className="flex flex-col sm:flex-row gap-3"
                  role="group"
                  aria-labelledby="engagement-heading"
                >
                  {ENGAGEMENT_OPTIONS.map(option => (
                    <button
                      key={option.value}
                      type="button"
                      onClick={() => setSelectedEngagementLevel(option.value)}
                      className={cn(
                        'text-left transition-all duration-200 rounded-lg border-2 text-white',
                        'focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
                        'transform hover:scale-[1.01] active:scale-[0.99]',
                        option.colorScheme,
                        selectedEngagementLevel === option.value
                          ? 'ring-2 ring-white ring-offset-2 shadow-md scale-[1.01]'
                          : 'hover:shadow-sm',
                        // Compact size for better modal fit
                        'flex-1 p-2 sm:p-3'
                      )}
                      aria-pressed={selectedEngagementLevel === option.value}
                    >
                      <div className="flex items-center">
                        <span className="mr-2 text-lg" aria-hidden="true">
                          {option.icon}
                        </span>
                        <div className="flex-1 min-w-0">
                          <div className="font-semibold text-xs sm:text-sm truncate">
                            {option.label}
                          </div>
                          <div className="opacity-90 text-xs leading-tight hidden sm:block">
                            {option.description}
                          </div>
                        </div>
                        {selectedEngagementLevel === option.value && (
                          <div className="ml-2">
                            <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center">
                              <div className="w-3 h-3 bg-white rounded-full animate-pulse" />
                            </div>
                          </div>
                        )}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-200 bg-gray-50 flex flex-col sm:flex-row gap-3 sm:gap-0 sm:justify-between sm:items-center">
            <Button
              variant="outline"
              onClick={handleCloseModal}
              disabled={isSubmitting}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={!isFormComplete || isSubmitting}
              className={cn(
                'w-full sm:w-auto min-w-[120px] transition-all duration-200',
                isFormComplete
                  ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-md hover:shadow-lg'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed opacity-60 hover:bg-gray-300'
              )}
              aria-describedby={
                !isFormComplete ? 'submit-disabled-help' : undefined
              }
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Submitting...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <Send className="h-4 w-4" />
                  <span>Submit Feedback</span>
                </div>
              )}
            </Button>
            {!isFormComplete && (
              <p
                className="text-xs text-gray-500 text-center sm:text-left"
                id="submit-disabled-help"
              >
                {getValidationMessage()}
              </p>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </React.Fragment>
  )
}

export default FeedbackWidget

// Inline component to show prior feedback timestamp if any
function PriorFeedbackNotice({ dragTreeId }: { dragTreeId: string }) {
  const [submittedAt, setSubmittedAt] = React.useState<string | null>(null)
  const [isLoading, setIsLoading] = React.useState<boolean>(true)

  useEffect(() => {
    let isMounted = true
    ;(async () => {
      try {
        const latest = await getLatestUserFeedbackForEntity({
          entityType: 'DRAGTREE',
          entityId: dragTreeId,
          feedbackType: UserFeedbackType.DRAGTREE_FLOATING_BUTTON,
        })
        if (
          isMounted &&
          latest &&
          'created_at' in latest &&
          latest.created_at
        ) {
          const date = new Date(latest.created_at as unknown as string)
          setSubmittedAt(date.toLocaleString())
        }
      } catch (_e) {
        // Silently ignore fetching errors for this non-blocking notice
      } finally {
        if (isMounted) setIsLoading(false)
      }
    })()
    return () => {
      isMounted = false
    }
  }, [dragTreeId])

  if (isLoading) return null
  if (!submittedAt) return null

  return (
    <div className="mt-2 text-xs text-gray-600">
      You last shared feedback on {submittedAt}. Thank you!
    </div>
  )
}
