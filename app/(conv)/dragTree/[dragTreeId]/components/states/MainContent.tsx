'use client'

import React, { useEffect, useCallback, startTransition } from 'react'
import { Session } from 'next-auth'
import Header from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/Header'
import { SimpleStoreTest } from '@/app/(conv)/dragTree/[dragTreeId]/components/debug'
import { useDragTreeStore } from '@/app/stores/dragtree_store/store'
import ResizableLayout from '@/app/(conv)/dragTree/[dragTreeId]/components/ResizableLayout'
import HierarchicalOutline from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline'
import { VisualFlowDiagram } from '@/app/(conv)/dragTree/[dragTreeId]/components/VisualFlowDiagram'
import TutorialOverlay from '@/app/(conv)/dragTree/[dragTreeId]/components/tutorial/TutorialOverlay'

import AiPane from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/AiPane'
import {
  useWorkspaceLayoutStore,
  selectIsAiPaneOpen,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { useAssetStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useAssetStore'
import { getAIGenerations } from '@/app/server-actions/drag-tree/get_ai_generations'
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip'
// import type { LayoutMode } from '@/app/(conv)/dragTree/[dragTreeId]/types' // Currently unused

type MainContentProps = {
  session: Session | null
  showSyncTest: boolean
  onToggleSyncTest: () => void
  dragTreeId: string | null
  // Tree loading state
  isDragTreeLoading?: boolean
  // Tutorial props
  showTutorial?: boolean
  onTutorialComplete?: () => void
  onTutorialSkip?: () => void
}

export default function MainContent({
  session,
  showSyncTest,
  onToggleSyncTest,
  dragTreeId,
  isDragTreeLoading = false,
  showTutorial = false,
  onTutorialComplete,
  onTutorialSkip,
}: MainContentProps) {
  // Optimized: Use individual selectors to prevent unnecessary re-renders
  const loadFromDatabase = useDragTreeStore(state => state.loadFromDatabase)
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )

  // AI Pane state
  const isAiPaneOpen = useWorkspaceLayoutStore(selectIsAiPaneOpen)
  const setIsAiPaneOpen = useWorkspaceLayoutStore(state => state.setIsOpen)
  const closeAiPane = useWorkspaceLayoutStore(state => state.closeAiPane)

  // Asset store for AI generations
  const bulkLoadAssets = useAssetStore(state => state.bulkLoadAssets)

  // Memoize the onClose handler to prevent unnecessary re-renders of AiPane
  const handleAiPaneClose = useCallback(() => {
    closeAiPane(true)
  }, [closeAiPane])

  // Load AI generations for this drag tree
  const loadAIGenerations = useCallback(
    async (dragTreeId: string) => {
      try {
        console.log(
          '🔄 [MainContent] Loading AI generations for dragTree:',
          dragTreeId
        )
        const generations = await getAIGenerations(dragTreeId)
        bulkLoadAssets(generations, dragTreeId)
        console.log(
          '✅ [MainContent] Loaded',
          generations.length,
          'AI generations'
        )
      } catch (error) {
        console.error('❌ [MainContent] Failed to load AI generations:', error)
      }
    },
    [bulkLoadAssets]
  )

  useEffect(() => {
    if (dragTreeId) {
      // Load both drag tree data and AI generations
      loadFromDatabase()
      startTransition(() => {
        loadAIGenerations(dragTreeId)
      })
    }
  }, [dragTreeId, loadAIGenerations]) // Removed loadFromDatabase dependency to prevent unnecessary calls

  const handleTutorialClose = () => {
    // When tutorial is completed (not skipped), mark as completed in DB
    onTutorialComplete?.()
  }

  const handleTutorialSkip = () => {
    // When tutorial is skipped, call the skip handler (distinct from completion)
    onTutorialSkip?.()
  }

  // Only show tutorial if we have content and tutorial is enabled
  const shouldShowTutorial = Boolean(
    showTutorial && frontendTreeStructure && !isDragTreeLoading
  )

  // Layout mode state for the visual flow diagram - currently not used
  // const [layoutMode, setLayoutMode] = useState<LayoutMode>('linear')
  return (
    <div
      id="tutorial-whole-page"
      className="flex flex-col h-full max-w-full w-full bg-white overflow-x-hidden animate-in fade-in duration-300"
    >
      {/* Header - Fixed height */}
      <div className="flex-shrink-0 border-b">
        <Header
          session={session}
          showSyncTest={showSyncTest}
          onToggleSyncTest={onToggleSyncTest}
          isLoading={isDragTreeLoading}
        />
      </div>

      {/* Main Content - Fixed height layout */}
      <div className="flex flex-col flex-1 min-h-0 animate-in slide-in-from-bottom-2 duration-500 delay-100">
        {showSyncTest ? (
          /* Sync Test Mode */
          <div className="flex-1 overflow-auto">
            {dragTreeId ? (
              <SimpleStoreTest />
            ) : (
              <div className="p-6 text-center text-gray-500">
                <p>No dragTreeId available for sync testing</p>
              </div>
            )}
          </div>
        ) : (
          /* Normal Mode */
          <>
            {/* Resizable Layout Container - Takes remaining space with fixed height */}
            <div className="flex-1 min-h-0 relative">
              <ResizableLayout
                leftPanel={
                  <HierarchicalOutline dragTreeId={dragTreeId || ''} />
                }
                rightPanel={<VisualFlowDiagram />}
                aiPane={
                  <AiPane
                    isOpen={isAiPaneOpen}
                    onClose={handleAiPaneClose}
                    dragTreeId={dragTreeId || ''}
                  />
                }
                showAiPane={isAiPaneOpen}
                minLeftWidth={20}
                minRightWidth={20}
                minAiPaneWidth={15}
                defaultLeftWidth={50}
                dragTreeId={dragTreeId || ''}
              />

              {/* Right-edge slider to open AI pane (mirrors sidebar style) */}
              {!isAiPaneOpen && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="fixed inset-y-0 right-0 z-[55]">
                      <div
                        onClick={() => setIsAiPaneOpen(true)}
                        className="w-2 h-full bg-gray-600 hover:bg-gray-700 cursor-pointer group transition-colors"
                      >
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-70 group-hover:opacity-100 transition-opacity">
                          <div className="w-6 h-12 bg-gray-600 rounded-l-xl flex items-center justify-center shadow-lg">
                            <div className="w-1 h-6 bg-white rounded" />
                          </div>
                        </div>
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="left">
                    <p>Click to open AI assistant</p>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          </>
        )}
      </div>

      {/* Tutorial Overlay */}
      <TutorialOverlay
        isVisible={shouldShowTutorial}
        onComplete={handleTutorialClose}
        onSkip={handleTutorialSkip}
      />
    </div>
  )
}
