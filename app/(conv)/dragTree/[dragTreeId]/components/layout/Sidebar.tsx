'use client'

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Session } from 'next-auth'
import { useSession as useNextAuthSession } from 'next-auth/react'
import { MdNoteAdd, MdClose } from 'react-icons/md'
import { <PERSON><PERSON><PERSON>, FiFolder, FiRefreshCw } from 'react-icons/fi'
import { useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import ProfileDialog from '@/app/(conv)/dragTree/[dragTreeId]/components/layout/ProfileDialog'
import { DragTreeStatus, SubscriptionTier } from '@prisma/client'
import { formatDistanceToNow } from 'date-fns'
import { sidebarEvents, type DragTreeData } from '@/app/libs/sidebar-events'
import {
  hasPaidFeatures,
  getResourceLimits,
} from '@/app/configs/tier-permissions'
import toast from 'react-hot-toast'
import { isLocalOrDevEnv } from '@/lib/utils'
import { logEventWithContext } from '@/app/libs/logging'
import { UpgradeButton } from '@/app/components/upgrade'
import { useT } from '@/app/i18n/I18nProvider'

type SidebarProps = {
  isOpen: boolean
  onClose: () => void
  session: Session
  /**
   * Optional server-prefetched drag trees so the sidebar can render instantly
   * without firing its own `/api/dragtree/list` request on first mount.
   */
  initialDragTrees?: DragTreeData[]
}

// DragTreeData type is now imported from sidebar-events with Prisma types

const Sidebar: React.FC<SidebarProps> = ({
  isOpen,
  onClose,
  session: initialSession,
  initialDragTrees = [],
}) => {
  const { data: liveSession } = useNextAuthSession()
  const effectiveSession = liveSession ?? initialSession
  const user = effectiveSession.user
  const router = useRouter()
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState<boolean>(false)
  const [dragTrees, setDragTrees] = useState<DragTreeData[]>(initialDragTrees)
  const [isLoading, setIsLoading] = useState<boolean>(
    initialDragTrees.length === 0
  )
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null)
  const [lastFetchTime, setLastFetchTime] = useState<number>(0)
  const [searchQuery, setSearchQuery] = useState('')
  const t = useT('sidebar')

  // Function to fetch drag trees
  const fetchDragTrees = useCallback(
    async (forceRefresh = false) => {
      const now = Date.now()

      // Prevent excessive fetching (less than 2 seconds since last fetch)
      if (!forceRefresh && now - lastFetchTime < 2000) {
        console.log('🔄 Skipping fetch - too recent')
        return
      }

      try {
        setIsLoading(true)
        setLastFetchTime(now)
        const response = await fetch('/api/dragtree/list')
        if (response.ok) {
          const data = await response.json()
          setDragTrees(data)
          setLastUpdated(new Date())
        } else if (response.status === 429) {
          // In development/local, silently ignore to avoid noise from React strict mode
          if (!isLocalOrDevEnv()) {
            const retryAfter = response.headers.get('Retry-After')
            toast.error(
              `Too many requests – please wait ${
                retryAfter ? retryAfter + 's' : 'a few seconds'
              } and try again.`
            )
          }
        } else {
          console.error('Failed to fetch drag trees')
        }
      } catch (error) {
        console.error('Error fetching drag trees:', error)
      } finally {
        setIsLoading(false)
      }
    },
    [lastFetchTime]
  )

  // Function to add a new drag tree to the sidebar (called when creating new trees)
  const addNewDragTree = useCallback((newDragTree: DragTreeData) => {
    console.log('➕ Adding new drag tree to sidebar:', newDragTree.id)
    setDragTrees(prev => [newDragTree, ...prev]) // Add to beginning of list
    setLastUpdated(new Date())
  }, [])

  // Hydrate from server data or fall back to client fetch.
  useEffect(() => {
    if (initialDragTrees.length > 0) {
      // Already have data – no need for an immediate fetch.
      setDragTrees(initialDragTrees)
      setIsLoading(false)
    } else {
      fetchDragTrees()
    }
    // We intentionally exclude fetchDragTrees from deps when we already have initial data
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialDragTrees])

  // Auto-refresh if no refresh in last 5 minutes when sidebar opens
  useEffect(() => {
    if (isOpen) {
      const now = Date.now()
      const fiveMinutesAgo = 5 * 60 * 1000 // 5 minutes in milliseconds

      if (now - lastFetchTime > fiveMinutesAgo) {
        console.log('🕐 Sidebar opened after 5+ minutes, auto-refreshing...')
        fetchDragTrees(true)
      }
    }
  }, [isOpen, lastFetchTime, fetchDragTrees])

  // Listen for sidebar events (new drag trees, updates, refresh requests)
  useEffect(() => {
    const unsubscribeNewTree = sidebarEvents.on(
      'NEW_DRAG_TREE',
      newDragTree => {
        console.log('📢 Sidebar received new drag tree event:', newDragTree.id)
        addNewDragTree(newDragTree)
      }
    )

    const unsubscribeRefresh = sidebarEvents.on('REFRESH_SIDEBAR', () => {
      console.log('📢 Sidebar received refresh event')
      fetchDragTrees(true)
    })

    // Cleanup event listeners on unmount
    return () => {
      unsubscribeNewTree()
      unsubscribeRefresh()
    }
  }, [addNewDragTree, fetchDragTrees])

  // No longer refetch when sidebar opens - users can manually refresh if needed
  // This improves performance and reduces unnecessary API calls

  // Helper function to format timestamp
  const formatTimestamp = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return formatDistanceToNow(dateObj, { addSuffix: true })
  }

  // Helper function to get drag tree status info
  const getStatusInfo = (status: DragTreeStatus) => {
    switch (status) {
      case DragTreeStatus.ACTIVE:
        return { text: 'active', color: 'bg-green-400', icon: null }
      case DragTreeStatus.INITIALIZED:
        return { text: 'initializing', color: 'bg-yellow-400', icon: null }
      default:
        return { text: 'inactive', color: 'bg-gray-300', icon: null }
    }
  }

  // Helper function to get display title
  const getDisplayTitle = (dragTree: DragTreeData) => {
    if (dragTree.title) return dragTree.title
    return 'Untitled Drag Tree'
  }

  // Helper function to get tree summary (simplified)
  const getTreeSummary = (dragTree: DragTreeData) => {
    // Just show the status in a user-friendly way
    switch (dragTree.status) {
      case DragTreeStatus.INITIALIZED:
        return 'Initializing...'
      case DragTreeStatus.GENERATING:
        return 'Generating...'
      default:
        return '' // Empty string but will maintain spacing
    }
  }

  // Handle drag tree click - navigate to the dragTree page with dragTreeId
  const handleDragTreeClick = useCallback(
    (dragTreeId: string) => {
      console.log(`Navigating to drag tree: ${dragTreeId}`)
      router.push(`/dragTree/${dragTreeId}`)
      onClose() // Close sidebar on mobile after navigation
    },
    [router, onClose]
  )

  // Handle new drag tree creation
  const handleNewDragTree = useCallback(() => {
    router.push('/screening') // Navigate to screening page to create new
    onClose() // Close sidebar on mobile after navigation
  }, [router, onClose])

  // Handle upgrade button click
  const handleUpgrade = useCallback(() => {
    const tier = ((effectiveSession?.user as any)?.subscription_tier ||
      (effectiveSession?.user as any)?.subscription?.tier ||
      SubscriptionTier.FREE) as SubscriptionTier

    logEventWithContext(
      'click_upgrade_sidebar',
      effectiveSession?.user?.id,
      undefined,
      {
        source: 'sidebar',
        current_tier: tier,
      }
    )
    onClose() // Close sidebar on mobile after navigation
    router.push('/subscription')
  }, [router, onClose, effectiveSession?.user])

  // Handle manual refresh
  const handleRefresh = useCallback(() => {
    console.log('🔄 Manual refresh triggered')
    fetchDragTrees(true) // Force refresh
  }, [fetchDragTrees])

  const handleProfileClick = () => {
    setIsProfileDialogOpen(true)
  }

  const handleCloseProfileDialog = () => {
    setIsProfileDialogOpen(false)
  }

  // Get active drag trees count
  const activeDragTreesCount = useMemo(
    () =>
      dragTrees.filter(tree => tree.status === DragTreeStatus.ACTIVE).length,
    [dragTrees]
  )

  // Get subscription info and calculate usage for FREE users
  // Always respect DB/session tier directly to avoid mismatched labels
  const tier = ((effectiveSession?.user as any)?.subscription_tier ||
    (effectiveSession?.user as any)?.subscription?.tier ||
    SubscriptionTier.FREE) as SubscriptionTier
  const isPaidUser = hasPaidFeatures(tier)
  const { maxDragTrees } = getResourceLimits(tier)
  const isViewer = tier === SubscriptionTier.VIEWER

  // Calculate remaining drag trees for FREE users
  const remainingDragTrees = useMemo(() => {
    if (tier !== SubscriptionTier.FREE) return null
    const remaining = maxDragTrees - dragTrees.length
    return Math.max(0, remaining)
  }, [tier, maxDragTrees, dragTrees.length])

  // Get button text for New Drag Tree button - commented out as not currently used
  // const getNewDragTreeButtonText = useMemo(() => {
  //   if (tier !== SubscriptionTier.FREE) return 'New Drag Tree'

  //   if (remainingDragTrees === null) return 'New Drag Tree'

  //   if (remainingDragTrees > 0) {
  //     return `New Drag Tree (${remainingDragTrees} available)`
  //   } else {
  //     return 'Used up free quota, consider upgrade'
  //   }
  // }, [tier, remainingDragTrees])

  // Debounced search function
  const debouncedSearch = useMemo(() => {
    const timeoutRef = { current: null as NodeJS.Timeout | null }

    return (query: string) => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
      timeoutRef.current = setTimeout(() => {
        setSearchQuery(query)
      }, 300)
    }
  }, [])

  // Filtered drag trees
  const filteredDragTrees = useMemo(() => {
    if (!searchQuery.trim()) return dragTrees

    return dragTrees.filter(tree => {
      const title = getDisplayTitle(tree).toLowerCase()
      return title.includes(searchQuery.toLowerCase())
    })
  }, [dragTrees, searchQuery])

  return (
    <>
      {/* Desktop/Mobile Sidebar */}
      <aside
        className={cn(
          'fixed inset-y-0 left-0 z-[60] w-80 bg-white border-r border-gray-200 shadow-lg transform transition-transform duration-300 ease-in-out',
          isOpen ? 'translate-x-0' : '-translate-x-full'
        )}
      >
        <div className="flex flex-col h-full">
          {/* Sidebar Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  {t('dragTrees')}
                </h2>
                <p className="text-sm text-gray-500">
                  {t('activeTrees')}: {activeDragTreesCount} (
                  {typeof maxDragTrees === 'number' && maxDragTrees > 0
                    ? `${t('available')}: ${Math.max(0, maxDragTrees - dragTrees.length)}`
                    : `${t('available')}: 0`}
                  )
                </p>
                {lastUpdated && (
                  <p className="text-xs text-gray-400">
                    Updated {formatTimestamp(lastUpdated)}
                  </p>
                )}
              </div>
              <div className="flex flex-col items-center space-y-1">
                <button
                  onClick={onClose}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
                  title="Close sidebar"
                >
                  <MdClose size={20} />
                </button>
                <button
                  onClick={handleRefresh}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
                  title="Refresh drag trees"
                  disabled={isLoading}
                >
                  <FiRefreshCw
                    size={18}
                    className={cn(
                      'transition-transform',
                      isLoading && 'animate-spin'
                    )}
                  />
                </button>
              </div>
            </div>

            {/* New Drag Tree Button */}
            <div className="mt-4 space-y-2">
              <button
                onClick={handleNewDragTree}
                disabled={
                  isViewer ||
                  (tier === SubscriptionTier.FREE && remainingDragTrees === 0)
                }
                className={cn(
                  'w-full flex items-center justify-center px-4 py-2 rounded-lg transition-colors',
                  isViewer ||
                    (tier === SubscriptionTier.FREE && remainingDragTrees === 0)
                    ? 'bg-gray-400 text-white cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                )}
              >
                <MdNoteAdd size={20} className="mr-2" />
                <div className="flex flex-col items-center">
                  <span className="text-sm font-medium">
                    {t('newDragTree')}
                  </span>
                  {tier === SubscriptionTier.FREE && (
                    <span className="text-xs opacity-90">
                      {remainingDragTrees !== null && remainingDragTrees > 0
                        ? `(${remainingDragTrees} ${t('available')})`
                        : remainingDragTrees === 0
                          ? t('quotaUsed')
                          : ''}
                    </span>
                  )}
                  {isViewer && (
                    <span className="text-xs opacity-90">{t('noAccess')}</span>
                  )}
                </div>
              </button>
            </div>
          </div>

          {/* Search Input */}
          <div className="px-6 py-2 border-b border-gray-200">
            <input
              type="text"
              placeholder={t('searchPlaceholder')}
              className="w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              onChange={e => debouncedSearch(e.target.value)}
            />
          </div>

          {/* Drag Trees List */}
          <div className="flex-1 overflow-y-auto px-3 py-4 space-y-2">
            {isLoading ? (
              // Loading skeleton
              <div className="space-y-2">
                {[...Array(5)].map((_, i) => (
                  <div
                    key={i}
                    className="p-4 rounded-lg border border-gray-200 animate-pulse"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="w-4 h-4 bg-gray-300 rounded"></div>
                          <div className="h-4 bg-gray-300 rounded w-32"></div>
                        </div>
                        <div className="h-3 bg-gray-200 rounded mb-2"></div>
                        <div className="h-3 bg-gray-200 rounded w-20"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : dragTrees.length === 0 ? (
              // Empty state
              <div className="text-center py-8">
                <FiFolder className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 text-sm">No drag trees yet</p>
                <p className="text-gray-400 text-xs">
                  Create your first drag tree to get started
                </p>
              </div>
            ) : (
              // Drag trees list
              filteredDragTrees.map(dragTree => {
                const statusInfo = getStatusInfo(dragTree.status)
                const displayTitle = getDisplayTitle(dragTree)
                const treeSummary = getTreeSummary(dragTree)
                const timestamp = formatTimestamp(dragTree.updated_at)

                return (
                  <div
                    key={dragTree.id}
                    onClick={() => handleDragTreeClick(dragTree.id)}
                    className="p-4 rounded-lg border border-gray-200 hover:bg-gray-50 cursor-pointer transition-colors min-h-[100px]"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start space-x-2 mb-2">
                          <FiFolder className="w-4 h-4 text-gray-400 flex-shrink-0 mt-0.5" />
                          <div className="flex-1 min-w-0">
                            <h3
                              className="text-sm font-medium text-gray-900 leading-tight"
                              style={{
                                display: '-webkit-box',
                                WebkitBoxOrient: 'vertical',
                                WebkitLineClamp: 2,
                                overflow: 'hidden',
                                lineHeight: '1.3',
                                maxHeight: '2.6em', // 2 lines * 1.3 line-height
                              }}
                            >
                              {displayTitle}
                            </h3>
                          </div>
                          {dragTree.status === DragTreeStatus.ACTIVE && (
                            <div
                              className={cn(
                                'w-2 h-2 rounded-full flex-shrink-0 mt-1',
                                statusInfo.color
                              )}
                            ></div>
                          )}
                        </div>

                        {treeSummary && (
                          <p className="text-xs text-gray-600 mb-2 min-h-[16px]">
                            {treeSummary}
                          </p>
                        )}
                        {!treeSummary && (
                          <div className="mb-2 min-h-[16px]"></div>
                        )}

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-1 text-xs text-gray-500">
                            <FiClock className="w-3 h-3" />
                            <span>{timestamp}</span>
                          </div>

                          <div className="flex items-center space-x-2">
                            {statusInfo.icon}
                            <span className="text-xs text-gray-500 capitalize">
                              {statusInfo.text}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              })
            )}
          </div>

          {/* Upgrade Button for FREE, GUEST and VIEWER users */}
          {(tier === SubscriptionTier.FREE ||
            tier === SubscriptionTier.GUEST ||
            isViewer) && (
            <div className="px-6 py-3">
              <UpgradeButton
                context="sidebar"
                currentTier={tier}
                onClick={handleUpgrade}
                fullWidth
                size="md"
                className="rounded-lg"
              />
            </div>
          )}

          {/* Footer - Clickable User Info */}
          <div className="px-6 py-4 border-t border-gray-200">
            <div
              onClick={handleProfileClick}
              className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors group"
            >
              <div className="flex-shrink-0 relative">
                {/* eslint-disable-next-line @next/next/no-img-element */}
                <img
                  src={user?.image || '/images/placeholder.jpg'}
                  alt="User"
                  width={40}
                  height={40}
                  className={cn(
                    'w-10 h-10 rounded-full border-2 group-hover:border-gray-300 transition-colors object-cover',
                    isPaidUser
                      ? 'border-purple-300 ring-2 ring-purple-100'
                      : 'border-gray-200'
                  )}
                />
                {/* Pro Badge */}
                {isPaidUser && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-white text-xs font-bold">✦</span>
                  </div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-gray-900 truncate group-hover:text-gray-700">
                    {user?.name || 'User'}
                  </p>
                  {/* Show tier badge for all users */}
                  <span
                    className={cn(
                      'text-xs px-2 py-0.5 rounded-full font-medium',
                      isPaidUser
                        ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                        : 'bg-gray-100 text-gray-700'
                    )}
                  >
                    {tier}
                  </span>
                </div>
                <p className="text-xs text-blue-600 truncate group-hover:text-blue-700 font-medium">
                  {user?.email} →
                </p>
              </div>
            </div>
          </div>
        </div>
      </aside>

      {/* Profile Dialog */}
      <ProfileDialog
        isOpen={isProfileDialogOpen}
        onClose={handleCloseProfileDialog}
        session={effectiveSession ?? initialSession}
      />
    </>
  )
}

export default React.memo(Sidebar)
