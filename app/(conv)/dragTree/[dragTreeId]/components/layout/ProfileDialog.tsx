'use client'

import React, { useState } from 'react'
import { Session } from 'next-auth'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  FiMail,
  FiCreditCard,
  FiLogOut,
  FiStar,
  FiRefreshCw,
} from 'react-icons/fi'
import { signOut, useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { createStripePortal } from '@/lib/stripe/server'
import { hasPaidFeatures } from '@/app/configs/tier-permissions'
import { SubscriptionTier } from '@prisma/client'
import { useUser } from '@/app/context/UserContext'
import { logEventWithContext } from '@/app/libs/logging'
import { useSessionRefresh } from '@/app/hooks/useSessionRefresh'
import { cn } from '@/lib/utils'
import { UpgradeButton } from '@/app/components/upgrade'
import { useT } from '@/app/i18n/I18nProvider'

type ProfileDialogProps = {
  isOpen: boolean
  onClose: () => void
  session: Session
}

const ProfileDialog: React.FC<ProfileDialogProps> = ({
  isOpen,
  onClose,
  session,
}) => {
  const router = useRouter()
  const currentUser = useUser()
  const t = useT('account')
  const { refreshSession, isRefreshing } = useSessionRefresh()
  const { data: liveSession } = useSession()
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const effectiveSession = liveSession ?? session
  const user = effectiveSession.user
  // Note: error is available but not used in this component

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      await signOut({ redirect: false })
      toast.success('Signed out successfully')
      router.push('/')
      onClose()
    } catch {
      // Error handled by showing toast message - no need to use error object
      toast.error('Error signing out')
    } finally {
      setIsLoading(false)
    }
  }

  const handleManageSubscription = async () => {
    setIsLoading(true)
    try {
      logEventWithContext(
        'click_manage_subscription_profile',
        session?.user?.id,
        undefined,
        {
          current_tier: currentUser?.subscription_tier,
          customer_id: currentUser?.subscription_customer_id ?? undefined,
        }
      )
      const redirectUrl = await createStripePortal(currentUser)

      if (typeof redirectUrl === 'string') {
        toast.success('Redirecting to Stripe...')
        window.open(redirectUrl, '_blank')
      } else if (redirectUrl instanceof Error) {
        toast.error(redirectUrl.message)
      } else {
        toast.error('Could not open billing portal. Please try again later.')
      }
    } catch (error) {
      toast.error('Failed to open subscription management')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubscribe = () => {
    logEventWithContext(
      'click_subscribe_profile',
      session?.user?.id,
      undefined,
      {
        current_tier: currentUser?.subscription_tier,
      }
    )
    onClose()
    router.push('/subscription')
  }

  const handleRefreshAccount = async () => {
    logEventWithContext(
      'click_refresh_account_profile',
      session?.user?.id,
      undefined,
      {
        current_tier: currentUser?.subscription_tier,
      }
    )

    await refreshSession({
      successMessage: 'Account refreshed successfully',
      errorMessage: 'Failed to refresh account. Please try again.',
    })
  }

  // Real subscription data from session (prefer structured subscription object, fallback to raw subscription_tier)
  const subscriptionInfo = effectiveSession.user.subscription
  const tier =
    subscriptionInfo?.tier ||
    (effectiveSession.user as any)?.subscription_tier ||
    SubscriptionTier.FREE
  const isPaidUser = hasPaidFeatures(tier)
  const subscriptionEndDate = subscriptionInfo?.expiry
    ? new Date(subscriptionInfo.expiry).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      })
    : null
  const cancelPending = subscriptionInfo?.cancelPending || false

  // Show raw DB tier value for maximum clarity and alignment with backend

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md mx-auto bg-white rounded-2xl shadow-2xl border-0 p-0 overflow-hidden flex flex-col max-h-[85vh]">
        {/* Header with gradient background (non-scrollable) */}
        <div className="bg-gradient-to-br from-purple-600 via-blue-600 to-indigo-700 px-6 py-8 text-white relative overflow-hidden shrink-0">
          <div className="absolute inset-0 bg-black/10"></div>
          <div className="relative z-10">
            <DialogHeader>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  {/* eslint-disable-next-line @next/next/no-img-element */}
                  <img
                    src={user?.image || '/images/placeholder.jpg'}
                    alt="Profile"
                    width={64}
                    height={64}
                    className="w-16 h-16 rounded-full border-4 border-white/30 shadow-lg object-cover"
                  />
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-white flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
                <div className="flex-1">
                  <DialogTitle className="text-xl font-bold text-white mb-1">
                    {user?.name || 'User Name'}
                  </DialogTitle>
                  <DialogDescription className="text-blue-100 text-sm">
                    {t('profile')}
                  </DialogDescription>
                </div>
              </div>
            </DialogHeader>
          </div>
        </div>

        {/* Content (scrollable) */}
        <div className="px-6 py-6 space-y-6 overflow-y-auto">
          {/* User Information */}
          <div className="space-y-4">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                {t('title')}
              </h3>
              <Button
                onClick={handleRefreshAccount}
                disabled={isRefreshing || isLoading}
                variant="outline"
                className="h-8 px-3 text-xs rounded-md border-gray-200 hover:bg-gray-50 transition-colors flex items-center gap-2"
                aria-label="Refresh account"
              >
                <FiRefreshCw
                  className={cn('w-3.5 h-3.5', isRefreshing && 'animate-spin')}
                />
                <span>{isRefreshing ? 'Refreshing…' : 'Refresh'}</span>
              </Button>
            </div>

            {/* Email */}
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <FiMail className="w-5 h-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700">Email</p>
                <p className="text-sm text-gray-900">
                  {user?.email || '<EMAIL>'}
                </p>
              </div>
            </div>

            {/* Subscription Status with optional renewal info */}
            <div className="p-3 bg-gray-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <FiStar className="w-5 h-5 text-purple-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-700">
                    {t('subscription')}
                  </p>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm text-gray-900">{tier}</p>
                    <Badge
                      variant={isPaidUser ? 'default' : 'secondary'}
                      className={`text-xs ${
                        isPaidUser
                          ? 'bg-purple-100 text-purple-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {isPaidUser ? 'Active' : 'Free'}
                    </Badge>
                    {cancelPending && (
                      <Badge
                        variant="outline"
                        className="text-xs text-orange-600 border-orange-200"
                      >
                        Ending Soon
                      </Badge>
                    )}
                  </div>
                  {isPaidUser &&
                    subscriptionEndDate &&
                    tier !== SubscriptionTier.GUEST && (
                      <p className="mt-1 text-xs text-gray-600">
                        {cancelPending ? 'Expires on' : 'Renews on'}{' '}
                        {subscriptionEndDate}
                      </p>
                    )}
                </div>
              </div>
            </div>

            {/* Manage Subscription button moved to here (in place of old Renews card) */}
            {isPaidUser && (
              <div className="pt-1">
                <Button
                  onClick={handleManageSubscription}
                  disabled={isLoading}
                  variant="outline"
                  className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl border-gray-200 hover:bg-gray-50 transition-colors"
                >
                  <FiCreditCard className="w-4 h-4" />
                  <span>
                    {isLoading ? 'Loading...' : 'Manage Subscription'}
                  </span>
                </Button>
              </div>
            )}

            {/* Guest Access Notice */}
            {tier === SubscriptionTier.GUEST && (
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-xl border border-blue-200">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <FiStar className="w-5 h-5 text-blue-600" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-700">
                    Guest Access
                  </p>
                  <p className="text-sm text-blue-600">
                    You have complimentary access to all Pro features
                  </p>
                </div>
              </div>
            )}

            {/* Refresh Account Button moved to header */}
          </div>

          {/* Action Buttons */}
          <div className="space-y-3 pt-4 border-t border-gray-100">
            {/* Subscription Actions */}
            {tier === SubscriptionTier.FREE ||
            tier === SubscriptionTier.GUEST ||
            tier === SubscriptionTier.VIEWER ? (
              <UpgradeButton
                context="profile"
                currentTier={tier}
                onClick={handleSubscribe}
                fullWidth
                size="lg"
                className="h-12 rounded-xl"
              />
            ) : null}

            {/* Account Settings removed – no content to configure currently */}

            {/* Sign Out */}
            <Button
              onClick={handleSignOut}
              disabled={isLoading}
              variant="outline"
              className="w-full flex items-center justify-center space-x-2 h-12 rounded-xl border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300 transition-colors"
            >
              <FiLogOut className="w-4 h-4" />
              <span>{isLoading ? 'Signing out...' : 'Sign Out'}</span>
            </Button>
          </div>

          {/* Footer */}
          <div className="text-center pt-4 border-t border-gray-100">
            <p className="text-xs text-gray-500">
              {(() => {
                const createdAt = (currentUser as any)?.created_at
                if (createdAt) {
                  const d = new Date(createdAt)
                  const y = d.getFullYear()
                  const m = (d.getMonth() + 1).toString().padStart(2, '0')
                  return `Member since ${y} ${m}`
                }
                return `Member since ${new Date().getFullYear()}`
              })()}
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default React.memo(ProfileDialog)
