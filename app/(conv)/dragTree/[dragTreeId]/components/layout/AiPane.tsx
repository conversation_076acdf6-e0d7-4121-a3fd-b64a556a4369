'use client'

import React, { useCallback } from 'react'
import { FiX } from 'react-icons/fi'

import {
  useWorkspaceLayoutStore,
  selectAiPaneSettings,
  selectAiPaneContextTokenCount,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import ControlPanel from '@/app/(conv)/dragTree/[dragTreeId]/components/aiPane/ControlPanel'
// Token counting handled inside child components; avoid importing tokenizer here
import { toast } from 'react-hot-toast'
import { generateAiConversationId } from '@/lib/id'

type AiPaneProps = {
  isOpen: boolean
  onClose: () => void
  dragTreeId: string
}

const AiPane: React.FC<AiPaneProps> = ({ isOpen, onClose, dragTreeId }) => {
  const settings = useWorkspaceLayoutStore(selectAiPaneSettings)
  const contextTokenCount = useWorkspaceLayoutStore(
    selectAiPaneContextTokenCount
  )
  const startAiGeneration = useWorkspaceLayoutStore(
    state => state.startAiGeneration
  )

  const MAX_TOKENS = 20000 // adjustable constant

  // Handle start button click to create new tab
  const handleStart = useCallback(async () => {
    // Get selected context IDs
    const selectedContextIds = settings.context
      .filter(item => item.selected)
      .map(item => item.id)

    // Calculate total tokens
    const approxPromptTokens = Math.ceil(settings.prompt.length / 4)
    const totalTokens = contextTokenCount + approxPromptTokens

    if (totalTokens > MAX_TOKENS) {
      toast.error(
        `Total token size (${totalTokens}) exceeds limit of ${MAX_TOKENS}. Please shorten your prompt or reduce context selection.`
      )
      return
    }

    // Generate conversation ID for chat tabs
    const conversationId =
      settings.type === 'chat' ? generateAiConversationId() : undefined

    // For chat: pre-create the conversation server-side (and optionally system context)
    if (settings.type === 'chat' && conversationId) {
      try {
        const resp = await fetch('/api/aipane/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            conversationId,
            contextEntityType: 'drag_tree',
            contextEntityId: dragTreeId,
            title: 'Untitled chat',
            contextIds: selectedContextIds,
            context: undefined,
            initialUserMessage: settings.prompt,
          }),
        })
        if (!resp.ok) {
          console.warn(
            '[AiPane] Failed to pre-create conversation, proceeding anyway'
          )
        }
      } catch (e) {
        console.warn('[AiPane] Pre-create conversation request failed:', e)
      }
    }

    // Use the consolidated atomic action for AI generation start
    const tabId = startAiGeneration({
      type: settings.type,
      model: settings.model,
      prompt: settings.prompt,
      contextIds: selectedContextIds,
      language: settings.language,
      settings: {
        files: settings.files.map(file => file.name),
        images: settings.images.map(image => image.name),
      },
      ...(conversationId && { conversationId }),
    })

    console.log('AI Pane: Created new tab', tabId)
    onClose() // Close AI pane after starting
  }, [onClose, settings, startAiGeneration, contextTokenCount, dragTreeId])

  // Handle closing the AI pane
  const handleClosePane = useCallback(() => {
    onClose()
  }, [onClose])

  // When used within ResizableLayout, just render the content when open
  if (isOpen) {
    return (
      <div className="h-full bg-white flex flex-col">
        {/* Header - Compact */}
        <div className="px-3 py-2 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-sm font-medium text-gray-900">AI Assistant</h2>
            <button
              onClick={handleClosePane}
              className="p-1 hover:bg-gray-200 rounded-md transition-colors"
            >
              <FiX className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <ControlPanel dragTreeId={dragTreeId} onStart={handleStart} />
        </div>
      </div>
    )
  }

  // When closed, return null (compress trigger is handled in MainContent)
  return null
}

export default React.memo(AiPane)
