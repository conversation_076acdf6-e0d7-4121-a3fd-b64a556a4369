'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import type { ExtractReasoningTitleResult } from '@/components/ai-elements/reasoning'
import { extractReasoningTitle } from '@/components/ai-elements/reasoning'

// Shared helpers for presenting streaming research steps in a timeline layout.

type RawQuickResearchPart = {
  type?: string
  text?: string
  state?: string
  input?: Record<string, unknown>
  output?: unknown
  toolName?: string
  errorText?: string
}

export type QuickResearchTimelinePart =
  | {
      kind: 'reasoning'
      id: string
      source: RawQuickResearchPart
      parsed: ExtractReasoningTitleResult
    }
  | {
      kind: 'tool'
      id: string
      source: RawQuickResearchPart
    }

export const buildQuickResearchTimelineParts = (
  parts: RawQuickResearchPart[] | undefined | null
): QuickResearchTimelinePart[] => {
  if (!Array.isArray(parts)) {
    return []
  }

  return parts.reduce<QuickResearchTimelinePart[]>((acc, part, index) => {
    if (!part || typeof part !== 'object') {
      return acc
    }

    if (part.type === 'reasoning') {
      const text = typeof part.text === 'string' ? part.text : ''
      if (!text.trim()) {
        return acc
      }

      acc.push({
        kind: 'reasoning',
        id: `reasoning-${index}`,
        source: part,
        parsed: extractReasoningTitle(text),
      })
      return acc
    }

    if (typeof part.type === 'string' && part.type.startsWith('tool-')) {
      acc.push({
        kind: 'tool',
        id: `tool-${index}`,
        source: part,
      })
    }

    return acc
  }, [])
}

export type QuickResearchTimelineStepProps = {
  stepNumber: number
  isLast: boolean
  className?: string
  children: React.ReactNode
}

export const QuickResearchTimelineStep: React.FC<
  QuickResearchTimelineStepProps
> = ({ stepNumber, isLast, className, children }) => {
  return (
    <div className={cn('flex items-stretch gap-4', className)}>
      <div className="flex min-w-[1.75rem] flex-col items-center">
        <div className="flex h-6 w-6 items-center justify-center rounded-full border border-sky-200 bg-sky-50 text-[11px] font-semibold text-sky-600 shadow-sm">
          {stepNumber}
        </div>
        {!isLast && (
          <div
            className="mt-1 h-full w-px grow bg-slate-200"
            aria-hidden="true"
          />
        )}
      </div>
      <div className={cn('flex-1 pb-6', isLast && 'pb-0')}>{children}</div>
    </div>
  )
}
