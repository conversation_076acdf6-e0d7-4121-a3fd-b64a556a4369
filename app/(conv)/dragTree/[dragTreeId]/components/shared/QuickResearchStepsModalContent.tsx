'use client'

import React, { useMemo } from 'react'
import {
  Conversation,
  ConversationContent,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'
import {
  QuickResearchTimelineStep,
  buildQuickResearchTimelineParts,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchTimeline'

export type QuickResearchStepsModalContentProps = {
  messages: any[]
}

// Detect whether a message has final assistant text (parts-based or legacy)
const hasAssistantText = (m: any): boolean => {
  if (Array.isArray(m?.parts)) {
    const textParts = m.parts.filter(
      (p: any) =>
        p?.type === 'text' && typeof p?.text === 'string' && p.text.trim()
    )
    if (textParts.length > 0) return true
  }
  if (typeof m?.content === 'string' && m.content.trim()) return true
  if (Array.isArray(m?.content)) {
    const text = m.content
      .filter(
        (p: any) =>
          (p?.type === 'text' && typeof p?.text === 'string') ||
          (p?.type === 'text-delta' &&
            typeof (p as any)?.textDelta === 'string')
      )
      .map((p: any) =>
        p.type === 'text-delta' ? (p as any).textDelta : p.text
      )
      .join('')
      .trim()
    if (text) return true
  }
  return false
}

const hasReasoningParts = (m: any): boolean => {
  return (
    Array.isArray(m?.parts) && m.parts.some((p: any) => p?.type === 'reasoning')
  )
}

const hasToolParts = (m: any): boolean => {
  return (
    Array.isArray(m?.parts) &&
    m.parts.some((p: any) => p?.type?.startsWith('tool-'))
  )
}

export default function QuickResearchStepsModalContent({
  messages,
}: QuickResearchStepsModalContentProps) {
  const filtered = useMemo(() => {
    if (!Array.isArray(messages)) return [] as any[]

    return messages
      .filter((msg, idx) => {
        // Exclude final assistant message if it only contains text (the main answer)
        if (msg.role === 'assistant' && idx === messages.length - 1) {
          if (
            hasAssistantText(msg) &&
            !hasReasoningParts(msg) &&
            !hasToolParts(msg)
          ) {
            return false
          }
        }
        // Include reasoning or tool messages; legacy reasoning supported
        const legacyHasReasoning = Boolean((msg as any).reasoning)
        return (
          (msg.role === 'assistant' &&
            (hasReasoningParts(msg) || hasToolParts(msg))) ||
          legacyHasReasoning
        )
      })
      .map(m => m)
  }, [messages])

  const timelineByMessage = useMemo(
    () =>
      filtered
        .map(message => {
          const legacyReasoningText =
            !Array.isArray(message.parts) &&
            typeof (message as any).reasoning === 'string'
              ? (message as any).reasoning
              : ''

          const normalizedLegacyParts = !Array.isArray(message.parts)
            ? ([
                legacyReasoningText && legacyReasoningText.trim()
                  ? { type: 'reasoning', text: legacyReasoningText }
                  : null,
                ...(((message as any).toolInvocations || []) as any[]).map(
                  (invocation: any) => ({
                    type: `tool-${invocation.toolName ?? 'unknown'}`,
                    toolName: invocation.toolName,
                    state: invocation.state,
                    input: invocation.args,
                    output: invocation.output ?? invocation.result,
                  })
                ),
              ].filter(Boolean) as any[])
            : undefined

          const timelineParts = Array.isArray(message.parts)
            ? buildQuickResearchTimelineParts(message.parts)
            : buildQuickResearchTimelineParts(normalizedLegacyParts)

          return { message, timelineParts }
        })
        .filter(entry => entry.timelineParts.length > 0),
    [filtered]
  )

  const totalTimelineSteps = timelineByMessage.reduce(
    (count, entry) => count + entry.timelineParts.length,
    0
  )

  if (!filtered.length) {
    return (
      <Conversation>
        <ConversationContent>
          <Message from="assistant">
            <MessageContent>
              <div className="text-sm text-gray-500">
                No steps available for display.
              </div>
            </MessageContent>
          </Message>
        </ConversationContent>
      </Conversation>
    )
  }

  return (
    <Conversation>
      <ConversationContent>
        {(() => {
          let processedSteps = 0

          return timelineByMessage.map(({ message, timelineParts }, index) => {
            const role = message.role as 'user' | 'system' | 'assistant'

            // Timeline numbering stays consistent across assistant messages.
            const renderedSteps = timelineParts.map(
              (timelinePart, timelineIndex) => {
                const isLastStep =
                  processedSteps + timelineIndex + 1 === totalTimelineSteps
                const stepNumber = processedSteps + timelineIndex + 1

                if (timelinePart.kind === 'reasoning') {
                  return (
                    <QuickResearchTimelineStep
                      key={timelinePart.id}
                      stepNumber={stepNumber}
                      isLast={isLastStep}
                    >
                      <Reasoning defaultOpen={true}>
                        <ReasoningTrigger title={timelinePart.parsed.title} />
                        <ReasoningContent>
                          {timelinePart.parsed.body ?? ''}
                        </ReasoningContent>
                      </Reasoning>
                    </QuickResearchTimelineStep>
                  )
                }

                const part = timelinePart.source as any
                const toolName =
                  part.toolName || part.type?.replace('tool-', '')
                const searchQuery =
                  (part.input &&
                    ((part.input as any).query ||
                      (part.input as any).keyword)) ||
                  ''

                return (
                  <QuickResearchTimelineStep
                    key={timelinePart.id}
                    stepNumber={stepNumber}
                    isLast={isLastStep}
                  >
                    <details className="mb-3">
                      <summary className="flex cursor-pointer select-none items-center gap-2 text-sm font-medium">
                        <span className="inline-block rounded bg-gray-100 px-2 py-0.5 text-gray-700">
                          {toolName === 'web_search_preview'
                            ? 'Web Search'
                            : toolName}
                        </span>
                        {toolName === 'web_search_preview' && (
                          <span className="text-xs text-gray-500">
                            {searchQuery
                              ? `Searched: ${searchQuery}`
                              : 'Search step'}
                          </span>
                        )}
                        {part.state && (
                          <span className="text-xs text-gray-500">
                            {part.state}
                          </span>
                        )}
                      </summary>
                      <div className="mt-2 space-y-2 text-sm">
                        {part.input && Object.keys(part.input).length > 0 && (
                          <div>
                            <div className="mb-1 text-xs text-gray-600">
                              Input:
                            </div>
                            <pre className="overflow-x-auto rounded bg-gray-100 p-2 text-xs">
                              {JSON.stringify(part.input, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </details>
                  </QuickResearchTimelineStep>
                )
              }
            )

            processedSteps += timelineParts.length

            return (
              <Message key={index} from={role}>
                <MessageContent>{renderedSteps}</MessageContent>
              </Message>
            )
          })
        })()}
      </ConversationContent>
    </Conversation>
  )
}
