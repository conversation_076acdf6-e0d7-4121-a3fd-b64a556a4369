'use client'

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { FiList } from 'react-icons/fi'
import {
  Conversation,
  ConversationContent,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'
import {
  Tool,
  ToolHeader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'
import type { ToolUIPart } from 'ai'

import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import QuickResearchStepsDialogBody from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchStepsDialogBody'
import {
  QuickResearchTimelineStep,
  buildQuickResearchTimelineParts,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchTimeline'

type ResearchStreamingDisplayProps = {
  messages: any[]
  isStreaming: boolean
  className?: string
  onConvertToEditor?: (content: string) => void
}

/**
 * New streaming display component for OpenAI native web search
 * Shows reasoning steps and tool execution in real-time
 * Replaces the textarea phase with AI SDK elements
 *
 * Updated to handle parts-based message format:
 * - message.parts[] with {type: "reasoning", text: "..."}
 * - message.parts[] with {type: "tool-web_search_preview", ...}
 * - message.parts[] with {type: "text", text: "..."}
 */
export const QuickResearchStreamingDisplay: React.FC<
  ResearchStreamingDisplayProps
> = ({ messages, isStreaming, className, onConvertToEditor }) => {
  const [stepsModalOpen, setStepsModalOpen] = useState<boolean>(false)

  // Helper functions to work with parts-based message format
  const hasAssistantText = (m: any): boolean => {
    // Check for text parts in the parts array (new format)
    if (Array.isArray(m?.parts)) {
      const textParts = m.parts.filter(
        (p: any) =>
          p?.type === 'text' && typeof p?.text === 'string' && p.text.trim()
      )
      if (textParts.length > 0) return true
    }

    // Fallback to legacy formats
    if (typeof m?.content === 'string' && m.content.trim()) return true
    if (Array.isArray(m?.content)) {
      const text = m.content
        .filter(
          (p: any) =>
            (p?.type === 'text' && typeof p?.text === 'string') ||
            (p?.type === 'text-delta' &&
              typeof (p as any)?.textDelta === 'string')
        )
        .map((p: any) =>
          p.type === 'text-delta' ? (p as any).textDelta : p.text
        )
        .join('')
        .trim()
      if (text) return true
    }
    return false
  }

  // Helper to check if message has reasoning parts
  const hasReasoningParts = (m: any): boolean => {
    return (
      Array.isArray(m?.parts) &&
      m.parts.some((p: any) => p?.type === 'reasoning')
    )
  }

  // Helper to check if message has tool parts
  const hasToolParts = (m: any): boolean => {
    return (
      Array.isArray(m?.parts) &&
      m.parts.some((p: any) => p?.type?.startsWith('tool-'))
    )
  }

  // Helper to extract text content from parts
  const extractTextFromParts = useCallback((parts: any[]): string => {
    return parts
      .filter((p: any) => p?.type === 'text' && typeof p?.text === 'string')
      .map((p: any) => p.text)
      .join('')
  }, [])

  // Determine if final assistant output has actually started (text tokens present)
  const finalAssistantMessage = messages
    .filter(m => m.role === 'assistant')
    .pop()
  const finalStreamingStarted = Boolean(
    isStreaming &&
      finalAssistantMessage &&
      hasAssistantText(finalAssistantMessage)
  )

  // For pre-final stage: show only the latest assistant message that contains reasoning/tool parts
  const preFinalAssistantMessage = !finalStreamingStarted
    ? [...messages]
        .reverse()
        .find(
          m =>
            m?.role === 'assistant' && (hasReasoningParts(m) || hasToolParts(m))
        )
    : null

  // Count messages for the Steps button (exclude final text-only message)
  const stepsMessageCount = messages.filter((msg, index) => {
    if (msg.role === 'assistant' && index === messages.length - 1) {
      if (
        hasAssistantText(msg) &&
        !hasReasoningParts(msg) &&
        !hasToolParts(msg)
      ) {
        return false
      }
    }
    return (
      msg.role === 'assistant' && (hasReasoningParts(msg) || hasToolParts(msg))
    )
  }).length

  // Get the final assistant response for main display
  // Steps button should appear once final output begins streaming, or after it finishes
  const showStepsButton =
    (finalStreamingStarted || (!isStreaming && stepsMessageCount > 0)) &&
    stepsMessageCount > 0

  const handleStepsClick = useCallback(() => {
    setStepsModalOpen(true)
  }, [])

  const handleConvertToEditor = useCallback(() => {
    if (finalAssistantMessage && onConvertToEditor) {
      let content = ''
      if (Array.isArray(finalAssistantMessage.parts)) {
        content = extractTextFromParts(finalAssistantMessage.parts)
      } else if (typeof finalAssistantMessage.content === 'string') {
        content = finalAssistantMessage.content
      } else if (Array.isArray(finalAssistantMessage.content)) {
        content = finalAssistantMessage.content
          .filter((part: any) => part.type === 'text')
          .map((part: any) => part.text)
          .join(' ')
      }
      onConvertToEditor(content)
    }
  }, [finalAssistantMessage, onConvertToEditor, extractTextFromParts])

  // Automatically convert to editor when streaming completes and we have final assistant text
  React.useEffect(() => {
    if (!isStreaming && finalAssistantMessage && onConvertToEditor) {
      handleConvertToEditor()
    }
  }, [
    isStreaming,
    finalAssistantMessage,
    onConvertToEditor,
    handleConvertToEditor,
  ])

  return (
    <div className={cn('w-full', className)}>
      {/* Main streaming content area */}
      <div className="relative">
        <div className="max-h-[420px] overflow-y-auto pr-1">
          <Conversation className="min-h-[200px]">
            <ConversationContent>
              {/* Before final output starts: show only the latest assistant message with reasoning/tool parts */}
              {!finalStreamingStarted && preFinalAssistantMessage && (
                <Message from={preFinalAssistantMessage.role as 'assistant'}>
                  <MessageContent>
                    {(() => {
                      const timelineParts = buildQuickResearchTimelineParts(
                        preFinalAssistantMessage.parts
                      )
                      if (!timelineParts.length) {
                        return null
                      }

                      // Show reasoning/tool steps within a lightweight timeline while the run streams.
                      return timelineParts.map(
                        (timelinePart, timelineIndex) => {
                          const isLast =
                            timelineIndex === timelineParts.length - 1

                          if (timelinePart.kind === 'reasoning') {
                            return (
                              <QuickResearchTimelineStep
                                key={timelinePart.id}
                                stepNumber={timelineIndex + 1}
                                isLast={isLast}
                              >
                                <Reasoning>
                                  <ReasoningTrigger
                                    title={timelinePart.parsed.title}
                                  />
                                  <ReasoningContent>
                                    {timelinePart.parsed.body ?? ''}
                                  </ReasoningContent>
                                </Reasoning>
                              </QuickResearchTimelineStep>
                            )
                          }

                          const part = timelinePart.source
                          const toolType =
                            typeof part.type === 'string' &&
                            part.type.startsWith('tool-')
                              ? (part.type as ToolUIPart['type'])
                              : ('tool-generic' as ToolUIPart['type'])
                          const validStates: ToolUIPart['state'][] = [
                            'input-streaming',
                            'input-available',
                            'output-available',
                            'output-error',
                          ]
                          const toolState = validStates.includes(
                            part.state as ToolUIPart['state']
                          )
                            ? (part.state as ToolUIPart['state'])
                            : 'output-available'
                          const toolInput = part.input as ToolUIPart['input']
                          const toolOutput = part.output as React.ReactNode
                          const toolError =
                            part.errorText as ToolUIPart['errorText']
                          const hasToolOutput =
                            toolOutput !== undefined && toolOutput !== null
                          return (
                            <QuickResearchTimelineStep
                              key={timelinePart.id}
                              stepNumber={timelineIndex + 1}
                              isLast={isLast}
                            >
                              <Tool className="mb-1">
                                <ToolHeader
                                  type={toolType}
                                  state={toolState}
                                  isExpandable={false}
                                />
                                <ToolContent className="pt-0.5">
                                  {toolType === 'tool-web_search_preview' &&
                                    toolState !== 'output-available' &&
                                    !toolOutput && (
                                      <div className="flex items-center gap-1.5 py-1 text-[11px] text-gray-500">
                                        <div className="h-2.5 w-2.5 animate-spin rounded-full border-b-2 border-gray-500" />
                                        <span>Searching…</span>
                                      </div>
                                    )}
                                  {!!toolInput && (
                                    <ToolInput input={toolInput} />
                                  )}
                                  {(hasToolOutput || toolError) && (
                                    <ToolOutput
                                      output={toolOutput}
                                      errorText={toolError}
                                    />
                                  )}
                                </ToolContent>
                              </Tool>
                            </QuickResearchTimelineStep>
                          )
                        }
                      )
                    })()}
                  </MessageContent>
                </Message>
              )}

              {/* While final output is streaming: show only the final assistant text */}
              {finalStreamingStarted && finalAssistantMessage && (
                <Message from="assistant">
                  <MessageContent>
                    {Array.isArray(finalAssistantMessage.parts) ? (
                      finalAssistantMessage.parts.map(
                        (part: any, partIndex: number) =>
                          part.type === 'text' && part.text ? (
                            <Response key={partIndex}>{part.text}</Response>
                          ) : null
                      )
                    ) : typeof finalAssistantMessage.content === 'string' ? (
                      <Response>{finalAssistantMessage.content}</Response>
                    ) : Array.isArray(finalAssistantMessage.content) ? (
                      <Response>
                        {finalAssistantMessage.content
                          .filter(
                            (part: any) =>
                              part.type === 'text' || part.type === 'text-delta'
                          )
                          .map((part: any) =>
                            part.type === 'text-delta'
                              ? (part as any).textDelta
                              : part.text
                          )
                          .join(' ')}
                      </Response>
                    ) : null}
                  </MessageContent>
                </Message>
              )}

              {/* Show loading indicator when streaming */}
              {isStreaming &&
                !finalStreamingStarted &&
                !preFinalAssistantMessage && (
                  <Message from="assistant">
                    <MessageContent>
                      <div className="flex items-center gap-2 text-gray-500">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                        <span>Researching...</span>
                      </div>
                    </MessageContent>
                  </Message>
                )}
            </ConversationContent>
          </Conversation>
        </div>

        {/* Steps button - positioned at bottom right when not streaming */}
        {showStepsButton && (
          <div className="absolute bottom-2 right-2">
            <Dialog open={stepsModalOpen} onOpenChange={setStepsModalOpen}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleStepsClick}
                  className="h-8 px-3 text-xs bg-white/80 backdrop-blur-sm"
                >
                  <FiList className="h-3 w-3 mr-1" />
                  Steps ({stepsMessageCount})
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] p-0">
                <QuickResearchStepsDialogBody messages={messages} />
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>

      {/* Conversion to editor happens automatically via effect when streaming completes */}
    </div>
  )
}
