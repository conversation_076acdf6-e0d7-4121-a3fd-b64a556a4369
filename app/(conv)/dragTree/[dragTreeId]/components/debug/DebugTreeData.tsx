'use client'

import React, { useState, useEffect } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { getDragTree } from '@/app/server-actions/drag-tree'
import { Button } from '@/components/ui/button'

const DebugTreeData: React.FC = () => {
  const dragTreeId = useDragTreeStore(s => s.dragTreeId)
  const [dbData, setDbData] = useState<any>(null)
  const [missingNodes, setMissingNodes] = useState<string[]>([])

  const loadDbData = async () => {
    if (!dragTreeId) return

    try {
      const result = await getDragTree(dragTreeId)
      if (result.success && result.data) {
        setDbData(result.data)

        // Analyze missing nodes
        const hierarchy = (result.data.tree_structure as any)?.hierarchy || {}
        const nodes = result.data.nodes || []
        const nodeIds = new Set(nodes.map((n: any) => n.id))

        const allReferencedNodes = new Set<string>()
        Object.keys(hierarchy).forEach(parentId => {
          allReferencedNodes.add(parentId)
          hierarchy[parentId].forEach((childId: string) => {
            allReferencedNodes.add(childId)
          })
        })

        const missing = Array.from(allReferencedNodes).filter(
          id => !nodeIds.has(id)
        )
        setMissingNodes(missing)

        console.log('🔍 Debug Analysis:')
        console.log('- Total nodes in database:', nodes.length)
        console.log('- Total referenced in hierarchy:', allReferencedNodes.size)
        console.log('- Missing nodes:', missing)
      }
    } catch (error) {
      console.error('Failed to load debug data:', error)
    }
  }

  useEffect(() => {
    if (dragTreeId) {
      loadDbData()
    }
  }, [dragTreeId])

  if (!dragTreeId) {
    return <div className="p-4 text-gray-500">No drag tree ID available</div>
  }

  return (
    <div className="p-4 border rounded-lg bg-yellow-50 border-yellow-200">
      <h3 className="text-lg font-semibold mb-4">🔍 Debug Tree Data</h3>

      <Button onClick={loadDbData} className="mb-4">
        Refresh Debug Data
      </Button>

      {dbData && (
        <div className="space-y-4">
          <div>
            <strong>Tree ID:</strong> {dragTreeId}
          </div>

          <div>
            <strong>Root ID:</strong> {(dbData.tree_structure as any)?.root_id}
          </div>

          <div>
            <strong>Total Nodes in DB:</strong> {dbData.nodes?.length || 0}
          </div>

          <div>
            <strong>Total Hierarchy Entries:</strong>{' '}
            {
              Object.keys((dbData.tree_structure as any)?.hierarchy || {})
                .length
            }
          </div>

          {missingNodes.length > 0 && (
            <div className="bg-red-50 border border-red-200 p-3 rounded">
              <strong className="text-red-700">
                ❌ Missing Nodes ({missingNodes.length}):
              </strong>
              <ul className="mt-2 text-sm">
                {missingNodes.map(nodeId => (
                  <li key={nodeId} className="text-red-600 font-mono">
                    {nodeId}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {missingNodes.length === 0 && (
            <div className="bg-green-50 border border-green-200 p-3 rounded">
              <strong className="text-green-700">
                ✅ All nodes found in database
              </strong>
            </div>
          )}

          <details className="mt-4">
            <summary className="cursor-pointer font-semibold">
              Show Raw Database Data
            </summary>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-96">
              {JSON.stringify(dbData, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  )
}

export default DebugTreeData
