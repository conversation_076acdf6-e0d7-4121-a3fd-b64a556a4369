'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ChevronRight, X, <PERSON>Right, ArrowLeft } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useWorkspaceLayoutStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'

type TutorialStep = {
  id: string
  title: string
  description: string
  targetId?: string
  targetSelector?: string
  position?: 'top' | 'bottom' | 'left' | 'right'
  action?: string
}

type TutorialOverlayProps = {
  isVisible: boolean
  onComplete: () => void
  onSkip: () => void
  targetNodeId?: string
}

/**
 * Tutorial Overlay Component
 *
 * Provides interactive step-by-step guidance for users.
 * Features contextual highlighting and progressive disclosure.
 */
const TutorialOverlay: React.FC<TutorialOverlayProps> = ({
  isVisible,
  onComplete,
  onSkip,
  targetNodeId,
}) => {
  const [currentStepIndex, setCurrentStepIndex] = useState<number>(0)
  const [steps, setSteps] = useState<TutorialStep[]>([])
  const [highlightRect, setHighlightRect] = useState<DOMRect | null>(null)

  // AI Pane store for controlling visibility during tutorial
  const setIsAiPaneOpen = useWorkspaceLayoutStore(state => state.setIsOpen)
  const [aiPaneWasOpenBeforeTutorial, setAiPaneWasOpenBeforeTutorial] =
    useState<boolean>(false)

  useEffect(() => {
    if (isVisible) {
      // Store initial AI pane state when tutorial starts
      const currentAiPaneState = useWorkspaceLayoutStore.getState().isOpen
      setAiPaneWasOpenBeforeTutorial(currentAiPaneState)

      // Context-aware tutorial steps that guide users through the DragTree interface
      // 7 steps total (6 base + 1 conditional feedback step)
      const baseSteps: TutorialStep[] = [
        {
          id: 'welcome',
          title: 'Welcome to DragTree! 🌳',
          description:
            "This interactive tree helps you explore and research complex topics. Let's take a quick tour of the key features.",
          targetId: 'tutorial-whole-page',
          position: 'bottom',
          action: 'Click Next to start the guided tour',
        },
        {
          id: 'outline-panel',
          title: 'Tree Structure Panel',
          description:
            'This panel shows your topic breakdown as an interactive tree. You can expand/collapse categories and generate AI research for any question.',
          targetId: 'tutorial-outline-panel',
          position: 'right',
          action: 'Try clicking on different categories to expand them',
        },
        {
          id: 'reactflow-panel',
          title: 'Visual Flow Diagram',
          description:
            'This panel provides a visual representation of your tree structure. You can switch between hierarchical and circular layouts, and export diagrams as images.',
          targetId: 'tutorial-reactflow-panel',
          position: 'left',
          action: 'Look for the layout toggle and export options',
        },
        {
          id: 'resize-handle',
          title: 'Adjustable Layout',
          description:
            'Drag this handle to resize the panels. You can adjust the layout to focus on either the tree structure or the visual diagram.',
          targetId: 'tutorial-resize-handle',
          position: 'bottom',
          action: 'Try dragging the handle to resize the panels',
        },
        {
          id: 'header-actions',
          title: 'More Options & Export',
          description:
            'Click here to access metadata, export options, and additional features. You can export your tree as markdown or download flow diagrams.',
          targetId: 'tutorial-node-actions',
          position: 'bottom',
          action: 'Click to explore export and sharing options',
        },
        {
          id: 'ai-chat-pane',
          title: 'AI Chat & Research ✨',
          description:
            'This AI chat pane lets you have conversations with AI and generate research content with real-time web search. Click the right edge to open it and start exploring!',
          targetId: 'tutorial-ai-pane',
          position: 'left',
          action: 'Click the right edge handle to open the AI assistant!',
        },
        {
          id: 'happy-thinking',
          title: 'Happy thinking! 🎉',
          description:
            "You're all set. Explore your DragTree and let your ideas flow.",
          targetId: 'tutorial-whole-page', // Centered dialog, no highlight
          position: 'bottom',
        },
      ]

      // Conditionally add feedback step if in development mode
      const isDevelopment = process.env.NODE_ENV === 'development'
      const contextualSteps: TutorialStep[] = isDevelopment
        ? [
            ...baseSteps.slice(0, -1), // All steps except the last one
            {
              id: 'feedback-widget',
              title: 'Share Your Feedback 💭',
              description:
                'Found a bug or have suggestions? Click this feedback button in the bottom-right corner to share your thoughts with us. Your feedback helps us improve the experience!',
              targetSelector: '[aria-label="Open feedback widget"]',
              position: 'left',
              action:
                'Try clicking the feedback button to see the feedback form',
            },
            baseSteps[baseSteps.length - 1], // Add the last step back
          ]
        : baseSteps

      setSteps(contextualSteps)
      setCurrentStepIndex(0)
    }
  }, [isVisible, targetNodeId])

  const currentStep = steps[currentStepIndex]
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === steps.length - 1

  // Handle AI pane visibility when navigating to/from Step 6 (AI pane step)
  useEffect(() => {
    if (!isVisible || !currentStep) return

    // Step 6 is the AI pane step (index 5, since we start from 0)
    const isAiPaneStep =
      currentStepIndex === 5 && currentStep.targetId === 'tutorial-ai-pane'

    if (isAiPaneStep) {
      // Open AI pane for Step 6 to ensure the target element exists
      setIsAiPaneOpen(true)
    }
  }, [currentStepIndex, currentStep, isVisible, setIsAiPaneOpen])

  // Update highlight position when step changes
  useEffect(() => {
    if (!isVisible || !currentStep) return

    const updateHighlight = () => {
      // Special case for whole page - no highlight needed
      if (currentStep.targetId === 'tutorial-whole-page') {
        setHighlightRect(null)
        return
      }

      const targetElement = currentStep.targetId
        ? document.getElementById(currentStep.targetId)
        : currentStep.targetSelector
          ? document.querySelector(currentStep.targetSelector)
          : null
      if (targetElement) {
        const rect = targetElement.getBoundingClientRect()
        setHighlightRect(rect)
      } else {
        // If element not found, set a default position
        console.warn(
          `Tutorial target element not found: ${currentStep.targetId || currentStep.targetSelector}`
        )
        setHighlightRect(null)
      }
    }

    // For AI pane step, add extra delay to ensure the pane is fully rendered
    const isAiPaneStep = currentStep.targetId === 'tutorial-ai-pane'
    const delay = isAiPaneStep ? 300 : 100

    // Initial update with a slight delay to ensure DOM is ready
    const timeoutId = setTimeout(updateHighlight, delay)

    // Update on resize
    window.addEventListener('resize', updateHighlight)
    return () => {
      window.removeEventListener('resize', updateHighlight)
      clearTimeout(timeoutId)
    }
  }, [currentStep, isVisible])

  // Add keyboard navigation
  useEffect(() => {
    if (!isVisible) return

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowRight':
        case 'ArrowDown':
          e.preventDefault()
          // Don't allow keyboard navigation to close tutorial on last step
          if (!isLastStep) {
            handleNext()
          }
          break
        case 'ArrowLeft':
        case 'ArrowUp':
          e.preventDefault()
          handlePrevious()
          break
        case 'Escape':
          e.preventDefault()
          handleSkip()
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isVisible, currentStepIndex, isLastStep])

  const handleNext = () => {
    if (!isLastStep) {
      setCurrentStepIndex(prev => prev + 1)
    } else {
      // Tutorial completed - restore AI pane state
      if (!aiPaneWasOpenBeforeTutorial) {
        setIsAiPaneOpen(false)
      }
      onComplete()
    }
  }

  const handlePrevious = () => {
    if (!isFirstStep) {
      setCurrentStepIndex(prev => prev - 1)
    }
  }

  const handleSkip = () => {
    // Tutorial skipped - restore AI pane state
    if (!aiPaneWasOpenBeforeTutorial) {
      setIsAiPaneOpen(false)
    }
    onSkip()
  }

  if (!isVisible || !currentStep) {
    return null
  }

  // Calculate tooltip position with improved positioning logic
  const getTooltipPosition = () => {
    // Special case for whole page - always center
    if (currentStep.targetId === 'tutorial-whole-page') {
      return {
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        position: 'fixed' as const,
      }
    }

    if (!highlightRect) {
      // Fallback position when no highlight rect is available
      return {
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        position: 'fixed' as const,
      }
    }

    const { position } = currentStep
    const padding = 20
    const tooltipWidth = 384 // max-w-sm = 24rem = 384px
    const tooltipHeight = 250 // estimated height for better spacing
    const viewportHeight = window.innerHeight
    const viewportWidth = window.innerWidth

    // Calculate base position
    let top = 0
    let left = 0
    let transform = ''

    switch (position) {
      case 'top':
        top = highlightRect.top - tooltipHeight - padding
        left = highlightRect.left + highlightRect.width / 2
        transform = 'translateX(-50%)'
        // Prevent going off top of screen
        if (top < padding) {
          top = highlightRect.bottom + padding
        }
        break
      case 'bottom':
        top = highlightRect.bottom + padding
        left = highlightRect.left + highlightRect.width / 2
        transform = 'translateX(-50%)'
        // Prevent going off bottom of screen
        if (top + tooltipHeight > viewportHeight - padding) {
          top = highlightRect.top - tooltipHeight - padding
        }
        break
      case 'left':
        top = highlightRect.top + highlightRect.height / 2
        left = highlightRect.left - tooltipWidth - padding
        transform = 'translateY(-50%)'
        // Prevent going off left of screen
        if (left < padding) {
          left = highlightRect.right + padding
        }
        break
      case 'right':
        top = highlightRect.top + highlightRect.height / 2
        left = highlightRect.right + padding
        transform = 'translateY(-50%)'
        // Prevent going off right of screen
        if (left + tooltipWidth > viewportWidth - padding) {
          left = highlightRect.left - tooltipWidth - padding
        }
        break
      default:
        // Center fallback
        top = highlightRect.top + highlightRect.height / 2
        left = highlightRect.left + highlightRect.width / 2
        transform = 'translate(-50%, -50%)'
    }

    // Final bounds checking to ensure tooltip stays on screen
    if (left < padding) left = padding
    if (left + tooltipWidth > viewportWidth - padding) {
      left = viewportWidth - tooltipWidth - padding
    }
    if (top < padding) top = padding
    if (top + tooltipHeight > viewportHeight - padding) {
      top = viewportHeight - tooltipHeight - padding
    }

    return {
      top: `${top}px`,
      left: `${left}px`,
      transform,
      position: 'fixed' as const,
    }
  }

  const tooltipStyle = getTooltipPosition()

  return (
    <div className="fixed inset-0 z-[100] pointer-events-none">
      {/* Dark overlay */}
      <div className="absolute inset-0 bg-black bg-opacity-50 pointer-events-auto">
        {/* Click to skip tutorial */}
        <div
          className="absolute inset-0 cursor-pointer"
          onClick={handleSkip}
          title="Click anywhere to skip tutorial"
        />
      </div>

      {/* Highlight rectangle */}
      {highlightRect && (
        <div
          className="absolute border-4 border-blue-400 rounded-lg pointer-events-none animate-pulse"
          style={{
            top: `${highlightRect.top}px`,
            left: `${highlightRect.left}px`,
            width: `${highlightRect.width}px`,
            height: `${highlightRect.height}px`,
            boxShadow: '0 0 0 4px rgba(59, 130, 246, 0.3)',
          }}
        />
      )}

      {/* Tooltip */}
      <div
        className="absolute pointer-events-auto max-w-sm bg-white rounded-lg shadow-2xl border border-gray-200 z-[110]"
        style={tooltipStyle}
      >
        {/* Progress indicator */}
        <div className="w-full bg-gray-200 rounded-t-lg">
          <div
            className="bg-blue-500 h-1 rounded-t-lg transition-all duration-300"
            style={{
              width: `${((currentStepIndex + 1) / steps.length) * 100}%`,
            }}
          />
        </div>

        <div className="p-6">
          {/* Step counter */}
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-blue-600">
              Step {currentStepIndex + 1} of {steps.length}
            </span>
            <button
              onClick={handleSkip}
              className="text-gray-400 hover:text-gray-600 transition-colors"
              title="Skip tutorial"
            >
              <X className="w-4 h-4" />
            </button>
          </div>

          {/* Content */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {currentStep.title}
            </h3>
            <p className="text-gray-600 text-sm leading-relaxed mb-3">
              {currentStep.description}
            </p>
            {currentStep.action && (
              <p className="text-blue-600 text-sm font-medium">
                💡 {currentStep.action}
              </p>
            )}
          </div>

          {/* Navigation buttons */}
          <div className="flex items-center justify-between">
            <button
              onClick={handlePrevious}
              disabled={isFirstStep}
              className={cn(
                'flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors',
                isFirstStep
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
              )}
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Previous</span>
            </button>

            <div className="flex items-center space-x-3">
              {!isLastStep && (
                <button
                  onClick={handleSkip}
                  className="px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors"
                >
                  Skip tour
                </button>
              )}
              {!isLastStep ? (
                <Button
                  onClick={handleNext}
                  className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700"
                >
                  <span>Next</span>
                  <ArrowRight className="w-4 h-4" />
                </Button>
              ) : (
                <div className="flex flex-col items-end space-y-2">
                  <p className="text-xs text-gray-600 text-right">
                    Tutorial complete! Click below to start exploring.
                  </p>
                  <Button
                    onClick={handleNext}
                    className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700"
                  >
                    <span>Start Exploring</span>
                    <ChevronRight className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TutorialOverlay
