'use client'
import React from 'react'
import { Button } from '@/components/ui/button'
import { LayoutGrid, Target, Share2, Box } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSession } from 'next-auth/react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { logEventWithContext } from '@/app/libs/logging'
import { useT } from '@/app/i18n/I18nProvider'
import {
  ENABLE_DRAGTREE_FORCE_GRAPH_2D,
  ENABLE_DRAGTREE_FORCE_GRAPH_3D,
  ENABLE_DRAGTREE_ISSUE_TREE,
  ENABLE_DRAGTREE_ISSUE_TREE_RADIAL,
} from '@/app/configs/feature-flags'

type LayoutMode = 'linear' | 'radial' | 'force' | 'force3d'

type LayoutModeToggleProps = {
  layoutMode: LayoutMode
  setLayoutMode: (mode: LayoutMode) => void
}

export const LayoutModeToggle: React.FC<LayoutModeToggleProps> = React.memo(
  function LayoutModeToggle({ layoutMode, setLayoutMode }) {
    const { data: session } = useSession()
    const dragTreeId = useDragTreeStore(state => state.dragTreeId)
    const t = useT('dragtree.flow')

    const handleLayoutModeChange = (mode: LayoutMode) => {
      // Respect feature flags: ignore clicks for disabled modes
      const isIssueTreeMode = mode === 'linear' || mode === 'radial'
      if (
        (isIssueTreeMode && !ENABLE_DRAGTREE_ISSUE_TREE) ||
        (mode === 'force' && !ENABLE_DRAGTREE_FORCE_GRAPH_2D) ||
        (mode === 'force3d' && !ENABLE_DRAGTREE_FORCE_GRAPH_3D)
      ) {
        return
      }
      // Log the layout mode change event
      const eventName =
        mode === 'linear'
          ? 'click_reactflow_hierarchical'
          : mode === 'radial'
            ? 'click_reactflow_circular'
            : mode === 'force'
              ? 'click_force_graph'
              : 'click_force_graph_3d'

      logEventWithContext(
        eventName,
        session?.user?.id,
        dragTreeId || undefined,
        {
          previous_mode: layoutMode,
          new_mode: mode,
        }
      )

      setLayoutMode(mode)
    }

    return (
      <div className="absolute top-4 left-4 z-10">
        <div className="flex items-center bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-lg p-1 shadow-sm">
          {ENABLE_DRAGTREE_ISSUE_TREE && (
            <Button
              variant={layoutMode === 'linear' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleLayoutModeChange('linear')}
              className={cn(
                'h-8 px-3 text-xs font-medium transition-all duration-200',
                layoutMode === 'linear'
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
              )}
            >
              <LayoutGrid className="w-3 h-3 mr-1.5" />
              {t('hierarchical', 'Hierarchical')}
            </Button>
          )}
          {ENABLE_DRAGTREE_ISSUE_TREE && ENABLE_DRAGTREE_ISSUE_TREE_RADIAL && (
            <Button
              variant={layoutMode === 'radial' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleLayoutModeChange('radial')}
              className={cn(
                'h-8 px-3 text-xs font-medium transition-all duration-200',
                layoutMode === 'radial'
                  ? 'bg-purple-500 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
              )}
            >
              <Target className="w-3 h-3 mr-1.5" />
              {t('circular', 'Circular')}
            </Button>
          )}
          {ENABLE_DRAGTREE_FORCE_GRAPH_2D && (
            <Button
              variant={layoutMode === 'force' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleLayoutModeChange('force')}
              className={cn(
                'h-8 px-3 text-xs font-medium transition-all duration-200',
                layoutMode === 'force'
                  ? 'bg-emerald-500 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
              )}
            >
              <Share2 className="w-3 h-3 mr-1.5" />
              {t('force', 'Force')}
            </Button>
          )}
          {ENABLE_DRAGTREE_FORCE_GRAPH_3D && (
            <Button
              variant={layoutMode === 'force3d' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => handleLayoutModeChange('force3d')}
              className={cn(
                'h-8 px-3 text-xs font-medium transition-all duration-200',
                layoutMode === 'force3d'
                  ? 'bg-cyan-600 text-white shadow-sm'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
              )}
            >
              <Box className="w-3 h-3 mr-1.5" />
              {t('force3d', 'Force 3D')}
            </Button>
          )}
        </div>
      </div>
    )
  }
)
