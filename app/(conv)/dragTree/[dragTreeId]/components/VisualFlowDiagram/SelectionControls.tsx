'use client'
import React from 'react'
import { Button } from '@/components/ui/button'
import { MessageSquare, X, ArrowLeft, List } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { useWorkspaceLayoutStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { useSelectionToContext } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useSelectionToContext'

type SelectionControlsProps = {
  className?: string
}

/**
 * SelectionControls Component
 *
 * Shows selection counter and "Use Selected as Context" button when nodes are selected.
 * Only visible when in selection mode and nodes are selected.
 */
export const SelectionControls: React.FC<SelectionControlsProps> = ({
  className,
}) => {
  const isSelectionMode = useSelectionStore(state => state.isSelectionMode)
  const selectedCount = useSelectionStore(state => state.getSelectedCount())
  const nonResearchedNodeIds = useSelectionStore(
    state => state.nonResearchedNodeIds
  )
  const clearSelectedNodes = useSelectionStore(
    state => state.clearSelectedNodes
  )
  const setSelectionMode = useSelectionStore(state => state.setSelectionMode)
  const setShowContextSelectionModal = useSelectionStore(
    state => state.setShowContextSelectionModal
  )

  const setIsAiPaneOpen = useWorkspaceLayoutStore(state => state.setIsOpen)

  const { applySelectionAsContext, getResearchedSelectionCount } =
    useSelectionToContext()

  // Get count of researched nodes in selection
  const researchedCount = getResearchedSelectionCount()
  const nonResearchedCount = nonResearchedNodeIds.size

  // Don't render if not in selection mode or no nodes selected (including non-researched)
  if (!isSelectionMode || (selectedCount === 0 && nonResearchedCount === 0)) {
    return null
  }

  const handleUseAsContext = async () => {
    try {
      // Apply selection as context first (now async to ensure content loading)
      await applySelectionAsContext()

      // Open AI pane
      setIsAiPaneOpen(true)

      // Exit selection mode and clear selections
      setSelectionMode(false)
    } catch (error) {
      console.error('Error applying selection as context:', error)
    }
  }

  const handleClearSelection = () => {
    clearSelectedNodes()
  }

  const handleExitSelection = () => {
    setSelectionMode(false)
  }

  const handleViewSelectedNodes = async () => {
    // Open the context selection modal
    // Ensure researched nodes are preloaded into context selection
    await applySelectionAsContext()
    setShowContextSelectionModal(true)
    setIsAiPaneOpen(true)
  }

  return (
    <div
      className={cn(
        'absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20',
        'bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg shadow-lg',
        'px-3 py-2 flex flex-wrap items-center gap-2',
        className
      )}
      role="toolbar"
      aria-label="Selection controls"
    >
      {/* Selection counter */}
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        <span className="text-sm font-medium text-gray-700">
          {selectedCount} item{selectedCount !== 1 ? 's' : ''} selected
          {researchedCount < selectedCount && (
            <span className="text-xs text-amber-600 ml-1">
              ({researchedCount} researched)
            </span>
          )}
        </span>
      </div>

      {/* Action buttons */}
      <div className="flex flex-wrap items-center gap-1 border-l border-gray-200 pl-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleViewSelectedNodes}
          className="h-7 px-2 text-[11px] text-blue-600 hover:text-blue-800"
          title="View selected nodes list"
          aria-label="View selected nodes"
        >
          <List className="w-3 h-3 mr-1" />
          View
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleExitSelection}
          className="h-7 px-2 text-[11px] text-gray-600 hover:text-gray-800"
          title="Exit selection mode (Esc)"
          aria-label="Exit selection mode"
        >
          <ArrowLeft className="w-3 h-3 mr-1" />
          Exit
        </Button>

        <Button
          variant="outline"
          size="sm"
          onClick={handleClearSelection}
          className="h-7 px-2 text-[11px]"
          title="Clear selection (Delete/Backspace)"
          aria-label="Clear selection"
        >
          <X className="w-3 h-3 mr-1" />
          Clear
        </Button>

        <Button
          size="sm"
          onClick={handleUseAsContext}
          disabled={researchedCount === 0}
          className="h-7 px-2 text-[11px] bg-blue-500 hover:bg-blue-600 text-white disabled:bg-gray-300 disabled:text-gray-500"
          title={
            researchedCount === 0
              ? 'No researched nodes selected'
              : 'Use selected nodes as AI context (Enter)'
          }
          aria-label={
            researchedCount === 0
              ? 'No researched nodes selected'
              : 'Use selected nodes as AI context'
          }
        >
          <MessageSquare className="w-3 h-3 mr-1" />
          Use as Context
          {researchedCount > 0 && researchedCount !== selectedCount && (
            <span className="ml-1 text-xs">({researchedCount})</span>
          )}
        </Button>
      </div>
    </div>
  )
}
