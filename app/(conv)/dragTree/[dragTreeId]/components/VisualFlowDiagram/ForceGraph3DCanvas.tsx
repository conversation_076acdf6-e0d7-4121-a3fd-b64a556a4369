'use client'

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  Suspense,
  lazy,
} from 'react'
import { LayoutModeToggle } from './LayoutModeToggle'
import { useForceGraphData } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useForceGraphData'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import * as THREE from 'three'
// @ts-ignore - types are bundled with the lib
import SpriteText from 'three-spritetext'

const ForceGraph3D = lazy(() =>
  import('react-force-graph-3d').then(m => ({ default: m.default }))
)

type Props = {
  layoutMode: 'force3d' | 'force' | 'linear' | 'radial'
  setLayoutMode: (m: 'force3d' | 'force' | 'linear' | 'radial') => void
  dragTreeId: string
}

const ForceGraph3DCanvas: React.FC<Props> = ({ layoutMode, setLayoutMode }) => {
  const { nodes, links } = useForceGraphData()
  const fgRef = useRef<any>(null)
  const [size, setSize] = useState<{ width: number; height: number }>({
    width: 800,
    height: 600,
  })
  const containerRef = useRef<HTMLDivElement | null>(null)
  const targetNodeId = useNavigationStore(s => s.targetNodeId)
  const [hoveredId, setHoveredId] = useState<string | null>(null)
  const [selectedId, setSelectedId] = useState<string | null>(null)
  const didFitRef = useRef(false)

  useEffect(() => {
    if (!containerRef.current) return
    const el = containerRef.current
    const ro = new ResizeObserver(entries => {
      for (const entry of entries) {
        const cr = entry.contentRect
        setSize({
          width: Math.max(200, cr.width),
          height: Math.max(200, cr.height),
        })
      }
    })
    ro.observe(el)
    return () => ro.disconnect()
  }, [])

  const data = useMemo(() => ({ nodes, links }), [nodes, links])

  // Adjust camera distance along the look vector for smoother zoom behavior
  const handleZoomControl = useCallback((direction: 'in' | 'out') => {
    const graph = fgRef.current
    if (!graph) return
    const camera = typeof graph.camera === 'function' ? graph.camera() : null
    const controls =
      typeof graph.controls === 'function' ? graph.controls() : null
    if (!camera || !controls) return

    const offset = new THREE.Vector3().subVectors(
      camera.position,
      controls.target
    )
    const currentDistance = offset.length()
    const zoomFactor = direction === 'in' ? 0.8 : 1.25
    const minDistance = 80
    const maxDistance = 4000
    const nextDistance = THREE.MathUtils.clamp(
      currentDistance * zoomFactor,
      minDistance,
      maxDistance
    )
    const nextPosition = offset
      .normalize()
      .multiplyScalar(nextDistance)
      .add(controls.target)

    graph.cameraPosition(
      { x: nextPosition.x, y: nextPosition.y, z: nextPosition.z },
      { x: controls.target.x, y: controls.target.y, z: controls.target.z },
      450
    )
  }, [])

  const handleResetCamera = useCallback(() => {
    const graph = fgRef.current
    if (!graph) return
    try {
      graph.zoomToFit(900, 160)
    } catch {}
  }, [])

  // Custom 3D object: sphere + transparent label
  const nodeThreeObject = useCallback(
    (node: any) => {
      const group = new THREE.Group()
      const isRoot = node.level === 1
      const isCategory = node.type === 'category'
      const color = isRoot ? 0xf59e0b : isCategory ? 0x7c3aed : 0x10b981
      const sphereGeom = new THREE.SphereGeometry(
        isRoot ? 10 : 4 + (node.level || 1),
        16,
        16
      )
      const sphereMat = new THREE.MeshStandardMaterial({
        color,
        roughness: 0.55,
        metalness: 0.15,
        emissive: isRoot
          ? new THREE.Color(0xf59e0b)
          : new THREE.Color(0x000000),
        emissiveIntensity: isRoot ? 0.25 : 0.0,
      })
      const sphere = new THREE.Mesh(sphereGeom, sphereMat)
      group.add(sphere)

      const label: any = new (SpriteText as any)(node.name || node.id, 8)
      label.color = isCategory ? '#312e81' : '#334155'
      label.fontFace = 'Inter, system-ui, -apple-system, BlinkMacSystemFont'
      const baseSize = isCategory ? 12 : 7
      const baseOpacity = isCategory ? 0.95 : 0.45
      label.textHeight = baseSize
      label.material.transparent = true
      label.material.opacity = node.id === hoveredId ? 1.0 : baseOpacity
      label.center.set(0.5, 0)
      label.position.set(0, isRoot ? 16 : 10, 0)
      // Prevent label sprite from capturing drag/raycast to reduce DragControls glitches
      label.raycast = () => null
      // Store reference for hover updates
      ;(group as any).__label = label
      group.add(label)
      return group
    },
    [hoveredId]
  )

  // Clicking a node should scroll outline to it
  const handleNodeClick = useCallback((node: any) => {
    try {
      const nav = (useNavigationStore.getState() as any).navigateToTreeNode
      if (typeof nav === 'function' && node?.id) nav(String(node.id))
      // also mark selection locally for emphasis and camera framing
      if (node?.id) setSelectedId(String(node.id))
      // camera: pull back to see neighborhood with a medium distance
      if (fgRef.current && node) {
        const dist = 240
        const tx = node.x || 0,
          ty = node.y || 0,
          tz = node.z || 0
        fgRef.current.cameraPosition(
          { x: tx + dist, y: ty + dist * 0.5, z: tz + dist },
          { x: tx, y: ty, z: tz },
          750
        )
      }
    } catch {}
  }, [])

  // Hover handler: emphasize label
  const handleNodeHover = useCallback(
    (node: any | null) => {
      // Soften previously hovered label
      try {
        if (hoveredId) {
          const prev: any = nodes.find(n => String(n.id) === hoveredId)
          const prevObj: any = prev?.__threeObj
          if (prevObj && prevObj.__label) {
            const wasCategory = prev?.type === 'category'
            prevObj.__label.material.opacity = wasCategory ? 0.75 : 0.45
            prevObj.__label.textHeight = wasCategory ? 12 : 7
          }
        }
        // Emphasize current
        const obj: any = node?.__threeObj
        if (obj && obj.__label) {
          obj.__label.material.opacity = 1.0
          const isCategory = node?.type === 'category'
          obj.__label.textHeight = isCategory ? 14 : 9
        }
      } catch {}
      setHoveredId(node?.id ? String(node.id) : null)
    },
    [hoveredId, nodes]
  )

  // Selection emphasis: selected node + its parent + children get larger, opaque labels
  useEffect(() => {
    if (!fgRef.current) return
    const selected = selectedId
    const neighborIds = new Set<string>()
    if (selected) {
      links.forEach((lk: any) => {
        const s = String((lk.source as any)?.id || lk.source)
        const t = String((lk.target as any)?.id || lk.target)
        if (s === selected) neighborIds.add(t)
        if (t === selected) neighborIds.add(s)
      })
    }

    const updateNode = (n: any) => {
      const obj: any = n.__threeObj
      if (!obj || !obj.__label) return
      const isSelected = selected ? String(n.id) === selected : false
      const isNeighbor = selected ? neighborIds.has(String(n.id)) : false
      const baseSize = n.type === 'category' ? 10 : 6
      const baseOpacity = n.type === 'category' ? 0.9 : 0.45
      if (isSelected || isNeighbor) {
        obj.__label.material.opacity = 1.0
        obj.__label.textHeight = n.type === 'category' ? 14 : 9
      } else {
        obj.__label.material.opacity = baseOpacity
        obj.__label.textHeight = baseSize
      }
    }

    // apply to all nodes
    nodes.forEach(updateNode)
  }, [selectedId, nodes, links])

  // Focus/zoom when navigating from outline (zoom out slightly more)
  useEffect(() => {
    if (!fgRef.current || !targetNodeId || nodes.length === 0) return
    const target: any = nodes.find(n => n.id === targetNodeId)
    if (!target) return
    const dist = 900
    const t = setTimeout(() => {
      try {
        const tx = target.x || 0,
          ty = target.y || 0,
          tz = target.z || 0
        fgRef.current.cameraPosition(
          { x: tx + dist, y: ty + dist * 0.6, z: tz + dist },
          { x: tx, y: ty, z: tz },
          900
        )
      } catch {}
    }, 80)
    return () => clearTimeout(t)
  }, [targetNodeId, nodes])

  // Tune forces and initial zoom-to-fit
  useEffect(() => {
    if (!fgRef.current) return
    const linkForce: any = fgRef.current.d3Force('link')
    if (linkForce && typeof linkForce.distance === 'function') {
      linkForce.distance((link: any) => {
        const src: any = link.source || {}
        const tgt: any = link.target || {}
        const level = Math.max(src.level || 1, tgt.level || 1)
        const srcType = src.type || ''
        const tgtType = tgt.type || ''
        const bothCategory = srcType === 'category' && tgtType === 'category'
        const categoryToLeaf =
          (srcType === 'category' && tgtType === 'question') ||
          (tgtType === 'category' && srcType === 'question')
        const degreeBoost = ((src.deg || 0) + (tgt.deg || 0)) * 6
        const len = Math.max(src.name?.length || 0, tgt.name?.length || 0)

        let base = 118 + level * 28
        if (bothCategory) base -= 32
        else if (categoryToLeaf) base -= 22

        base = Math.max(70, base)
        return Math.min(600, base + Math.min(220, len) + degreeBoost)
      })
    }
    const charge: any = fgRef.current.d3Force('charge')
    if (charge && typeof charge.strength === 'function') charge.strength(-300)

    // One-time zoom to fit and back off a little to see all content
    if (!didFitRef.current) {
      didFitRef.current = true
      setTimeout(() => {
        try {
          fgRef.current.zoomToFit(1000, 120)
        } catch {}
      }, 200)
    }
  }, [data])

  // Guard against intermittent DragControls null target bug while preserving drag behavior
  useEffect(() => {
    const handler = (e: ErrorEvent) => {
      const msg = e?.message || (e?.error && (e.error as any).message) || ''
      if (
        typeof msg === 'string' &&
        (msg.includes('DragControls') || msg.includes('matrixWorld'))
      ) {
        // Swallow the error to avoid breaking interaction; allow rendering to continue
        e.preventDefault()
        return true
      }
      return false
    }
    window.addEventListener('error', handler)
    return () => window.removeEventListener('error', handler)
  }, [])

  return (
    <div
      ref={containerRef}
      className={cn(
        'h-full w-full relative bg-gradient-to-br from-cyan-50 to-sky-50'
      )}
    >
      <LayoutModeToggle layoutMode={layoutMode} setLayoutMode={setLayoutMode} />
      <Suspense
        fallback={
          <div className="absolute inset-0 flex items-center justify-center text-sm text-gray-500">
            Loading 3D…
          </div>
        }
      >
        <ForceGraph3D
          ref={fgRef}
          width={size.width}
          height={size.height}
          graphData={data as any}
          nodeId="id"
          nodeLabel={(n: any) => n.name}
          nodeThreeObject={nodeThreeObject}
          nodeThreeObjectExtend={true}
          linkOpacity={0.6}
          linkColor={() => 'rgba(100,116,139,0.8)'}
          backgroundColor="rgba(255,255,255,0)"
          onNodeClick={handleNodeClick}
          onNodeHover={handleNodeHover}
          enableNodeDrag={true}
          onNodeDrag={() => {
            /* no-op, but keeps drag active */
          }}
          onNodeDragEnd={() => {
            /* no-op */
          }}
        />
      </Suspense>
      <div className="absolute bottom-4 left-4 z-20 pointer-events-none">
        <div
          className={cn(
            'flex items-center gap-2 rounded-lg border border-cyan-200/80 bg-white/85 px-3 py-2 shadow-sm backdrop-blur-sm',
            'pointer-events-auto'
          )}
        >
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleZoomControl('in')}
            className="h-8 w-8 p-0 text-base font-semibold text-cyan-900"
            aria-label="Zoom in"
          >
            +
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleZoomControl('out')}
            className="h-8 w-8 p-0 text-base font-semibold text-cyan-900"
            aria-label="Zoom out"
          >
            -
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetCamera}
            className="h-8 px-3 text-xs font-semibold lowercase text-cyan-900"
            aria-label="Reset camera"
          >
            reset
          </Button>
        </div>
      </div>
    </div>
  )
}

export default React.memo(ForceGraph3DCanvas)
