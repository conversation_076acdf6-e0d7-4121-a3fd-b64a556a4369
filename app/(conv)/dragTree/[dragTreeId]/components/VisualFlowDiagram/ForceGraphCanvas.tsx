'use client'

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  Suspense,
  lazy,
} from 'react'
import { LayoutModeToggle } from './LayoutModeToggle'
import { useForceGraphData } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useForceGraphData'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { cn } from '@/lib/utils'
import { forceCollide } from 'd3-force'

const ForceGraph2D = lazy(() =>
  import('react-force-graph-2d').then(m => ({ default: m.default }))
)

type ForceGraphCanvasProps = {
  layoutMode: 'linear' | 'radial' | 'force' | 'force3d'
  setLayoutMode: (mode: 'linear' | 'radial' | 'force' | 'force3d') => void
  dragTreeId: string
}

const ForceGraphSkeleton: React.FC = () => (
  <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-emerald-500 mx-auto mb-2"></div>
      <p className="text-gray-600 text-sm">Loading force graph...</p>
    </div>
  </div>
)

export type FGNode = {
  id: string
  name: string
  type: 'category' | 'question'
  level: number
}

export type FGLink = { source: string; target: string }

const ForceGraphCanvas: React.FC<ForceGraphCanvasProps> = ({
  layoutMode,
  setLayoutMode,
  dragTreeId: _dragTreeId,
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null)
  const [size, setSize] = useState<{ width: number; height: number }>({
    width: 800,
    height: 600,
  })
  const { nodes, links } = useForceGraphData()
  const fgRef = useRef<any>(null)
  const [didInitialFit, setDidInitialFit] = useState(false)
  const targetNodeId = useNavigationStore(state => state.targetNodeId)

  // Observe container size for responsive canvas
  useEffect(() => {
    if (!containerRef.current) return
    const el = containerRef.current
    const ro = new ResizeObserver(entries => {
      for (const entry of entries) {
        const cr = entry.contentRect
        setSize({
          width: Math.max(200, cr.width),
          height: Math.max(200, cr.height),
        })
      }
    })
    ro.observe(el)
    return () => ro.disconnect()
  }, [])

  const data = useMemo(() => ({ nodes, links }), [nodes, links])

  const handleNodeClick = useCallback((node: any) => {
    try {
      const navigateToTreeNode = (useNavigationStore.getState() as any)
        .navigateToTreeNode
      if (typeof navigateToTreeNode === 'function' && node?.id) {
        navigateToTreeNode(String(node.id))
      }
    } catch {}
  }, [])

  // Custom draw for nodes for better differentiation
  const nodeCanvasObject = useCallback(
    (node: any, ctx: CanvasRenderingContext2D, globalScale: number) => {
      const label: string = node.name || node.id
      const fontSize = Math.max(10, 12 / Math.sqrt(globalScale))
      ctx.font = `${fontSize}px Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial`
      const maxWidth = 260 // cap label width
      const padding = 6

      // word-wrap to multiple lines to avoid ultra-wide nodes
      const words = label.split(' ')
      const lines: string[] = []
      let current = ''
      for (const w of words) {
        const test = current ? current + ' ' + w : w
        if (ctx.measureText(test).width <= maxWidth) {
          current = test
        } else {
          if (current) lines.push(current)
          current = w
        }
      }
      if (current) lines.push(current)

      const textWidth = Math.min(
        maxWidth,
        Math.max(...lines.map(l => ctx.measureText(l).width), 0)
      )
      const lineHeight = fontSize * 1.25
      const bW = textWidth + padding * 2
      const bH = lineHeight * lines.length + padding * 2

      // draw rounded background (special color for root)
      ctx.fillStyle =
        node.level === 1
          ? '#f59e0b'
          : node.type === 'category'
            ? '#7c3aed'
            : '#10b981'
      ctx.beginPath()
      ctx.roundRect(node.x - bW / 2, node.y - bH / 2, bW, bH, 8)
      ctx.fill()

      // highlight ring when focused from outline
      if (targetNodeId && node.id === targetNodeId) {
        ctx.strokeStyle = 'rgba(255,255,255,0.9)'
        ctx.lineWidth = 3
        ctx.stroke()
      }

      // draw text
      ctx.fillStyle = 'white'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      const startY = node.y - (lines.length - 1) * lineHeight * 0.5
      lines.forEach((ln, i) => {
        ctx.fillText(ln, node.x, startY + i * lineHeight)
      })
    },
    [targetNodeId]
  )

  // Improve hit area to match wrapped label box for reliable clicking
  const nodePointerAreaPaint = useCallback(
    (
      node: any,
      color: string,
      ctx: CanvasRenderingContext2D,
      globalScale: number
    ) => {
      const label: string = node.name || node.id
      const fontSize = Math.max(10, 12 / Math.sqrt(globalScale))
      ctx.font = `${fontSize}px Inter, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial`
      const maxWidth = 260
      const padding = 6
      const words = label.split(' ')
      let current = ''
      const lines: string[] = []
      for (const w of words) {
        const test = current ? current + ' ' + w : w
        if (ctx.measureText(test).width <= maxWidth) current = test
        else {
          if (current) lines.push(current)
          current = w
        }
      }
      if (current) lines.push(current)
      const textWidth = Math.min(
        maxWidth,
        Math.max(...lines.map(l => ctx.measureText(l).width), 0)
      )
      const lineHeight = fontSize * 1.25
      const bW = textWidth + padding * 2
      const bH = lineHeight * lines.length + padding * 2
      ctx.fillStyle = color
      ctx.fillRect(node.x - bW / 2, node.y - bH / 2, bW, bH)
    },
    []
  )

  // Configure forces to spread nodes out
  useEffect(() => {
    if (!fgRef.current) return
    // collision radius scales with label length and depth
    fgRef.current.d3Force(
      'collide',
      forceCollide(
        (d: any) => 34 + Math.min(40, (d.name?.length || 0) / 6 + d.level * 4)
      )
    )
    // stronger repulsion than default
    const charge: any = fgRef.current.d3Force('charge')
    if (charge && typeof charge.strength === 'function') charge.strength(-320)

    // dynamic link distance via link force
    const linkForce: any = fgRef.current.d3Force('link')
    if (linkForce && typeof linkForce.distance === 'function') {
      linkForce.distance((link: any) => {
        const src: any = link.source || {}
        const tgt: any = link.target || {}
        const level = Math.max(src.level || 1, tgt.level || 1)
        const degreeBoost = ((src.deg || 0) + (tgt.deg || 0)) * 6
        const len = Math.max(src.name?.length || 0, tgt.name?.length || 0)
        const base = 160 + level * 40
        return Math.min(800, base + Math.min(300, len) + degreeBoost)
      })
    }
  }, [data])

  // Focus/zoom when navigating from outline
  useEffect(() => {
    if (!fgRef.current || !targetNodeId || nodes.length === 0) return
    const target: any = nodes.find(n => n.id === targetNodeId) as any
    if (!target) return
    // wait for engine to stabilize a bit
    const t = setTimeout(() => {
      try {
        fgRef.current.centerAt(target.x, target.y, 600)
        fgRef.current.zoom(1.25, 600)
      } catch {}
    }, 50)
    return () => clearTimeout(t)
  }, [targetNodeId, nodes])

  return (
    <div
      ref={containerRef}
      className={cn(
        'h-full w-full relative bg-gradient-to-br from-emerald-50 to-teal-50'
      )}
    >
      <LayoutModeToggle layoutMode={layoutMode} setLayoutMode={setLayoutMode} />
      <Suspense fallback={<ForceGraphSkeleton />}>
        <ForceGraph2D
          ref={fgRef}
          width={size.width}
          height={size.height}
          graphData={data as any}
          nodeId="id"
          nodeLabel={(n: any) => n.name}
          nodeCanvasObject={nodeCanvasObject}
          nodePointerAreaPaint={nodePointerAreaPaint}
          linkDirectionalParticles={1}
          linkDirectionalParticleWidth={2}
          linkColor={() => 'rgba(100,116,139,0.8)'}
          onNodeClick={handleNodeClick}
          cooldownTicks={120}
          d3VelocityDecay={0.25}
          d3AlphaDecay={0.02}
          enableNodeDrag={true}
          onEngineStop={() => {
            if (!didInitialFit && fgRef.current) {
              fgRef.current.zoomToFit(600, 100)
              setDidInitialFit(true)
            }
          }}
        />
      </Suspense>
    </div>
  )
}

export default React.memo(ForceGraphCanvas)
