'use client'
import React, { Suspense, lazy } from 'react'
import {
  ENABLE_DRAGTREE_FORCE_GRAPH_2D,
  ENABLE_DRAGTREE_FORCE_GRAPH_3D,
  ENABLE_DRAGTREE_ISSUE_TREE,
  ENABLE_DRAGTREE_ISSUE_TREE_RADIAL,
} from '@/app/configs/feature-flags'

// Lightweight wrapper that lazy-loads the heavy ReactFlow canvas
const ReactFlowCanvas = lazy(() =>
  import('./ReactFlowCanvas').then(m => ({ default: m.default }))
)
const ForceGraphCanvas = lazy(() =>
  import('./ForceGraphCanvas').then(m => ({ default: m.default }))
)
const ForceGraph3DCanvas = lazy(() =>
  import('./ForceGraph3DCanvas').then(m => ({ default: m.default }))
)

const ReactFlowSkeleton: React.FC = () => (
  <div className="w-full h-full flex items-center justify-center bg-gray-50 rounded-lg">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
      <p className="text-gray-600 text-sm">Loading diagram...</p>
    </div>
  </div>
)

type DiagramViewProps = {
  layoutMode: 'linear' | 'radial' | 'force' | 'force3d'
  setLayoutMode: (mode: 'linear' | 'radial' | 'force' | 'force3d') => void
  dragTreeId?: string
}

export const DiagramView: React.FC<DiagramViewProps> = React.memo(
  function DiagramView({ layoutMode, setLayoutMode, dragTreeId }) {
    const safeId = dragTreeId || 'test'
    // Enforce feature flags at render time with a safe fallback order: 3D -> star -> 2D
    const isIssueTree = layoutMode === 'linear' || layoutMode === 'radial'
    const requested3D = layoutMode === 'force3d'
    const requested2D = layoutMode === 'force'
    let effectiveMode: 'linear' | 'radial' | 'force' | 'force3d' = layoutMode
    if (requested3D && !ENABLE_DRAGTREE_FORCE_GRAPH_3D) {
      effectiveMode = ENABLE_DRAGTREE_ISSUE_TREE
        ? 'linear'
        : ENABLE_DRAGTREE_FORCE_GRAPH_2D
          ? 'force'
          : 'linear'
    } else if (requested2D && !ENABLE_DRAGTREE_FORCE_GRAPH_2D) {
      effectiveMode = ENABLE_DRAGTREE_ISSUE_TREE
        ? 'linear'
        : ENABLE_DRAGTREE_FORCE_GRAPH_3D
          ? 'force3d'
          : 'linear'
    } else if (isIssueTree && !ENABLE_DRAGTREE_ISSUE_TREE) {
      effectiveMode = ENABLE_DRAGTREE_FORCE_GRAPH_3D
        ? 'force3d'
        : ENABLE_DRAGTREE_FORCE_GRAPH_2D
          ? 'force'
          : 'linear'
    }

    // If Issue Tree is enabled but radial is disabled, coerce radial to linear
    if (effectiveMode === 'radial' && !ENABLE_DRAGTREE_ISSUE_TREE_RADIAL) {
      effectiveMode = 'linear'
    }
    return (
      <Suspense fallback={<ReactFlowSkeleton />}>
        {effectiveMode === 'force' ? (
          <ForceGraphCanvas
            layoutMode={effectiveMode}
            setLayoutMode={setLayoutMode}
            dragTreeId={safeId}
          />
        ) : effectiveMode === 'force3d' ? (
          <ForceGraph3DCanvas
            layoutMode={effectiveMode}
            setLayoutMode={setLayoutMode}
            dragTreeId={safeId}
          />
        ) : (
          <ReactFlowCanvas
            layoutMode={effectiveMode === 'radial' ? 'radial' : 'linear'}
            setLayoutMode={setLayoutMode}
            dragTreeId={safeId}
          />
        )}
      </Suspense>
    )
  }
)
