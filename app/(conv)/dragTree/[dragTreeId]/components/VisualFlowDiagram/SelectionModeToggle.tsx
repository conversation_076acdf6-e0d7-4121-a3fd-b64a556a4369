'use client'
import React from 'react'
import { Button } from '@/components/ui/button'
import { MousePointer2, Hand } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useSelectionStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useSelectionStore'
import { ENABLE_DRAGTREE_NODE_SELECTION } from '@/app/configs/feature-flags'

type SelectionModeToggleProps = {
  className?: string
}

export const SelectionModeToggle: React.FC<SelectionModeToggleProps> =
  React.memo(function SelectionModeToggle({ className }) {
    const isSelectionMode = useSelectionStore(state => state.isSelectionMode)
    const toggleSelectionMode = useSelectionStore(
      state => state.toggleSelectionMode
    )
    const selectedCount = useSelectionStore(state => state.getSelectedCount())

    if (!ENABLE_DRAGTREE_NODE_SELECTION) return null

    return (
      <div
        className={cn(
          'flex items-center bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-lg p-1 shadow-sm',
          className
        )}
      >
        <Button
          variant={isSelectionMode ? 'default' : 'ghost'}
          size="sm"
          onClick={toggleSelectionMode}
          className={cn(
            'h-auto px-3 py-1.5 text-xs font-medium transition-all duration-200 flex items-center gap-2',
            isSelectionMode
              ? 'bg-emerald-500 text-white shadow-sm hover:bg-emerald-600 border-2 border-rose-300 ring-2 ring-rose-400'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100/50'
          )}
          title={
            isSelectionMode
              ? 'Exit selection mode (Esc)'
              : 'Enter selection mode to select nodes for AI context'
          }
          aria-label={
            isSelectionMode ? 'Exit selection mode' : 'Enter selection mode'
          }
          aria-pressed={isSelectionMode}
        >
          {isSelectionMode ? (
            <MousePointer2 className="w-3 h-3" />
          ) : (
            <Hand className="w-3 h-3" />
          )}
          {isSelectionMode ? (
            <div className="flex flex-col leading-tight items-start">
              <span>Selecting</span>
              <span className="text-[10px] font-normal opacity-80">Esc</span>
            </div>
          ) : (
            'Select to Use'
          )}
          {isSelectionMode && selectedCount > 0 && (
            <span className="bg-white/20 text-white text-xs px-1.5 py-0.5 rounded-full font-semibold ml-1">
              {selectedCount}
            </span>
          )}
        </Button>
      </div>
    )
  })
