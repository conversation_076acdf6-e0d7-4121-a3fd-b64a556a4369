# Visual Flow Diagram Components

## Overview

This directory contains the ReactFlow-based visualization components for rendering tree data as interactive flow diagrams with dual layout modes (hierarchical/circular), robust export functionality, and enhanced error handling.

## Key Components

### DiagramView.tsx

Main ReactFlow component that renders the flow diagram with custom nodes and edges.

**Key Features:**

- **Dual Layout Support**: Linear (hierarchical) and radial (circular) layout modes
- **Intelligent Auto-Focus**: Different centering strategies based on layout mode and initialization state
- **Error Recovery**: Comprehensive viewport validation and automatic recovery for NaN errors
- **MiniMap Protection**: Error boundary with fallback UI for SVG viewBox issues
- **Magic Pad Protection**: Trackpad pinch zoom disabled due to ReactFlow bugs
- **Exponential Backoff**: Retry mechanism for circular layout centering

**Auto-Focus Behavior:**

- **Initial Circular Layout**: Uses exponential backoff retry to ensure ELK.js positioning completes before centering
- **Hierarchical Layout**: Fast root node focusing with 200ms timeout
- **Layout Switching**: Maintains existing root node focusing behavior

### FlowExportButton.tsx

Header-integrated export buttons providing dual layout download options.

**Export Options:**

- **Download Hierarchical Flow**: Linear layout export with GitMerge icon
- **Download Circular Flow**: Radial layout export with CircleDot icon
- **Layout Switching**: Temporarily switches to requested layout for download, then restores original
- **Status Management**: Independent loading states for each export type
- **Header Integration**: Located in export tab, replacing markdown export

### useFlowImageDownload.ts

Enhanced ReactFlow download hook with layout mode support and temporary layout switching.

**Key Features:**

- **Layout Mode Support**: Downloads in specified layout (linear/radial) regardless of current view
- **Temporary Layout Switching**: Switches layout for download, then restores original
- **Native ReactFlow APIs**: Uses `getRectOfNodes()` and `getTransformForBounds()` following official examples
- **Full Diagram Capture**: Calculates bounds of all nodes and applies proper transform to capture entire diagram
- **Edge Visibility**: Targets `.react-flow__viewport` with computed transform to ensure both nodes and edges are included
- **Smart Filenames**: Includes layout type in filename (`flow-diagram-hierarchical-timestamp.png`)

**Usage:**

```tsx
const { downloadImage, isDownloading } = useFlowImageDownload(
  layoutMode,
  setLayoutMode
)

// Download in current layout
await downloadImage()

// Download in specific layout
await downloadImage('radial')
```

### useDiagramNavigation.ts

Navigation hook for cross-component communication between outline and flow diagram.

**Enhanced Zoom Behavior:**

- **Reduced Max Zoom**: Changed from 0.8 to 0.4 for better context visibility
- **Increased Padding**: From 0.4 to 0.6 to show more surrounding nodes
- **Navigation Focus**: When clicking from outline, users see target node plus neighboring nodes

### ForceGraph3DCanvas.tsx

3D force-directed renderer with dedicated camera helpers.

**Key Enhancements:**

- **Reset Zoom-To-Fit**: The `reset` control now animates `zoomToFit()` so users instantly see the full graph again after panning or drilling into details.
- **Zoom Controls**: Bottom-left UI exposes `+`, `-`, and `reset` actions that call the new camera utilities for smooth dolly-style zooming.
- **Compact Tips**: Bottom-center pill text summarizes orbit, pan, and zoom gestures without covering the viewport.
- **Category Styling**: Category labels render with indigo text and larger type so they stand out from question nodes.
- **Safety Bounds**: Zoom controls clamp distances (`80`-`4000`) and link forces reduce category↔leaf spacing for a denser cluster.

### Error Handling & Recovery

**Viewport Validation System:**

- **Real-time Monitoring**: `onMove` handler validates all viewport changes
- **NaN Detection**: Prevents invalid viewport values from reaching SVG rendering
- **Automatic Recovery**: Debounced `fitView()` when invalid values detected
- **Fallback Strategy**: Restores last known good viewport or applies safe defaults

**MiniMap Error Boundary:**

- **SVG Error Detection**: Catches `viewBox` NaN errors via window error listener
- **Fallback UI**: Shows "MiniMap Recovering..." with click-to-retry functionality
- **Automatic Recovery**: Triggers viewport recovery when minimap errors detected
- **Graceful Degradation**: Maintains functionality even when minimap fails

**Exponential Backoff for Circular Layout:**

- **Position Validation**: Checks if ELK.js has properly positioned nodes before centering
- **Retry Strategy**: 200ms → 400ms → 800ms → 1600ms with max 4 attempts
- **Adaptive Timing**: Only waits as long as needed for layout positioning
- **Bounded Recovery**: Always applies fallback `fitView()` if validation fails

## Export Functionality

Export buttons are integrated into the header export tab with two options:

### Hierarchical Flow Export

1. Switches to linear layout if needed
2. Calculates bounds using `getRectOfNodes(getNodes())`
3. Computes transform using `getTransformForBounds()`
4. Downloads with filename: `flow-diagram-hierarchical-YYYY-MM-DD-HH-mm-ss.png`
5. Restores original layout if switched

### Circular Flow Export

1. Switches to radial layout if needed
2. Waits for ELK.js positioning to complete
3. Captures full circular arrangement with proper centering
4. Downloads with filename: `flow-diagram-circular-YYYY-MM-DD-HH-mm-ss.png`
5. Restores original layout if switched

## Layout Modes

### Linear (Hierarchical) Layout

- **Algorithm**: Dagre layout engine
- **Structure**: Traditional tree hierarchy flowing left-to-right
- **Performance**: Fast synchronous positioning
- **Use Case**: Structured, step-by-step content flows

### Radial (Circular) Layout

- **Algorithm**: ELK.js radial layout engine
- **Structure**: Circular arrangement with root at center
- **Performance**: Asynchronous positioning with retry logic
- **Use Case**: Concept mapping and relationship visualization

## Technical Details

### Zoom & Navigation

- **Default Viewport**: `{ x: 0, y: 0, zoom: 0.5 }` (reduced from 0.8 for better context)
- **Initial Focus Max Zoom**: 0.5 for hierarchical, fitView() for circular
- **Navigation Max Zoom**: 0.4 with 0.6 padding for context visibility
- **Trackpad Protection**: `zoomOnPinch={false}` due to ReactFlow Magic Pad bugs

### Error Recovery

- **Viewport Validation**: Real-time validation of x, y, zoom values
- **Recovery Timeout**: 150ms debounced to prevent rapid-fire recovery
- **NaN Detection**: Comprehensive checks for finite, non-NaN values
- **MiniMap Fallback**: Custom error boundary with user-friendly retry interface

### Performance Optimizations

- **Debounced Layout Computation**: `useDiagramData` now debounces tree and layout mode changes (300 ms) to prevent redundant layout recalculations during rapid updates.
- **Layout Algorithms**: NaN/Infinity validation in both Dagre and ELK layouts
- **Exponential Backoff**: Efficient retry logic for circular layout centering
- **Debounced Recovery**: Prevents excessive `fitView()` calls
- **Memory Management**: Proper cleanup of timeouts and event listeners

## Integration Points

### Header Export Tab

- Located in `components/layout/Header.tsx` export tab
- Replaces markdown export with dual flow diagram exports
- Uses custom event system for ReactFlow communication

### Navigation System

- Cross-component navigation via `useNavigationStore`
- Automatic expansion and scrolling in hierarchical outline
- Enhanced zoom behavior for better context visibility

### Store Integration

- Layout mode managed via component props
- Export status communicated through custom events
- Error states managed via local component state

## Magic Pad Trackpad Issues

**Known ReactFlow Bug:**

- Magic Pad pinch gestures can generate extreme zoom values (NaN/Infinity)
- Causes SVG rendering errors in Background and MiniMap components
- `zoomOnPinch={false}` disabled as protection
- Comprehensive error recovery system handles edge cases

**Alternative Zoom Methods:**

- Mouse scroll wheel (zoomOnScroll={true})
- Zoom controls in UI (ReactFlow + Force 3D `+ / - / reset` buttons)
- MiniMap zoom functionality

## Selection-Based Context Feature

### Overview

The Visual Flow Diagram includes a powerful selection-based context feature that allows users to visually select nodes and use them as context for AI interactions.

### Selection Mode Toggle

- **Location**: Upper right corner of the React Flow interface
- **Button**: "Select to Use" / "Selecting" toggle
- **Functionality**: Switches between normal navigation mode and selection mode

### Visual Selection

- **Rectangle Selection**: Click and drag to create selection rectangles over nodes
- **Multiple Selections**: Support for multiple selection rectangles
- **Visual Feedback**: Selected nodes are highlighted with blue borders and glow effects
- **Smart Selection**: When a category node is selected, all its child nodes are automatically included

### Node Filtering

- **Researched Nodes Only**: Only nodes with research content are included in selections
- **Visual Indicators**: Non-researched nodes are filtered out automatically
- **Content Validation**: Ensures only meaningful context is passed to AI

### Selection Controls

- **Dynamic Counter**: Shows "X items selected" with research status
- **Clear Button**: Quickly clear current selection
- **Use as Context Button**: Apply selection as AI context and open sidebar
- **Smart Validation**: Button is disabled when no researched nodes are selected

### AI Integration

- **Seamless Context Transfer**: Selected nodes are automatically converted to AI context items
- **Pre-populated Sidebar**: AI pane opens with selected content ready to use
- **Content Mapping**: Node content is properly mapped to context format

### Keyboard Shortcuts

- **Escape**: Exit selection mode
- **Delete/Backspace**: Clear current selection
- **Enter**: Use selection as context (if researched nodes are selected)

### Accessibility

- **ARIA Labels**: Proper labeling for screen readers
- **Keyboard Navigation**: Full keyboard support for all functions
- **Focus Management**: Proper focus handling in selection mode
- **Role Attributes**: Semantic markup for assistive technologies

### Technical Implementation

**Core Components:**

- **SelectionModeToggle**: Toggle button for entering/exiting selection mode
- **SelectionOverlay**: Handles mouse interactions for rectangle drawing
- **SelectionControls**: Counter and action buttons for managing selections
- **Enhanced Node Components**: Visual feedback for selection state

**State Management:**

- **useSelectionStore**: Zustand store for selection state
- **Selection Rectangles**: Tracked with unique IDs and coordinates
- **Selected Nodes**: Set-based storage for efficient lookups
- **Drawing State**: Current rectangle being drawn

**Hooks:**

- **useNodeSelection**: Logic for detecting node intersections with rectangles
- **useSelectionToContext**: Converts selections to AI context format
- **useSelectionCleanup**: Handles state cleanup on navigation/unmount
- **useSelectionKeyboard**: Keyboard shortcut handling

### Usage Workflow

1. **Enter Selection Mode**: Click "Select to Use" button
2. **Select Nodes**: Click and drag to create selection rectangles over desired nodes
3. **Review Selection**: Check the counter to see how many researched nodes are selected
4. **Use as Context**: Click "Use as Context" or press Enter
5. **AI Interaction**: AI sidebar opens with selected content pre-populated

## Future Enhancements

- **Additional Layout Algorithms**: Force-directed, hierarchical bottom-up
- **Custom Node Types**: Specialized nodes for different content types
- **Advanced Export Options**: SVG, PDF, multiple resolutions
- **Real-time Collaboration**: Multi-user flow diagram editing
- **Animation System**: Smooth transitions between layout modes
- **Enhanced Selection**: Ctrl/Cmd + A for select all, Shift + Click for individual nodes
- **Selection Persistence**: Optional session-based selection memory
