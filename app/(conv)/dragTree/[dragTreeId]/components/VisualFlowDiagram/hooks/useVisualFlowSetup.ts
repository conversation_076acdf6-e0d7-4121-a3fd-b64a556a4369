'use client'
import { useState, useEffect } from 'react'
import {
  ENABLE_DRAGTREE_FORCE_GRAPH_2D,
  ENABLE_DRAGTREE_FORCE_GRAPH_3D,
  ENABLE_DRAGTREE_ISSUE_TREE,
} from '@/app/configs/feature-flags'

export const useVisualFlowSetup = () => {
  // Default selection respects flags: prefer 3D, else star, else 2D
  const preferredDefault: 'linear' | 'radial' | 'force' | 'force3d' =
    ENABLE_DRAGTREE_ISSUE_TREE
      ? 'linear'
      : ENABLE_DRAGTREE_FORCE_GRAPH_3D
        ? 'force3d'
        : ENABLE_DRAGTREE_FORCE_GRAPH_2D
          ? 'force'
          : 'linear'
  const [layoutMode, setLayoutMode] = useState<
    'linear' | 'radial' | 'force' | 'force3d'
  >(preferredDefault)
  const [isInitialLoading, setInitialLoading] = useState(true)

  // Brief loading state for initial ReactFlow setup
  useEffect(() => {
    const timeout = setTimeout(() => setInitialLoading(false), 100) // Quick setup time
    return () => clearTimeout(timeout)
  }, [])

  // Reset layout mode on unmount to prevent state persistence between routes
  useEffect(() => {
    return () => {
      // Reset to default respecting current flags
      setLayoutMode(
        ENABLE_DRAGTREE_ISSUE_TREE
          ? 'linear'
          : ENABLE_DRAGTREE_FORCE_GRAPH_3D
            ? 'force3d'
            : ENABLE_DRAGTREE_FORCE_GRAPH_2D
              ? 'force'
              : 'linear'
      )
    }
  }, [])

  return {
    isInitialLoading,
    layoutMode,
    setLayoutMode,
  }
}
