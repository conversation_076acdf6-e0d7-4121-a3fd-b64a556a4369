'use client'

import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import {
  FiMessageCircle,
  FiSettings,
  FiEdit2,
  FiCopy,
  FiList,
} from 'react-icons/fi'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { getAccessPermissions } from '@/app/configs/tier-permissions'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'

import type { UIMessage } from 'ai'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { getChatApiEndpoint } from '@/app/configs/feature-flags'

import { useWorkspaceLayoutStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useNavigationStore } from '@/app/stores/navigation_store'
import toast from 'react-hot-toast'
import { logEventWithContext } from '@/app/libs/logging'

import ChatMessageList from '../chat/ChatMessageList'
import ChatInput from '../chat/ChatInput'

type ChatTabContentProps = {
  tab: Tab
  dragTreeId: string
}

// Internal component that uses the ChatPane context
function ChatTabContentInternal({
  tab,
  conversationTitle,
  contextIdsFromConversation,
}: {
  tab: Tab
  conversationTitle?: string | null
  contextIdsFromConversation?: string[] // Fallback from metadata to avoid "No context" mismatch
}) {
  const { data: session } = useSession()
  const userTier =
    (session?.user as any)?.subscriptionTier || SubscriptionTier.FREE
  const permissions = getAccessPermissions(userTier)

  // Get tree data for finding node names
  const findNodeById = useDragTreeStore(state => state.findNodeById)
  const navigateToTreeNode = useNavigationStore(
    state => state.navigateToTreeNode
  )
  const updateTabTitle = useWorkspaceLayoutStore(state => state.updateTabTitle)

  // Compute effective context IDs: prefer tab.aiPaneData, fallback to conversation metadata for consistency
  const effectiveContextIds = useMemo<string[]>(() => {
    const idsFromTab = tab.aiPaneData?.contextIds || []
    if (idsFromTab.length > 0) return idsFromTab
    return contextIdsFromConversation || []
  }, [tab.aiPaneData?.contextIds, contextIdsFromConversation])

  // Initialize all hooks first (before any conditional returns)
  // Remove local messages state - use chat's state for everything
  const [isLoadingHistory, setIsLoadingHistory] = useState<boolean>(true)
  const conversationId = tab.aiPaneData?.conversationId

  // Add ref to track if history has been set (prevents re-setting during streaming)
  const historySetRef = useRef(false)
  const [showSettingsModal, setShowSettingsModal] = useState<boolean>(false)
  const [showContextDialog, setShowContextDialog] = useState<boolean>(false)
  const [isEditingTitle, setIsEditingTitle] = useState<boolean>(false)
  const [editableTitle, setEditableTitle] = useState<string>(tab.title || '')
  // const updateTabAiPaneData = useTabStore(state => state.updateTabAiPaneData)

  useEffect(() => {
    setEditableTitle(tab.title || '')
  }, [tab.title])

  // Input state management (AI SDK v5 doesn't provide this)
  const [input, setInput] = useState<string>('')

  // Cache configuration
  const CACHE_TTL = 2 * 60 * 1000 // 2 minutes

  // Enhanced cache functions with localStorage persistence
  const loadFromCache = useCallback(
    (id: string) => {
      try {
        const cachedStr = localStorage.getItem(`chat-cache-${id}`)
        if (cachedStr) {
          const cached = JSON.parse(cachedStr)
          if (Date.now() - cached.timestamp < CACHE_TTL) {
            return cached.data
          }
        }
      } catch (error) {
        console.warn('[Chat Cache] Failed to load from localStorage:', error)
      }
      return null
    },
    [CACHE_TTL]
  )

  const saveToCache = useCallback((id: string, data: UIMessage[]) => {
    try {
      localStorage.setItem(
        `chat-cache-${id}`,
        JSON.stringify({
          data,
          timestamp: Date.now(),
          lastMessageId: data[data.length - 1]?.id || null,
        })
      )
    } catch (error) {
      console.warn('[Chat Cache] Failed to save to localStorage:', error)
    }
  }, [])

  const invalidateCache = useCallback((id: string) => {
    try {
      localStorage.removeItem(`chat-cache-${id}`)
    } catch (error) {
      console.warn('[Chat Cache] Failed to invalidate localStorage:', error)
    }
  }, [])

  const contextContent = useCallback(() => {
    try {
      if (effectiveContextIds.length === 0) return ''
      return `Context from ${effectiveContextIds.length} documents`
    } catch (error) {
      console.error('Error getting context:', error)
      return ''
    }
  }, [effectiveContextIds])

  // Context display removed from header per updated UX

  // Auto-fetch context if missing (debug and recovery)
  useEffect(() => {
    console.log('[Context Debug] Current state:', {
      'tab.id': tab.id,
      'effectiveContextIds.length': effectiveContextIds.length,
      effectiveContextIds: effectiveContextIds,
      'tab.aiPaneData?.contextIds': tab.aiPaneData?.contextIds,
      contextIdsFromConversation: contextIdsFromConversation,
    })

    // Skip if we already have context
    if (effectiveContextIds.length > 0) return

    // For now, just log the debug info - in a real implementation,
    // you would fetch from an API endpoint like `/api/context/${dragTreeId}`
    console.log(
      '[Context Debug] No context found - would fetch from API if endpoint existed'
    )
  }, [tab.id, effectiveContextIds, contextIdsFromConversation])

  // Only initialize chat when we have a conversation ID
  const shouldMountChat = Boolean(conversationId)

  // Track first turn to decide whether to include system context in payload
  const isFirstTurnRef = useRef(true)

  // Track if we've sent the initial prompt from AI Pane
  const [initialPromptSent] = useState(false)
  // Persisted dedupe flag to survive Strict Mode double-mounts
  // autoSentKey removed: we rely solely on first-user-message handoff

  // Memoize context content to prevent re-computation
  const memoizedContextContent = useMemo(() => {
    return contextContent()
  }, [contextContent])

  // Memoize transport to prevent re-initialization on every render
  const transport = useMemo((): DefaultChatTransport<UIMessage> | undefined => {
    if (!shouldMountChat) return undefined

    return new DefaultChatTransport({
      api: getChatApiEndpoint(),
      prepareSendMessagesRequest: ({ messages: outgoing }): { body: any } => {
        // Extract text from the last message (user's input)
        const lastMessage = outgoing[outgoing.length - 1] as any
        let text = ''

        if (Array.isArray(lastMessage?.parts)) {
          const textPart = lastMessage.parts.find(
            (p: any) => p?.type === 'text'
          )
          text = textPart?.text || ''
        } else if (typeof lastMessage?.content === 'string') {
          text = lastMessage.content
        }

        if (!conversationId) {
          throw new Error('Conversation ID not available')
        }

        const body = {
          message: text,
          conversationId,
          // Model is now controlled server-side based on user subscription tier
          contextIds: effectiveContextIds,
        }

        return { body }
      },
    })
  }, [
    shouldMountChat,
    conversationId,
    // Model dependency removed - now controlled server-side
    effectiveContextIds,
    memoizedContextContent,
  ])

  // Use proper transport configuration for AI SDK v5
  const chat = useChat({
    id: conversationId || 'disabled',
    transport,
  })

  console.log('[Chat] useChat debug:', {
    'chat keys': Object.keys(chat),
    'chat.messages.length': chat.messages.length,
    'chat.status': chat.status,
  })

  console.log('[Chat] Tab data debug:', {
    'tab.aiPaneData?.prompt': tab.aiPaneData?.prompt,
    'tab.aiPaneData?.conversationId': tab.aiPaneData?.conversationId,
    conversationId: conversationId,
    initialPromptSent: initialPromptSent,
  })

  // Track server-provided title and refresh it after each completed assistant turn
  const [serverTitle, setServerTitle] = useState<string | null>(
    conversationTitle ?? null
  )
  useEffect(() => {
    setServerTitle(conversationTitle ?? null)
  }, [conversationTitle])

  const lastAssistantCountRef = useRef<number>(0)
  useEffect(() => {
    if (!conversationId || chat.status !== 'ready') return
    const assistantCount = chat.messages.filter(
      m => m.role === 'assistant'
    ).length
    if (
      assistantCount > 0 &&
      assistantCount !== lastAssistantCountRef.current
    ) {
      lastAssistantCountRef.current = assistantCount
      // Fetch conversation to get latest title
      fetch(`/api/aipane/conversations/${conversationId}`)
        .then(res => (res.ok ? res.json() : null))
        .then(data => {
          const t = data?.conversation?.title
          if (typeof t === 'string' && t.trim() && t !== 'Untitled chat') {
            setServerTitle(t)
            updateTabTitle(tab.id, t)
            setEditableTitle(t)
          }
        })
        .catch(() => {})
    }
  }, [conversationId, chat.status, chat.messages, updateTabTitle, tab.id])

  // Auto-send initial prompt removed: we rely on sessionStorage handoff to avoid duplicate sends

  // First-user-message handoff: if only SYSTEM exists in DB and a pending first user message is saved,
  // submit it through useChat then clear the stash. This follows chat-demo flow.
  useEffect(() => {
    if (!conversationId || chat.status !== 'ready') return
    try {
      const key = `chat:firstUser:${conversationId}`
      const pending = sessionStorage.getItem(key)
      if (!pending) return

      // Only submit when no user/assistant messages are present yet (likely only system context exists)
      const hasNonSystem = chat.messages.some(m => m.role !== 'system')
      if (hasNonSystem) {
        sessionStorage.removeItem(key)
        return
      }

      chat.sendMessage({ text: pending })
      sessionStorage.removeItem(key)
    } catch {}
  }, [conversationId, chat.status, chat.messages])

  // Input change handler
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setInput(e.target.value)
    },
    []
  )

  // Submit handler - only works when conversation ID exists
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault()
      if (!input.trim() || !conversationId) return

      // Use AI SDK v5 sendMessage method (only when chat is ready)
      if (shouldMountChat && chat.sendMessage) {
        // After first user send, no longer attach initial context in payload
        isFirstTurnRef.current = false
        // Analytics: log send action
        try {
          logEventWithContext(
            'click_aipane_sendMessage',
            (session?.user as any)?.id,
            undefined,
            {
              message_length: input.trim().length,
              conversation_id: conversationId,
              context_count: effectiveContextIds.length,
            }
          )
        } catch {}
        chat.sendMessage({ text: input.trim() })

        // Invalidate cache on send for freshness
        invalidateCache(conversationId)
        console.log(`🗑️ [Chat Cache] Invalidated cache for ${conversationId}`)

        setInput('') // Clear input after sending
      }
    },
    [input, conversationId, shouldMountChat, chat.sendMessage, invalidateCache]
  )

  const handleTitleSave = useCallback(() => {
    const title = editableTitle.trim()
    if (title.length === 0) {
      setIsEditingTitle(false)
      return
    }
    updateTabTitle(tab.id, title)
    if (conversationId) {
      fetch(`/api/aipane/conversations/${conversationId}/title`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title }),
      }).catch(err => console.error('Failed to update title:', err))
    }
    setIsEditingTitle(false)
  }, [editableTitle, updateTabTitle, tab.id, conversationId])

  // Load history on mount or conversationId change with caching
  useEffect(() => {
    if (!conversationId) return

    // Skip history loading for new conversations that haven't sent their first message yet
    // This prevents race condition with auto-send logic
    const hasInitialPrompt = tab.aiPaneData?.prompt
    if (hasInitialPrompt && !initialPromptSent) {
      console.log(
        '[Chat] Skipping history load - waiting for auto-send to complete'
      )
      setIsLoadingHistory(false)
      return
    }

    // Avoid clobbering live streaming state; only load when chat is ready
    if (chat.status !== 'ready') {
      return
    }

    setIsLoadingHistory(true)

    const loadHistory = async () => {
      const startTime = performance.now()

      try {
        // Check localStorage cache first
        const cachedData = loadFromCache(conversationId)
        if (cachedData) {
          const loadTime = performance.now() - startTime
          console.log(
            `⚡ [Chat Cache] Hit for ${conversationId} (~${loadTime.toFixed(2)}ms)`
          )
          // Ensure cached data has proper parts structure for ChatMessageList
          const normalizedCachedData = cachedData.map(
            (msg: any, idx: number) => ({
              ...msg,
              id: msg.id || `cached_${idx}_${Date.now()}`,
              // Add parts structure if missing (for backward compatibility)
              parts:
                msg.parts ||
                (msg.content ? [{ type: 'text', text: msg.content }] : []),
            })
          )

          // Integrate cached history into useChat (prevents re-setting during streaming)
          if (!historySetRef.current) {
            chat.setMessages(normalizedCachedData)
            historySetRef.current = true
            if (normalizedCachedData.length > 0) {
              isFirstTurnRef.current = false
            }
          }
          setIsLoadingHistory(false)
          await new Promise(resolve => setTimeout(resolve, 0))
          return
        }

        // Cache miss or stale - fetch from API
        const url = new URL(
          `/api/aipane/conversations/${conversationId}`,
          window.location.origin
        )
        url.searchParams.set('limit', '50')
        url.searchParams.set('includeSteps', 'false')
        const response = await fetch(url.toString())
        if (response.status === 404) {
          // Conversation not found yet (e.g., first-load race). Treat as empty and continue.
          setIsLoadingHistory(false)
          return
        }
        if (!response.ok) throw new Error('Failed to load history')

        const data = await response.json()

        // Convert history to AI SDK v5 format; synthesize and merge tool steps into parts
        const historyMessages: UIMessage[] = (data.messages || [])
          .map((msg: any, idx: number) => {
            const ui = msg.uiMessage as UIMessage | undefined
            const fallbackParts: UIMessage['parts'] = msg.content
              ? [{ type: 'text' as const, text: msg.content }]
              : []
            const normalizedRole = (
              (ui?.role || msg.role || 'assistant') as string
            ).toLowerCase() as UIMessage['role']

            const parts =
              Array.isArray(ui?.parts) && ui.parts.length > 0
                ? (ui.parts as UIMessage['parts'])
                : fallbackParts

            const normalized: UIMessage = {
              ...(ui || {}),
              id: ui?.id || msg.id || `hist_${idx}_${Date.now()}`,
              role: normalizedRole,
              parts,
            }

            return normalized
          })
          .filter(Boolean)

        // Integrate history into useChat (prevents re-setting during streaming)
        if (!historySetRef.current) {
          chat.setMessages(historyMessages)
          historySetRef.current = true
          if (historyMessages.length > 0) {
            isFirstTurnRef.current = false
          }
        }

        // Store in localStorage cache
        saveToCache(conversationId, historyMessages)

        const loadTime = performance.now() - startTime
        console.log(
          `📚 [Chat Cache] Miss - Stored new data for ${conversationId} (${loadTime.toFixed(2)}ms)`
        )
      } catch (error) {
        console.error('Failed to load history:', error)
      } finally {
        setIsLoadingHistory(false)
      }
    }

    loadHistory()
  }, [conversationId, initialPromptSent, tab.aiPaneData?.prompt, chat.status])

  // Use AI SDK messages directly - no manual merging needed
  // The AI SDK v5 handles message persistence and tool calls automatically
  const displayMessages = chat.messages

  // --- User message outline (jump by selection) ---
  const userOutlineItems = useMemo(() => {
    const items: Array<{ idx: number; text: string }> = []
    for (let i = 0; i < chat.messages.length; i++) {
      const m = chat.messages[i] as any
      if ((m.role || '').toLowerCase() !== 'user') continue
      // Extract text from parts or content
      let text = ''
      if (Array.isArray(m.parts)) {
        text = m.parts
          .filter(
            (p: any) => p && (p.type === 'text' || p.type === 'text-delta')
          )
          .map((p: any) => (p.type === 'text-delta' ? p.textDelta : p.text))
          .join(' ')
      } else if (typeof m.content === 'string') {
        text = m.content
      }
      const snippet = text.trim().replace(/\s+/g, ' ').slice(0, 100)
      items.push({ idx: i, text: snippet || '(empty message)' })
    }
    return items
  }, [chat.messages])
  const handleJumpToIdx = useCallback((idx: number) => {
    const el = document.querySelector(
      `[data-msg-idx="${idx}"]`
    ) as HTMLElement | null
    if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }, [])

  // Check permissions after hooks are initialized
  if (!permissions.canCreateAiChat) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <FiMessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <h2 className="text-lg font-medium text-gray-500 mb-2">
            Chat Not Available
          </h2>
          <p className="text-sm text-gray-400">
            Upgrade your plan to access AI chat functionality
          </p>
        </div>
      </div>
    )
  }

  const showLoading = isLoadingHistory && displayMessages.length === 0
  const isEmpty = !showLoading && displayMessages.length === 0

  return (
    <div className="flex flex-col h-full min-h-0">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <FiMessageCircle className="w-5 h-5 text-blue-600" />
          <div>
            {isEditingTitle ? (
              <Input
                value={editableTitle}
                onChange={e => setEditableTitle(e.target.value)}
                onBlur={handleTitleSave}
                onKeyDown={e => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    handleTitleSave()
                  }
                }}
                className="h-7 text-sm px-2 py-1"
                autoFocus
              />
            ) : (
              <div className="flex items-center space-x-1 group">
                <h2 className="font-medium text-gray-900">
                  {(serverTitle && serverTitle.trim()) ||
                    (editableTitle && editableTitle.trim()) ||
                    'Untitled'}{' '}
                  {/* Prefer backend title, fallback to simple 'Untitled' without prefix */}
                </h2>
                <button
                  onClick={() => setIsEditingTitle(true)}
                  className="opacity-0 group-hover:opacity-100 transition-opacity text-gray-500 hover:text-gray-700"
                  title="Edit title"
                >
                  <FiEdit2 className="w-4 h-4" />
                </button>
              </div>
            )}
            {/* Context usage summary removed from header */}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                title="Outline of your questions"
              >
                <FiList className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-[420px] max-h-[60vh] overflow-y-auto p-0"
            >
              {userOutlineItems.length === 0 ? (
                <div className="text-sm text-gray-500 p-2">
                  No user messages yet.
                </div>
              ) : (
                userOutlineItems.map((item, i) => (
                  <DropdownMenuItem
                    key={`outline-${item.idx}`}
                    onClick={() => handleJumpToIdx(item.idx)}
                    className="whitespace-normal leading-snug py-2"
                  >
                    <span className="text-xs text-gray-500 mr-2">#{i + 1}</span>
                    <span className="text-gray-900">{item.text}</span>
                  </DropdownMenuItem>
                ))
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-500 hover:text-gray-700"
            onClick={() => setShowSettingsModal(true)}
            title="Chat settings"
          >
            <FiSettings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Body */}
      <div className="flex-1 min-h-0">
        {showLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex flex-col items-center gap-2 text-gray-500 animate-pulse">
              <FiMessageCircle className="w-8 h-8" />
              <span className="text-sm">Loading conversation...</span>
            </div>
          </div>
        ) : (
          <ChatMessageList
            messages={displayMessages.map((m, i) => ({ ...m, _idx: i }) as any)}
            isLoading={
              chat.status === 'streaming' || chat.status === 'submitted'
            }
            className="flex-1 min-h-0"
          />
        )}
      </div>

      {/* Input */}
      <ChatInput
        input={input}
        onInputChange={handleInputChange}
        onSubmit={handleSubmit}
        disabled={!permissions.canCreateAiMessage || !conversationId}
        isLoading={chat.status === 'streaming' || chat.status === 'submitted'}
        placeholder={
          !conversationId
            ? 'Start a chat to begin messaging...'
            : isEmpty
              ? 'Type your question...'
              : 'Message AI Assistant...'
        }
      />

      {/* Settings Modal */}
      <Dialog open={showSettingsModal} onOpenChange={setShowSettingsModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Chat Settings</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Model</span>
              <span className="text-gray-900">Server-controlled</span>
            </div>

            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Messages</span>
              <span className="text-gray-900">{displayMessages.length}</span>
            </div>

            <div className="text-sm flex items-center justify-between">
              <span className="font-medium text-gray-700">Conversation ID</span>
              <div className="flex items-center space-x-2">
                <span className="text-gray-900 font-mono text-xs">
                  {conversationId || 'Not created yet'}
                </span>
                {conversationId && (
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(conversationId)
                      toast.success('Conversation ID copied!')
                    }}
                    className="text-gray-500 hover:text-gray-700"
                    title="Copy conversation ID"
                  >
                    <FiCopy className="w-3 h-3" />
                  </button>
                )}
              </div>
            </div>

            {effectiveContextIds.length > 0 && (
              <div className="text-sm flex items-center justify-between">
                <span className="font-medium text-gray-700">Context Items</span>
                <button
                  onClick={() => {
                    setShowContextDialog(true)
                    setShowSettingsModal(false)
                  }}
                  className="text-blue-600 underline hover:text-blue-800"
                >
                  {effectiveContextIds.length} selected
                </button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Outline modal removed in favor of dropdown */}

      {/* Context Items Dialog */}
      <Dialog open={showContextDialog} onOpenChange={setShowContextDialog}>
        <DialogContent className="max-w-2xl max-h-[70vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>
              Context Items ({effectiveContextIds.length})
            </DialogTitle>
          </DialogHeader>
          <div className="overflow-y-auto space-y-2 max-h-[60vh]">
            {effectiveContextIds.map((nodeId, index) => {
              const node = findNodeById(nodeId)
              const nodeLabel = node?.label || `Item ${index + 1}`

              return (
                <div
                  key={nodeId}
                  className="p-3 border border-gray-200 rounded-lg bg-gray-50"
                >
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-gray-900">{nodeLabel}</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigateToTreeNode(nodeId)
                        setShowContextDialog(false)
                      }}
                    >
                      View
                    </Button>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Node ID: {nodeId}
                  </p>
                </div>
              )
            })}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Main export component that wraps with provider
export default function ChatTabContent({
  tab,
  dragTreeId: _dragTreeId,
}: ChatTabContentProps) {
  // const { updateTabAiPaneData } = useTabStore() // Disabled with auto-fix logic

  // Get conversation data to check for contextIds in metadata
  const [conversationData, setConversationData] = useState<any>(null)

  // Load conversation data to check metadata
  useEffect(() => {
    if (tab.aiPaneData?.conversationId) {
      fetch(`/api/aipane/conversations/${tab.aiPaneData.conversationId}`)
        .then(res => res.json())
        .then(data => setConversationData(data?.conversation || null))
        .catch(err => console.error('Failed to load conversation:', err))
    }
  }, [tab.aiPaneData?.conversationId])

  // Parse conversation metadata
  let _meta: any = conversationData?.metadata
  if (typeof _meta === 'string') {
    try {
      _meta = JSON.parse(_meta)
    } catch {
      _meta = undefined
    }
  }
  const contextIdsFromConversation = Array.isArray(_meta?.contextIds)
    ? (_meta.contextIds as string[])
    : []

  return (
    <ChatTabContentInternal
      tab={tab}
      conversationTitle={conversationData?.title ?? null}
      contextIdsFromConversation={contextIdsFromConversation}
    />
  )
}
