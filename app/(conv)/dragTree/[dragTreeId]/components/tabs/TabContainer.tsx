'use client'

import React, { useEffect, useRef, useState, Suspense } from 'react'
import {
  useWorkspaceLayoutStore,
  selectTabs,
  selectActiveTabId,
  type Tab,
  isCoreTabType,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import TabButton from '@/app/(conv)/dragTree/[dragTreeId]/components/tabs/TabButton'
import { cn } from '@/lib/utils'
const ResearchContainer = React.lazy(() =>
  import(
    '@/app/(conv)/dragTree/[dragTreeId]/components/research/ResearchContainer'
  ).then(m => ({ default: m.ResearchContainer }))
)
import ChatErrorBoundary from '@/app/(conv)/dragTree/[dragTreeId]/components/chat/ChatErrorBoundary'
import { Button } from '@/components/ui/button'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { getAssetType, isValidAssetType } from '@/lib/asset-types'
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  closestCenter,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  SortableContext,
  horizontalListSortingStrategy,
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Home, Zap, MessageCircle, Search, ChevronDown } from 'lucide-react'
import { useT } from '@/app/i18n/I18nProvider'

interface TabContainerProps {
  children: React.ReactNode
  className?: string
  dragTreeId: string
}

export const TabContainer: React.FC<TabContainerProps> = ({
  children,
  className,
  dragTreeId,
}) => {
  const tabs = useWorkspaceLayoutStore(selectTabs)
  const activeTabId = useWorkspaceLayoutStore(selectActiveTabId)
  const reorderTabs = useWorkspaceLayoutStore(state => state.reorderTabs)
  const setActiveTab = useWorkspaceLayoutStore(state => state.setActiveTab)
  const t = useT('dragtree.tab')

  // Scroll container ref and state
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(false)

  // Tab initialization is now handled by useDragTreeLoader via loadTabsFromMetadata

  // Check scroll state
  const checkScrollState = () => {
    const container = scrollContainerRef.current
    if (!container) return

    const { scrollLeft, scrollWidth, clientWidth } = container
    setCanScrollLeft(scrollLeft > 0)
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth)
  }

  // Update scroll state when tabs change
  useEffect(() => {
    checkScrollState()
    const container = scrollContainerRef.current
    if (container) {
      container.addEventListener('scroll', checkScrollState)
      return () => container.removeEventListener('scroll', checkScrollState)
    }
  }, [tabs])

  // Scroll functions
  const scrollLeft = () => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollBy({ left: -150, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    const container = scrollContainerRef.current
    if (container) {
      container.scrollBy({ left: 150, behavior: 'smooth' })
    }
  }

  // Auto-scroll to active tab when it changes or tabs list updates
  const scrollActiveTabIntoView = () => {
    if (!activeTabId) return

    const container = scrollContainerRef.current
    if (!container) return

    const activeTabElement = container.querySelector(
      `[data-tab-id="${activeTabId}"]`
    ) as HTMLElement | null

    if (!activeTabElement) return

    const containerRect = container.getBoundingClientRect()
    const tabRect = activeTabElement.getBoundingClientRect()

    // If the tab is already fully visible, no action required
    if (
      tabRect.left >= containerRect.left &&
      tabRect.right <= containerRect.right
    ) {
      return
    }

    // Calculate desired scroll so the tab is centered in the viewport
    const targetScrollLeft =
      activeTabElement.offsetLeft -
      container.clientWidth / 2 +
      activeTabElement.clientWidth / 2

    const clampedScrollLeft = Math.max(
      0,
      Math.min(targetScrollLeft, container.scrollWidth - container.clientWidth)
    )

    container.scrollTo({ left: clampedScrollLeft, behavior: 'smooth' })
  }

  useEffect(() => {
    // Delay to next animation frame to ensure dimensions are settled
    const id = requestAnimationFrame(scrollActiveTabIntoView)
    return () => cancelAnimationFrame(id)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTabId, tabs.length])

  return (
    <>
      {/* CSS for hiding scrollbars */}
      <style>{`
        .tab-scroll-container::-webkit-scrollbar {
          display: none;
        }
      `}</style>

      <div
        className={cn(
          'h-full max-w-full w-full flex flex-col min-h-0',
          className
        )}
      >
        {/* Tab Bar - always visible so layout doesn't shift when new tabs open */}
        <div className="flex items-center bg-gradient-to-b from-white via-slate-50 to-slate-100 border-b border-slate-200 shadow-sm min-h-[32px] max-w-full w-full">
          {/* Left Scroll Button */}
          {canScrollLeft && (
            <Button
              variant="ghost"
              size="sm"
              onClick={scrollLeft}
              className="flex-shrink-0 h-8 w-8 p-0 mx-1 hover:bg-slate-200"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
          )}

          {/* Quick Home: visible when Main is not active */}
          {activeTabId !== 'main' && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setActiveTab('main')}
              className="flex-shrink-0 h-8 w-8 p-0 mx-1 hover:bg-slate-200"
              title={t('goToMain', 'Go to visualization')}
              aria-label={t('goToMain', 'Go to visualization')}
            >
              <Home className="h-4 w-4" />
            </Button>
          )}

          {/* Tabs Container */}
          <div
            ref={scrollContainerRef}
            className="flex items-center overflow-x-auto flex-nowrap flex-1 tab-scroll-container"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
            }}
            onScroll={checkScrollState}
          >
            <DndContext
              sensors={useSensors(
                useSensor(PointerSensor, {
                  activationConstraint: { distance: 5 },
                })
              )}
              collisionDetection={closestCenter}
              onDragEnd={(event: DragEndEvent) => {
                const { active, over } = event
                if (active.id !== over?.id) {
                  const oldIndex = tabs.findIndex(t => t.id === active.id)
                  const newIndex = tabs.findIndex(t => t.id === over?.id)
                  if (oldIndex !== -1 && newIndex !== -1) {
                    reorderTabs(oldIndex, newIndex)
                  }
                }
              }}
            >
              <SortableContext
                items={tabs.map(t => t.id)}
                strategy={horizontalListSortingStrategy}
              >
                {tabs.map(tab => (
                  <SortableTabItem key={tab.id} tab={tab} />
                ))}
              </SortableContext>
            </DndContext>
          </div>

          {/* Right Scroll Button */}
          {canScrollRight && (
            <Button
              variant="ghost"
              size="sm"
              onClick={scrollRight}
              className="flex-shrink-0 h-8 w-8 p-0 mx-1 hover:bg-slate-200"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}

          {/* Open Tabs Dropdown - right aligned */}
          <div className="ml-auto mr-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 hover:bg-slate-200"
                  title={t('switchToOpenTab', 'Switch to open tab')}
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" sideOffset={6} className="w-64">
                {tabs.map(tab => (
                  <DropdownMenuItem
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className="flex items-center gap-2"
                  >
                    {/* Reuse tab icons by type */}
                    {tab.type === 'main' && (
                      <Home className="h-3.5 w-3.5 text-gray-500" />
                    )}
                    {tab.type === 'generate' && (
                      <Zap className="h-3.5 w-3.5 text-blue-500" />
                    )}
                    {tab.type === 'chat' && (
                      <MessageCircle className="h-3.5 w-3.5 text-green-500" />
                    )}
                    {tab.type === 'research' && (
                      <Search className="h-3.5 w-3.5 text-purple-500" />
                    )}
                    <span className="truncate flex-1">
                      {tab.title || tab.fullTitle || tab.id}
                    </span>
                    {activeTabId === tab.id && (
                      <span className="text-xs text-green-600">
                        {t('current', 'Current')}
                      </span>
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Tab Content */}
        <div className="flex-1 min-h-0 relative">
          {(() => {
            const active = tabs.find(t => t.id === activeTabId)
            if (!active) return null

            const isMainActive = isCoreTabType(active.type)
              ? active.type === 'main'
              : false

            const renderNonMainContent = () => {
              // Only render non-main content here; main is kept mounted persistently below
              if (isCoreTabType(active.type)) {
                if (active.type === 'research') {
                  return (
                    <Suspense fallback={<TabLoadingFallback />}>
                      <ResearchTabContent tab={active} />
                    </Suspense>
                  )
                }
                // main handled separately
                return null
              }

              // Handle asset types via registry
              const assetType = getAssetType(active.type)
              if (assetType) {
                const Component = assetType.tabComponent
                return (
                  <Suspense fallback={<TabLoadingFallback />}>
                    <ChatErrorBoundary
                      onRetry={() => {
                        window.location.reload()
                      }}
                    >
                      <Component tab={active} dragTreeId={dragTreeId} />
                    </ChatErrorBoundary>
                  </Suspense>
                )
              }

              // Unknown tab type
              return (
                <div className="h-full w-full p-4 flex items-center justify-center">
                  <div className="text-slate-400 italic">
                    {t('unknownTabType', 'Unknown tab type')}: {active.type}
                    {isValidAssetType(active.type)
                      ? ` (${t('registeredButMissing', 'Asset type registered but component not found')})`
                      : ` (${t('notRegistered', 'Not a registered asset type')})`}
                  </div>
                </div>
              )
            }

            return (
              <div className="h-full w-full flex flex-col min-h-0">
                {/* Persistent main tab (Visual Flow Diagram) kept mounted to preserve internal state/viewport */}
                <div
                  className={cn('h-full w-full', isMainActive ? '' : 'hidden')}
                >
                  {children}
                </div>

                {/* Active non-main tab content (mounted/unmounted normally) */}
                {!isMainActive && (
                  <div className="h-full w-full">{renderNonMainContent()}</div>
                )}
              </div>
            )
          })()}
        </div>
      </div>
    </>
  )
}

// Memoize ResearchTabContent to prevent unnecessary re-renders
const ResearchTabContent: React.FC<{ tab: Tab }> = React.memo(({ tab }) => {
  const t = useT('dragtree.tab')
  if (!tab.nodeId || !tab.contentId) {
    return (
      <div className="h-full w-full p-4 flex items-center justify-center">
        <div className="text-slate-400 italic">
          {t('noContent', 'No content available')}
        </div>
      </div>
    )
  }

  return (
    <ResearchContainer
      nodeId={tab.nodeId}
      contentId={tab.contentId}
      fullTitle={tab.fullTitle}
      variant="tab"
      editorType="tiptap"
      showHeader={true}
      showNavigation={true}
      showSearchResults={true}
      showStatusFooter={true}
      placeholder="Research content will appear here..."
    />
  )
})

ResearchTabContent.displayName = 'ResearchTabContent'

// Loading fallback for dynamically loaded tab components
const TabLoadingFallback: React.FC = () => {
  const t = useT('dragtree.tab')
  return (
    <div className="h-full w-full p-4 flex items-center justify-center">
      <div className="flex flex-col items-center gap-3 text-gray-500">
        <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
        <span className="text-sm">
          {t('loading', 'Loading tab content...')}
        </span>
      </div>
    </div>
  )
}

// Memoize SortableTabItem to prevent unnecessary re-renders
const SortableTabItem: React.FC<{ tab: Tab }> = React.memo(({ tab }) => {
  const activeTabId = useWorkspaceLayoutStore(selectActiveTabId)
  const setActiveTab = useWorkspaceLayoutStore(state => state.setActiveTab)
  const removeTabWithCleanup = useWorkspaceLayoutStore(
    state => state.removeTabWithCleanup
  )
  const { attributes, listeners, setNodeRef, transform, transition } =
    useSortable({ id: tab.id, disabled: tab.type === 'main' })

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      data-tab-id={tab.id}
      {...attributes}
      {...listeners}
    >
      <TabButton
        tab={tab}
        isActive={activeTabId === tab.id}
        onSelect={() => setActiveTab(tab.id)}
        onClose={() => removeTabWithCleanup(tab.id)}
      />
    </div>
  )
})

SortableTabItem.displayName = 'SortableTabItem'
