'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import {
  useWorkspaceLayoutStore,
  type AiPaneType,
  selectAiPaneType,
} from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'
import { FiMessageCircle, FiZap } from 'react-icons/fi'

const TypeSelection: React.FC = () => {
  // Granular store subscriptions - only subscribe to what we actually use
  const currentType = useWorkspaceLayoutStore(selectAiPaneType)
  const setType = useWorkspaceLayoutStore(state => state.setType)

  const typeOptions: Array<{
    type: AiPaneType
    label: string
    description: string
    icon: React.ComponentType<{ className?: string }>
  }> = [
    {
      type: 'generate',
      label: 'Generate',
      description: 'Create structured content',
      icon: FiZap,
    },
    {
      type: 'chat',
      label: 'Chat',
      description: 'AI conversation',
      icon: FiMessageCircle,
    },
  ]

  return (
    <div className="flex gap-2">
      {typeOptions.map(({ type, label, icon: Icon }) => (
        <button
          key={type}
          onClick={() => setType(type)}
          className={cn(
            'flex-1 h-9 px-3 rounded-md border text-center transition-all duration-200',
            'hover:border-blue-300 hover:bg-blue-50',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1',
            'flex items-center justify-center space-x-2',
            currentType === type
              ? 'border-blue-500 bg-blue-50 shadow-sm text-blue-900'
              : 'border-gray-200 bg-white text-gray-700'
          )}
        >
          <Icon
            className={cn(
              'w-3 h-3',
              currentType === type ? 'text-blue-600' : 'text-gray-500'
            )}
          />
          <span
            className={cn(
              'text-sm font-medium',
              currentType === type ? 'text-blue-900' : 'text-gray-700'
            )}
          >
            {label}
          </span>
        </button>
      ))}
    </div>
  )
}

export default React.memo(TypeSelection)
