# Chat Components - AI SDK v5 Compatible

This directory contains the chat implementation that properly handles AI SDK v5 message format with tool calls and streaming.

## Key Improvements over v1

### 1. **Proper Tool Call Display**

- Uses AI SDK Elements (`Tool`, `ToolHeader`, `ToolContent`, `ToolInput`, `ToolOutput`)
- Tool calls appear as separate parts in message structure
- No more empty messages when tools are used

### 2. **AI SDK v5 Native Support**

- Uses `UIMessage` format with `parts` array
- Supports `text`, `text-delta`, `reasoning`, and tool UI parts
- Compatible with `useChat` hook from `@ai-sdk/react`

### 3. **Enhanced Message Rendering**

- Uses AI SDK Elements for consistent UI
- Proper streaming indicators
- Auto-scroll functionality with scroll-to-bottom button

## Components

### ChatTabContent.tsx

**Main chat interface component**

- Manages conversation lifecycle using `useAiConversation`
- Handles permissions and user tier validation
- Provides empty state and start chat functionality
- Integrates with tab store for conversation persistence

**Key Features:**

- Permission-based access control
- Context document integration
- Conversation ID management
- Real-time streaming support
- History loader consumes persisted `uiMessage` blobs from `/api/aipane/conversations/[id]` (no client-side step merging required)

### ChatMessageList.tsx

**Message rendering component using AI SDK Elements**

- Uses `Conversation`, `ConversationContent`, `ConversationScrollButton`
- Renders `Message` and `MessageContent` with proper styling
- Builds a numbered timeline for tool + reasoning steps via `buildQuickResearchTimelineParts(...)` and `QuickResearchTimelineStep`
- Handles all message part types:
  - `text` / `text-delta` – rendered with `Response`
  - `reasoning` – wrapped in the collapsible `Reasoning` component
  - Tool UI parts – rendered inside the timeline using `ToolHeader`, `ToolInput`, and `ToolOutput`

**Key Features:**

- Auto-scroll to bottom on new messages
- Loading indicators for streaming
- Graceful handling of unknown part types
- System message filtering

### ChatInput.tsx

**Input component using AI SDK Elements**

- Uses `PromptInput`, `PromptInputTextarea`, `PromptInputSubmit`
- Character limit validation
- Keyboard shortcuts (Enter to send, Shift+Enter for new line)
- Loading state management

**Key Features:**

- Input validation with visual feedback
- Character counter
- Disabled state handling
- Consistent styling with AI SDK Elements

## Usage

### Basic Integration

```typescript
import ChatTabContent from './ChatTabContent'

// Replace existing ChatTabContent with v2
<ChatTabContent
  tab={tab}
  dragTreeId={dragTreeId}
/>
```

### Message Structure

Chat uses AI SDK v5 `UIMessage` format:

```typescript
{
  id: 'msg_123',
  role: 'assistant',
  parts: [
    {
      type: 'text',
      text: 'I\'ll help you with that. Let me search for information.'
    },
    {
      type: 'tool-call',
      toolCallId: 'call_123',
      toolName: 'web_search',
      args: { query: 'search term' },
      state: 'in-progress'
    },
    {
      type: 'tool-result',
      toolCallId: 'call_123',
      result: { /* search results */ },
      state: 'complete'
    },
    {
      type: 'text',
      text: 'Based on my search, here\'s what I found...'
    }
  ]
}
```

### Tool Call Rendering

Tool calls and reasoning steps are grouped into a numbered timeline:

```typescript
const timeline = buildQuickResearchTimelineParts(message.parts)

return timeline.map((step, index) => (
  <QuickResearchTimelineStep
    key={step.id}
    stepNumber={index + 1}
    isLast={index === timeline.length - 1}
  >
    {step.kind === 'reasoning' ? (
      <Reasoning>
        <ReasoningTrigger title={step.parsed.title} />
        <ReasoningContent>{step.parsed.body}</ReasoningContent>
      </Reasoning>
    ) : (
      <Tool>
        <ToolHeader
          type={step.source.type as ToolUIPart['type']}
          state={step.source.state as ToolUIPart['state']}
        />
        <ToolContent>
          {step.source.input && <ToolInput input={step.source.input} />}
          <ToolOutput
            output={step.source.output}
            errorText={step.source.errorText}
          />
        </ToolContent>
      </Tool>
    )}
  </QuickResearchTimelineStep>
))
```

## API Integration

Chat components work with the `/api/aipane/chat` endpoint:

```typescript
// useAiConversation hook configuration
const conversation = useAiConversation({
  conversationId: 'thread_abc123',
  apiEndpoint: '/api/aipane/chat'
  model: 'gpt-4.1',
  context: contextContent(),
  contextIds: ['doc_1', 'doc_2'],
  enabled: true,
})
```

## Migration from v1

### Component Replacement

```typescript
// ❌ Old v1 components
import ChatTabContent from './ChatTabContent'
import SimpleMessageList from './SimpleMessageList'
import SimpleChatInput from './SimpleChatInput'

// ✅ New v2 components
import ChatTabContent from './ChatTabContent'
import ChatMessageList from './ChatMessageList'
import ChatInput from './ChatInput'
```

### Hook Replacement

```typescript
// ❌ Old v1 hook
import { useAiConversation } from '../hooks/useAiConversation'

// ✅ New v2 hook
import { useAiConversation } from '../hooks/useAiConversation'
```

### API Endpoint Update

```typescript
// ❌ Old v1 endpoint
apiEndpoint: '/api/aipane/chat'

// ✅ New v2 endpoint
apiEndpoint: '/api/aipane/chat'
```

## Benefits

1. **Tool Calls Work**: No more empty messages when AI uses tools
2. **Better UX**: Proper loading states and streaming indicators
3. **Consistent UI**: Uses AI SDK Elements for standardized components
4. **Future-Proof**: Built for AI SDK v5 with proper message structure
5. **Maintainable**: Cleaner code structure with better separation of concerns

## Testing

To test tool call functionality:

1. Start a chat in the v2 interface
2. Ask questions that require web search: "What's the weather today?"
3. Verify tool calls appear as separate UI elements
4. Confirm final AI response incorporates tool results
5. Check that streaming works smoothly without interruption
