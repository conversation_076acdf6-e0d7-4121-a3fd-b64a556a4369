'use client'

import React from 'react'
import { <PERSON><PERSON><PERSON>, FiArrowDown } from 'react-icons/fi'
import type { UIMessage, ToolUIPart } from 'ai'
import {
  Conversation,
  ConversationContent,
  ConversationScrollButton,
} from '@/components/ai-elements/conversation'
import { Message, MessageContent } from '@/components/ai-elements/message'
import { Response } from '@/components/ai-elements/response'
import {
  Too<PERSON>,
  Tool<PERSON>eader,
  ToolContent,
  ToolInput,
  ToolOutput,
} from '@/components/ai-elements/tool'
import {
  Reasoning,
  ReasoningTrigger,
  ReasoningContent,
} from '@/components/ai-elements/reasoning'
import {
  QuickResearchTimelineStep,
  buildQuickResearchTimelineParts,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchTimeline'

type ChatMessageListProps = {
  messages: UIMessage[]
  isLoading?: boolean
  className?: string
}

export default function ChatMessageList({
  messages,
  isLoading = false,
  className = '',
}: ChatMessageListProps) {
  // StickToBottom handles scrolling within the chat container.
  // Avoid window-level scrollIntoView to prevent the entire page from jumping.

  // Filter out system messages for display
  const displayMessages = messages.filter(message => message.role !== 'system')

  return (
    <div className={`flex flex-col h-full ${className}`}>
      <Conversation className="flex-1">
        <ConversationContent>
          {displayMessages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <FiCpu className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p className="text-lg font-medium">Start a conversation</p>
                <p className="text-sm">
                  Send a message to begin chatting with AI
                </p>
              </div>
            </div>
          ) : (
            displayMessages.map((message, messageIndex) => {
              // Normalize to parts array for rendering. Some client messages may only have `content`.
              const partsForGrouping: any[] = (() => {
                const m: any = message as any
                if (Array.isArray(m.parts) && m.parts.length > 0) return m.parts
                if (
                  typeof m.content === 'string' &&
                  m.content.trim().length > 0
                ) {
                  return [{ type: 'text', text: m.content }]
                }
                if (Array.isArray(m.content)) {
                  return m.content.map((c: any) =>
                    typeof c === 'string' ? { type: 'text', text: c } : c
                  )
                }
                return []
              })()

              const textParts = partsForGrouping.filter(
                (part: any) =>
                  part &&
                  (part.type === 'text' ||
                    part.type === 'text-delta' ||
                    (typeof part.type === 'string' &&
                      part.type.startsWith('text-')))
              )

              const timelineParts =
                buildQuickResearchTimelineParts(partsForGrouping)

              return (
                <Message
                  key={message.id}
                  from={message.role}
                  className=""
                  data-msg-idx={(message as any)._idx}
                >
                  <MessageContent className="p-2">
                    {timelineParts.length > 0 && (
                      <div className="mb-3 space-y-2">
                        {timelineParts.map((timelinePart, timelineIndex) => {
                          const isLast =
                            timelineIndex === timelineParts.length - 1

                          if (timelinePart.kind === 'reasoning') {
                            return (
                              <QuickResearchTimelineStep
                                key={timelinePart.id}
                                stepNumber={timelineIndex + 1}
                                isLast={isLast}
                              >
                                <Reasoning
                                  isStreaming={
                                    isLoading &&
                                    messageIndex === displayMessages.length - 1
                                  }
                                  defaultOpen={false}
                                >
                                  <ReasoningTrigger
                                    title={timelinePart.parsed.title}
                                  />
                                  <ReasoningContent>
                                    {timelinePart.parsed.body ?? ''}
                                  </ReasoningContent>
                                </Reasoning>
                              </QuickResearchTimelineStep>
                            )
                          }

                          const part = timelinePart.source
                          const toolType =
                            typeof part.type === 'string' &&
                            part.type.startsWith('tool-')
                              ? (part.type as ToolUIPart['type'])
                              : ('tool-generic' as ToolUIPart['type'])
                          const validStates: ToolUIPart['state'][] = [
                            'input-streaming',
                            'input-available',
                            'output-available',
                            'output-error',
                          ]
                          const toolState = validStates.includes(
                            part.state as ToolUIPart['state']
                          )
                            ? (part.state as ToolUIPart['state'])
                            : 'output-available'
                          const toolInput = part.input as ToolUIPart['input']
                          const toolOutput = part.output as ToolUIPart['output']
                          const toolError =
                            part.errorText as ToolUIPart['errorText']

                          return (
                            <QuickResearchTimelineStep
                              key={timelinePart.id}
                              stepNumber={timelineIndex + 1}
                              isLast={isLast}
                            >
                              <Tool className="mb-1 text-xs">
                                <ToolHeader
                                  type={toolType}
                                  state={toolState}
                                  className="px-2 py-1.5"
                                />
                                <ToolContent>
                                  {toolInput !== undefined && (
                                    <div className="px-2 pb-1">
                                      <ToolInput input={toolInput} />
                                    </div>
                                  )}
                                  <ToolOutput
                                    output={toolOutput as any}
                                    errorText={toolError}
                                    className="text-xs"
                                  />
                                </ToolContent>
                              </Tool>
                            </QuickResearchTimelineStep>
                          )
                        })}
                      </div>
                    )}

                    {/* Render main text content */}
                    {textParts.map((part, partIndex) => {
                      // Handle text parts
                      if (part.type === 'text') {
                        return (
                          <Response
                            key={`${message.id}-text-${partIndex}`}
                            defaultOrigin={
                              typeof window !== 'undefined'
                                ? window.location.origin
                                : undefined
                            }
                          >
                            {part.text}
                          </Response>
                        )
                      }

                      // Handle text-delta parts (streaming text)
                      if ((part as any).type === 'text-delta') {
                        return (
                          <Response
                            key={`${message.id}-text-delta-${partIndex}`}
                            defaultOrigin={
                              typeof window !== 'undefined'
                                ? window.location.origin
                                : undefined
                            }
                          >
                            {(part as any).textDelta}
                          </Response>
                        )
                      }

                      // Handle unknown part types gracefully
                      console.warn(
                        'Unknown message part type:',
                        part.type,
                        part
                      )
                      return null
                    })}

                    {/* Show loading indicator for streaming messages */}
                    {isLoading &&
                      messageIndex === displayMessages.length - 1 && (
                        <div className="flex items-center gap-2 text-xs text-gray-500 mt-2">
                          <div className="flex space-x-1">
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" />
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce delay-100" />
                            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce delay-200" />
                          </div>
                          <span>AI is thinking...</span>
                        </div>
                      )}
                  </MessageContent>
                </Message>
              )
            })
          )}

          {/* StickToBottom internally manages the scroll anchor */}
        </ConversationContent>

        {/* Scroll to bottom button */}
        <ConversationScrollButton>
          <FiArrowDown className="w-4 h-4" />
        </ConversationScrollButton>
      </Conversation>
    </div>
  )
}
