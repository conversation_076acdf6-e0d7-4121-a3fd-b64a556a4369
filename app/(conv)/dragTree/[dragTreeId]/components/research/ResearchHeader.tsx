'use client'

import React from 'react'
import { cn } from '@/lib/utils'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import {
  SourceCitations,
  hasSearchResults,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/SearchResultsDisplay'
import { TreeNode } from '@/app/types'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { FiList } from 'react-icons/fi'
import { Button } from '@/components/ui/button'
import QuickResearchStepsDialogBody from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchStepsDialogBody'
import { Copy } from 'lucide-react'
import toast from 'react-hot-toast'
import { useSession } from 'next-auth/react'
import { extractNodeContentText } from '@/app/utils/nodeContent'
import { logEventWithContext } from '@/app/libs/logging'

export interface ResearchHeaderProps {
  nodeId: string
  contentId: string
  fullTitle: string
  variant?: 'default' | 'compact' | 'tab'
  showNavigation?: boolean
  showSearchResults?: boolean
  className?: string
}

// Utility function to find a category node by matching its label in the tree
const findCategoryNodeByLabel = (
  tree: TreeNode | null,
  categoryLabel: string
): TreeNode | null => {
  if (!tree) return null

  // Check if current node is a category with matching label
  if (tree.type === 'category' && tree.label === categoryLabel) {
    return tree
  }

  // Recursively search in children
  for (const child of tree.children) {
    const found = findCategoryNodeByLabel(child, categoryLabel)
    if (found) return found
  }

  return null
}

/**
 * Reusable research header component
 * Displays title, category path, and search results
 * Can be used in both main research display and tab views
 */
export const ResearchHeader: React.FC<ResearchHeaderProps> = ({
  nodeId,
  contentId,
  fullTitle,
  variant = 'default',
  showNavigation = true,
  showSearchResults = true,
  className,
}) => {
  const { navigateToTreeNodeFromReactFlow } = useNavigationStore()
  // Optimized: Use individual selectors to prevent unnecessary re-renders
  // Subscribe directly to this content record so header re-renders when messages load
  const storedNodeContent = useDragTreeStore(state =>
    state.nodeContent.get(nodeId)?.get(contentId)
  )
  const fetchNodeContent = useDragTreeStore(state => state.fetchNodeContent)
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const dragTreeId = useDragTreeStore(state => state.dragTreeId)
  const { data: session } = useSession()

  // Parse the full title to separate category path from question
  const parseTitle = (fullTitle: string) => {
    const parts = fullTitle.split(' > ')
    if (parts.length > 1) {
      const questionPart = parts[parts.length - 1]
      const categoryPath = parts.slice(0, -1).join(' > ')
      const categoryParts = parts.slice(0, -1) // Individual category labels
      return { categoryPath, questionPart, categoryParts }
    }
    return { categoryPath: '', questionPart: fullTitle, categoryParts: [] }
  }

  const { categoryPath, questionPart, categoryParts } = parseTitle(fullTitle)

  // Get search results for this content
  const searchResults =
    storedNodeContent?.metadata && hasSearchResults(storedNodeContent.metadata)
      ? storedNodeContent.metadata.searchResults
      : null
  // Prefer messages column (UIMessage[]) for Steps; fallback to metadata.messages
  const stepMessages = Array.isArray(storedNodeContent?.messages)
    ? (storedNodeContent!.messages as any[])
    : storedNodeContent?.metadata &&
        Array.isArray((storedNodeContent.metadata as any).messages)
      ? ((storedNodeContent.metadata as any).messages as any[])
      : []

  const hasCitations = !!(searchResults && searchResults.length > 0)
  const hasSteps = Array.isArray(stepMessages) && stepMessages.length > 0
  // Only show Steps button when content text has loaded to avoid empty modal
  const hasContentText = Boolean(
    storedNodeContent?.contentText &&
      storedNodeContent.contentText.trim().length > 0
  )
  // Prefetch steps once content is present but steps missing
  React.useEffect(() => {
    if (hasContentText && contentId && !hasSteps) {
      fetchNodeContent(nodeId, contentId).catch(e =>
        console.warn('Failed to prefetch steps in header:', e)
      )
    }
  }, [hasContentText, contentId, hasSteps, nodeId, fetchNodeContent])

  // Show Steps only when we actually have steps
  const shouldShowStepsButton = hasSteps

  // Determine if copy button should be enabled (tab variant only)
  const canCopy = React.useMemo(() => {
    if (!storedNodeContent) return false
    const text = extractNodeContentText(storedNodeContent)
    return typeof text === 'string' && text.trim().length > 0
  }, [storedNodeContent])

  const userId = (session?.user as { id?: string } | undefined)?.id

  const handleCopy = async () => {
    const textToCopy = extractNodeContentText(storedNodeContent || {})
    const trimmed = (textToCopy || '').trim()
    if (!trimmed) {
      toast.error('No quick research content available to copy yet.')
      logEventWithContext(
        'click_copy_quickResearch_empty',
        userId,
        dragTreeId || undefined,
        {
          node_id: nodeId,
          content_id: contentId ?? null,
        }
      )
      return
    }

    try {
      if (
        typeof navigator === 'undefined' ||
        !navigator.clipboard ||
        typeof navigator.clipboard.writeText !== 'function'
      ) {
        throw new Error('Clipboard API unavailable')
      }

      await navigator.clipboard.writeText(trimmed)
      toast.success('Quick research copied to clipboard.')
      logEventWithContext(
        'click_copy_quickResearch',
        userId,
        dragTreeId || undefined,
        {
          node_id: nodeId,
          content_id: contentId ?? null,
          copy_origin: 'stored',
          copied_length: trimmed.length,
        }
      )
    } catch (error) {
      console.error('Failed to copy quick research content:', error)
      toast.error('Unable to copy quick research. Try again.')
      logEventWithContext(
        'click_copy_quickResearch_failure',
        userId,
        dragTreeId || undefined,
        {
          node_id: nodeId,
          content_id: contentId ?? null,
          copy_origin: 'stored',
          error_message: error instanceof Error ? error.message : 'unknown',
        }
      )
    }
  }

  const handleNavigateToNode = () => {
    if (showNavigation && nodeId) {
      navigateToTreeNodeFromReactFlow(nodeId)
    }
  }

  const handleNavigateToCategory = (categoryLabel: string) => {
    if (!showNavigation || !frontendTreeStructure) return

    const categoryNode = findCategoryNodeByLabel(
      frontendTreeStructure,
      categoryLabel
    )
    if (categoryNode) {
      navigateToTreeNodeFromReactFlow(categoryNode.id)
    }
  }

  // Get variant-specific styles
  const getVariantStyles = () => {
    switch (variant) {
      case 'compact':
        return {
          categoryText: 'text-xs text-slate-500 mb-1',
          questionText: 'text-sm font-medium text-slate-700',
          container: 'mb-2',
        }
      case 'tab':
        return {
          categoryText: 'text-xs text-slate-500 mb-1',
          questionText: 'text-sm font-medium text-slate-700',
          container: 'mb-4',
        }
      default:
        return {
          categoryText: 'text-sm text-slate-500 mb-1',
          questionText: 'text-lg font-semibold text-slate-800',
          container: 'mb-4',
        }
    }
  }

  const styles = getVariantStyles()

  return (
    <div className={cn(styles.container, className)}>
      {/* Category Path - clickable parts */}
      {categoryPath && (
        <div
          className={cn(
            styles.categoryText,
            'flex items-center justify-between gap-2'
          )}
        >
          <div className="flex items-center gap-1 flex-wrap">
            {categoryParts.map((categoryLabel, index) => (
              <React.Fragment key={index}>
                <button
                  onClick={() => handleNavigateToCategory(categoryLabel)}
                  className={cn(
                    'hover:text-blue-600 hover:underline transition-colors cursor-pointer',
                    showNavigation ? 'text-slate-600' : 'text-slate-500'
                  )}
                  title={
                    showNavigation
                      ? `Navigate to "${categoryLabel}" category`
                      : categoryLabel
                  }
                >
                  {categoryLabel}
                </button>
                {index < categoryParts.length - 1 && (
                  <span className="text-slate-400">›</span>
                )}
              </React.Fragment>
            ))}
          </div>

          {/* Controls moved to category line: search icons and Steps button */}
          <div className="flex items-center gap-2">
            {variant === 'tab' && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 pl-2 pr-2 text-xs font-normal border-0 bg-gray-50/60 text-gray-600 hover:bg-gray-100/80 hover:text-gray-800"
                title={
                  canCopy
                    ? 'Copy quick research to clipboard'
                    : 'Content not ready to copy yet'
                }
                onClick={handleCopy}
                disabled={!canCopy}
              >
                <Copy className="h-3 w-3 mr-1" /> Copy
              </Button>
            )}
            {showSearchResults && hasCitations && (
              <SourceCitations
                searchResults={searchResults}
                className="text-xs"
                as="div"
                iconSize="sm"
              />
            )}

            {shouldShowStepsButton && (
              <Dialog>
                <DialogTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 pl-2 pr-2 text-xs font-normal border-0 bg-gray-50/60 text-gray-600 hover:bg-gray-100/80 hover:text-gray-800"
                    title="Show research steps"
                    onClick={async () => {
                      if (contentId) {
                        try {
                          await fetchNodeContent(nodeId, contentId)
                        } catch (e) {
                          console.warn('Failed to load steps on click:', e)
                        }
                      }
                    }}
                  >
                    <FiList className="h-3 w-3 mr-1" /> Steps
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[80vh] p-0">
                  <QuickResearchStepsDialogBody messages={stepMessages || []} />
                </DialogContent>
              </Dialog>
            )}
          </div>
        </div>
      )}

      {/* Question Title */}
      <div className="flex items-start gap-2 flex-wrap">
        <h2
          className={cn(
            styles.questionText,
            'flex-1 min-w-0 leading-relaxed break-words',
            showNavigation &&
              'cursor-pointer hover:text-blue-600 transition-colors'
          )}
          title={
            showNavigation
              ? 'Click to navigate to this node in the outline'
              : fullTitle
          }
          onClick={showNavigation ? handleNavigateToNode : undefined}
          style={{
            wordWrap: 'break-word',
            overflowWrap: 'break-word',
            whiteSpace: 'normal',
          }}
        >
          {questionPart}
        </h2>

        {/* Controls moved above; nothing on the right here for more space */}
      </div>
    </div>
  )
}
