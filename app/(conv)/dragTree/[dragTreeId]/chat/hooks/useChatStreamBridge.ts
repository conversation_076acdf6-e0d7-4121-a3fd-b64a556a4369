'use client'

import { useRef, useEffect, useCallback } from 'react'
import { useChat } from '@ai-sdk/react'
import { DefaultChatTransport } from 'ai'
import { UI_STREAM_THROTTLE_MS } from '@/constants/chat'
import type { UIMessage } from 'ai'
// Note: Request body is assembled inline; API validates/normalizes server-side
import { ENABLE_CHAT_DEBUG_LOGGING } from '@/app/configs/feature-flags'

export type UseChatStreamBridgeOptions = {
  conversationId?: string
  apiEndpoint: string
  model: string
  contextIds: string[]
  getContext: () => string
  enabled?: boolean
}

export type UseChatStreamBridgeReturn = {
  messages: UIMessage[]
  isStreaming: boolean
  error: Error | null | undefined
  sendMessage: ((options: { text: string }) => void) | null
  status: 'idle' | 'streaming' | 'error' | 'submitted' | 'ready'
}

/**
 * Hook that wraps Vercel AI SDK useChat with conversation ID gating
 * and proper request preparation for the chat API.
 *
 * Only mounts the SDK hook when conversationId is available to prevent
 * "pending id" issues and race conditions.
 */
export function useChatStreamBridge({
  conversationId,
  apiEndpoint,
  model,
  contextIds,
  getContext,
  enabled = true,
}: UseChatStreamBridgeOptions): UseChatStreamBridgeReturn {
  const isFirstMessageRef = useRef<boolean>(true)
  const contextRef = useRef<string>('')

  // Only instantiate useChat when we have a conversation ID
  const shouldMountChat = Boolean(conversationId && enabled)

  // Memoize context to avoid re-evaluation on every render
  const memoizedGetContext = useCallback(() => {
    if (!contextRef.current) {
      contextRef.current = getContext()
    }
    return contextRef.current
  }, [getContext])

  const chat = useChat({
    id: conversationId || 'disabled',
    transport: shouldMountChat
      ? new DefaultChatTransport({
          api: apiEndpoint,
          prepareSendMessagesRequest: ({ messages: outgoing }) => {
            if (!conversationId) {
              throw new Error('Conversation ID not available')
            }

            // Extract text from the last message (user's input)
            const lastMessage = outgoing[outgoing.length - 1] as any
            let text = ''

            if (Array.isArray(lastMessage?.parts)) {
              const textPart = lastMessage.parts.find(
                (p: any) => p?.type === 'text'
              )
              text = textPart?.text || ''
            } else if (typeof lastMessage?.content === 'string') {
              text = lastMessage.content
            }

            // Prepare the chat request body
            const body = {
              message: text,
              conversationId,
              model,
              contextIds,
              // Only include context on the first message to reduce payload size
              ...(isFirstMessageRef.current && {
                context: memoizedGetContext(),
              }),
            }

            // Mark that we've sent the first message
            isFirstMessageRef.current = false

            if (ENABLE_CHAT_DEBUG_LOGGING) {
              console.log('🚀 [useChatStreamBridge] Sending message:', {
                conversationId,
                messageLength: text.length,
                isFirstMessage: !isFirstMessageRef.current,
                contextIds: contextIds.length,
              })
            }

            return { body }
          },
          // Enable throttling if available to reduce re-renders
          ...(typeof (DefaultChatTransport as any).experimental_throttle ===
            'number' && {
            experimental_throttle: UI_STREAM_THROTTLE_MS,
          }),
        })
      : undefined,
  })

  // Reset first message flag and context when conversation ID changes
  useEffect(() => {
    if (conversationId) {
      isFirstMessageRef.current = true
      contextRef.current = '' // Reset context cache
    }
  }, [conversationId])

  // Return appropriate values based on whether chat is mounted
  if (!shouldMountChat) {
    return {
      messages: [],
      isStreaming: false,
      error: null,
      sendMessage: null,
      status: 'idle',
    }
  }

  return {
    messages: chat.messages,
    isStreaming: chat.status === 'streaming' || chat.status === 'submitted',
    error: chat.error,
    sendMessage: ({ text }: { text: string }) => {
      if (!text.trim()) return

      if (ENABLE_CHAT_DEBUG_LOGGING) {
        console.log(
          '📤 [useChatStreamBridge] Sending message:',
          text.substring(0, 100)
        )
      }

      chat.sendMessage({ text })
    },
    status: chat.status as any,
  }
}
