import React from 'react'
import { contactEmail } from '@/app/configs'

const PrivacyNotice = () => {
  return (
    <div className="px-4 max-w-2xl">
      <h1 className="text-3xl font-bold mb-4">Privacy Notice</h1>

      <h2 className="text-2xl font-bold mb-2">Last updated: Oct 20, 2023</h2>

      <p className="m-8">
        Note: By using our website and platform, you agree to and accept the
        practices described in this Privacy Policy and our Terms of Service.
      </p>

      <p className="mb-4">
        Thought Atlas (&quot;we&quot;, &quot;us&quot;, or &quot;our&quot;),
        operated by BUJU Studio LLC, values the privacy of its users
        (&quot;User,&quot; &quot;you,&quot; or &quot;your&quot;). This document
        outlines our practices regarding your information on the Thought Atlas
        platform through our website. Please read carefully to understand our
        policies and practices concerning your information.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">
        1. Collection of Personal Information
      </h2>
      <p className="mb-4">
        We collect personal details during registration, through queries, and
        postings on our platform. This includes your name, address, online
        contact information, geolocation, and equipment information. Financial
        data is not stored as a third-party payment processor is utilized.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">
        2. Usage of Collected Information
      </h2>
      <p className="mb-4">
        The collected information is used to personalize your experience,
        provide requested services, notify you about account changes, and send
        periodic emails for marketing or other purposes.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">3. Protection Measures</h2>
      <p className="mb-4">
        We employ regular security and malware scans, along with PCI Compliance
        measures to ensure the security of your data. Employees requiring access
        are authenticated, and third-party access is actively prevented.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">4. Data Security</h2>
      <p className="mb-4">
        Reasonable measures are in place to prevent unauthorized access,
        alteration, or disclosure of personal information. Users are responsible
        for keeping their access passwords confidential.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">
        5. Disclosure of Personal Information
      </h2>
      <p className="mb-4">
        Your information is shared with contractors, service providers, and
        third parties (Partners) to provide services. This includes third-party
        AI models like OpenAI for specific functionalities. Personal Information
        may also be disclosed to comply with legal obligations or to protect
        Thought Atlas's rights.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">
        6. Third-Party Service Providers
      </h2>
      <p className="mb-4">
        Our third-party service providers include OpenAI, AWS, Supabase, Vercel,
        and Google Cloud. For any questions or concerns regarding our
        third-party service providers, please email us at {contactEmail}.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">
        7. User Rights and Compliance
      </h2>
      <p className="mb-4">
        We adhere to Fair Information Practice Principles, CAN-SPAM Act of 2003,
        and COPPA for children under 13. Special sections for Canadian and
        California residents detailing their rights and how to exercise them are
        also included.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">8. Modification Rights</h2>
      <p className="mb-4">
        Thought Atlas reserves the right to modify this Privacy Policy,
        notifying users through email or on the platform.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">
        9. Copyright Infringement Notice
      </h2>
      <p className="mb-4">
        A process is provided for users to report copyright infringement. Please
        send a written notification to {contactEmail}.
      </p>

      <h2 className="text-xl font-bold mt-4 mb-2">10. Contact Information</h2>
      <p className="mb-4">
        For questions or comments regarding this Privacy Policy and our privacy
        practices, contact us at {contactEmail}.
      </p>
    </div>
  )
}

export default PrivacyNotice
