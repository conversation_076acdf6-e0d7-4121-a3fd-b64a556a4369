export const runtime = 'edge'

/**
 * Lightweight Upstash KV heartbeat endpoint.
 * - Does not involve any AI providers
 * - Uses REST (KV_REST_API_URL + KV_REST_API_TOKEN)
 * - INCR + EXPIRE on a date-scoped key to keep the database active
 */
export async function GET(): Promise<Response> {
  const KV_URL: string | undefined =
    process.env.KV_REST_API_URL || process.env.KV_URL
  const KV_TOKEN: string | undefined = process.env.KV_REST_API_TOKEN

  // Allow route to be invoked even when KV is not configured locally
  if (!KV_URL || !KV_TOKEN) {
    return new Response(
      JSON.stringify({
        ok: false,
        message: 'KV env vars not configured on this environment.',
      }),
      { status: 200, headers: { 'content-type': 'application/json' } }
    )
  }

  // Use a stable daily key and a modest TTL so keys expire naturally
  const dateStr: string = new Date().toISOString().slice(0, 10)
  const key: string = `kv:heartbeat:${dateStr}`

  try {
    const incrResp = await fetch(`${KV_URL}/incr/${encodeURIComponent(key)}`, {
      headers: { Authorization: `Bearer ${KV_TOKEN}` },
      cache: 'no-store',
    })
    const hitsText: string = await incrResp.text()
    const hits: number = Number(hitsText)

    // Set expiry to 30 days (in seconds)
    await fetch(`${KV_URL}/expire/${encodeURIComponent(key)}/2592000`, {
      headers: { Authorization: `Bearer ${KV_TOKEN}` },
      cache: 'no-store',
    })

    return new Response(JSON.stringify({ ok: true, key, hits }), {
      status: 200,
      headers: { 'content-type': 'application/json' },
    })
  } catch (error) {
    return new Response(
      JSON.stringify({ ok: false, error: 'KV request failed' }),
      { status: 500, headers: { 'content-type': 'application/json' } }
    )
  }
}
