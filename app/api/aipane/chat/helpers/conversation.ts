import prisma from '@/app/libs/prismadb'
import { isCanonicalSystemMessage } from '../prompt-utils'
import type { HistoryMessage } from './messages'

type EnsureConversationArgs = {
  conversationId: string
  userId: string
  contextIds: string[]
  dragTreeId?: string
}

export type ConversationRecord = {
  storedContextIds: string[]
}

export const ensureConversationRecord = async ({
  conversationId,
  userId,
  contextIds,
  dragTreeId,
}: EnsureConversationArgs): Promise<ConversationRecord> => {
  await prisma.aiConversation.upsert({
    where: { id: conversationId },
    create: {
      id: conversationId,
      userId,
      title: 'Untitled chat',
      contextEntityType: dragTreeId ? 'drag_tree' : undefined,
      contextEntityId: dragTreeId || undefined,
      metadata: { contextIds },
    },
    update: {},
  })

  const conversation = await prisma.aiConversation.findUnique({
    where: { id: conversationId },
    select: { metadata: true },
  })

  const storedContextIds = Array.isArray(
    (conversation?.metadata as any)?.contextIds
  )
    ? ((conversation?.metadata as any)?.contextIds as string[])
    : []

  return { storedContextIds }
}

export type ConversationState = {
  history: HistoryMessage[]
  canonicalSystemMessage?: { id: string; content: string }
  isFirstTurn: boolean
}

export const loadConversationState = async (
  conversationId: string
): Promise<ConversationState> => {
  const messages = await prisma.aiMessage.findMany({
    where: { conversationId },
    select: {
      id: true,
      role: true,
      content: true,
      createdAt: true,
      metadata: true,
    },
    orderBy: { createdAt: 'asc' },
  })

  const systemMessages = messages.filter(message => message.role === 'SYSTEM')
  const canonicalSystem = systemMessages.find(message =>
    isCanonicalSystemMessage({
      role: message.role,
      content: message.content,
      metadata:
        (message.metadata as Record<string, unknown> | null) || undefined,
    })
  )

  const history = messages
    .filter(message => message.role !== 'SYSTEM')
    .map(message => ({
      role: message.role,
      content: message.content,
    }))

  return {
    history,
    canonicalSystemMessage: canonicalSystem
      ? { id: canonicalSystem.id, content: canonicalSystem.content }
      : undefined,
    isFirstTurn: history.length === 0,
  }
}
