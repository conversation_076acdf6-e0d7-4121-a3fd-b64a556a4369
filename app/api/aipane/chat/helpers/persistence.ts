import prisma from '@/app/libs/prismadb'
import {
  finalizeConversationTurn,
  persistConversationTurn,
} from '@/app/server-actions/ai-chat'
import type {
  ExecutionStepData,
  MessageData,
} from '@/app/server-actions/ai-chat/types'
import {
  RETRY_CONFIG,
  computeBackoffDelay,
} from '@/app/api/aipane/chat/chat-config'
import { buildSystemUiMessage } from '../prompt-utils'
import { SYSTEM_MESSAGE_VERSION } from '../constants'
import { generateAiMessageId } from '@/lib/id'

const generateChatTitleFromText = (text: string): string => {
  const normalized = (text || '').trim().replace(/\s+/g, ' ')
  if (!normalized) return 'Untitled'
  const firstLine = normalized.split('\n')[0]
  const max = 50
  if (firstLine.length <= max) return firstLine
  const idx = firstLine.lastIndexOf(' ', max)
  return `${idx > 20 ? firstLine.slice(0, idx) : firstLine.slice(0, max)}...`
}

type PersistChatTurnArgs = {
  conversationId: string
  isFirstTurn: boolean
  systemPrompt: string
  contextText?: string
  effectiveContextIds: string[]
  userMessage: { text: string; uiMessage: MessageData['uiMessage'] }
  assistantMessage: {
    text: string
    uiMessage: MessageData['uiMessage']
  }
  steps: ExecutionStepData[]
  modelMetadata: Record<string, unknown>
  userId: string
}

export const persistChatTurn = async ({
  conversationId,
  isFirstTurn,
  systemPrompt,
  contextText,
  effectiveContextIds,
  userMessage,
  assistantMessage,
  steps,
  modelMetadata,
  userId,
}: PersistChatTurnArgs): Promise<void> => {
  const attemptPersistence = async () => {
    if (isFirstTurn) {
      const systemMessageId = generateAiMessageId()
      const systemUiMessage = buildSystemUiMessage(
        systemMessageId,
        systemPrompt,
        {
          metadata: { contextIds: effectiveContextIds },
        }
      )

      const result = await finalizeConversationTurn(
        conversationId,
        {
          role: 'USER',
          content: userMessage.text,
          uiMessage: userMessage.uiMessage,
        },
        {
          role: 'ASSISTANT',
          content: assistantMessage.text,
          steps,
          uiMessage: assistantMessage.uiMessage,
          metadata: modelMetadata,
        },
        {
          conversationMetadata: { contextIds: effectiveContextIds },
          systemMessage: {
            id: systemMessageId,
            role: 'SYSTEM',
            content: systemPrompt,
            metadata: {
              systemMessage: true,
              version: SYSTEM_MESSAGE_VERSION,
              contextIds: effectiveContextIds,
              ...(contextText ? { contextText } : {}),
            },
            uiMessage: systemUiMessage,
          },
        }
      )

      if (!result.success) {
        throw new Error(result.error || 'Failed to finalize conversation turn')
      }

      await maybeUpdateConversationTitle(
        conversationId,
        userMessage.text,
        userId,
        contextText
      )
    } else {
      const result = await persistConversationTurn({
        conversationId,
        userMessage: {
          role: 'USER',
          content: userMessage.text,
          uiMessage: userMessage.uiMessage,
        },
        assistantMessage: {
          role: 'ASSISTANT',
          content: assistantMessage.text,
          steps,
          uiMessage: assistantMessage.uiMessage,
          metadata: modelMetadata,
        },
      })

      if (!result.success) {
        throw new Error(result.error || 'Failed to persist conversation turn')
      }
    }
  }

  await runWithRetries(attemptPersistence)
}

const runWithRetries = async (fn: () => Promise<void>) => {
  let attempt = 0
  let lastError: unknown

  while (attempt < RETRY_CONFIG.MAX_RETRIES) {
    attempt += 1
    try {
      await fn()
      return
    } catch (error) {
      lastError = error
      const delay = computeBackoffDelay(
        attempt,
        RETRY_CONFIG.BACKOFF_MS,
        RETRY_CONFIG.STRATEGY
      )
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  throw lastError instanceof Error
    ? lastError
    : new Error('Unknown persistence error')
}

const maybeUpdateConversationTitle = async (
  conversationId: string,
  userMessage: string,
  userId: string,
  contextText?: string
) => {
  try {
    const { generateTitleWithWeakModel } = await import(
      '@/app/libs/title-generation'
    )

    const seed = [
      contextText ? `Context:\n${contextText}` : null,
      `Question: ${userMessage}`,
    ]
      .filter(Boolean)
      .join('\n\n')

    const titleFromModel = (
      await generateTitleWithWeakModel(seed, userId, conversationId)
    ).trim()
    const finalTitle = titleFromModel || generateChatTitleFromText(userMessage)

    await prisma.aiConversation.update({
      where: { id: conversationId },
      data: { title: finalTitle },
    })
  } catch (error) {
    const fallbackTitle = generateChatTitleFromText(userMessage)
    try {
      await prisma.aiConversation.update({
        where: { id: conversationId },
        data: { title: fallbackTitle },
      })
    } catch (updateError) {
      console.warn('Failed to set conversation title', updateError)
    }
  }
}
