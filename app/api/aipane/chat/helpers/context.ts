import prisma from '@/app/libs/prismadb'
import { buildSystemPrompt } from '../prompt-utils'
import {
  DEFAULT_CONTEXT_HEADER,
  MAX_CONTEXT_ITEMS,
  MAX_CONTEXT_SNIPPET_LENGTH,
} from '../constants'
import type { ConversationState } from './conversation'
import { uniqueStrings } from './common'

export type PromptResolution = {
  systemPrompt: string
  contextText?: string
  effectiveContextIds: string[]
  shouldPersistSystemMessage: boolean
}

type ResolvePromptArgs = {
  explicitContext?: string
  requestedContextIds: string[]
  storedContextIds: string[]
  state: ConversationState
}

export const resolveSystemPrompt = async ({
  explicitContext,
  requestedContextIds,
  storedContextIds,
  state,
}: ResolvePromptArgs): Promise<PromptResolution> => {
  const effectiveContextIds = uniqueStrings([
    ...requestedContextIds,
    ...storedContextIds,
  ])

  if (state.canonicalSystemMessage) {
    return {
      systemPrompt: state.canonicalSystemMessage.content,
      effectiveContextIds,
      shouldPersistSystemMessage: false,
    }
  }

  let contextText = explicitContext?.trim() || undefined

  if (!contextText && state.isFirstTurn && effectiveContextIds.length > 0) {
    contextText = await buildContextTextFromIds(effectiveContextIds)
  }

  const systemPrompt = buildSystemPrompt({
    contextText,
    contextHeader: DEFAULT_CONTEXT_HEADER,
  })

  return {
    systemPrompt,
    contextText,
    effectiveContextIds,
    shouldPersistSystemMessage: true,
  }
}

const buildContextTextFromIds = async (contextIds: string[]) => {
  const limitedIds = contextIds.slice(0, MAX_CONTEXT_ITEMS)

  const nodes = await prisma.dragTreeNode.findMany({
    where: { id: { in: limitedIds } },
    select: { id: true, label: true },
  })

  if (nodes.length === 0) {
    return undefined
  }

  const contentByNodeId: Record<string, string> = {}
  await Promise.all(
    nodes.map(async node => {
      try {
        const content = await prisma.dragTreeNodeContent.findFirst({
          where: {
            drag_tree_node_id: node.id,
            status: 'ACTIVE',
          },
          select: { content_text: true, updated_at: true },
          orderBy: { updated_at: 'desc' },
        })

        if (content?.content_text) {
          const snippet = content.content_text.trim()
          contentByNodeId[node.id] =
            snippet.length > MAX_CONTEXT_SNIPPET_LENGTH
              ? `${snippet.slice(0, MAX_CONTEXT_SNIPPET_LENGTH)}…`
              : snippet
        }
      } catch (error) {
        console.warn('Failed to load context content', { id: node.id, error })
      }
    })
  )

  const lines: string[] = [DEFAULT_CONTEXT_HEADER]
  nodes.forEach((node, index) => {
    const snippet = contentByNodeId[node.id]
    if (snippet) {
      lines.push(`${index + 1}. ${node.label}`)
      lines.push('')
      lines.push(snippet)
      lines.push('')
    } else {
      lines.push(`${index + 1}. ${node.label}`)
    }
  })

  return lines.join('\n')
}
