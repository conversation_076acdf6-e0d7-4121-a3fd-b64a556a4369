import type { UIMessage } from 'ai'

export const extractTextFromUIMessage = (
  message?: UIMessage | null
): string => {
  if (!message) return ''
  if (typeof (message as any).content === 'string') {
    return (message as any).content as string
  }
  if (Array.isArray(message?.parts)) {
    return (message.parts || [])
      .map(part => {
        if (!part) return ''
        if ((part as any).type === 'text') return (part as any).text || ''
        if ((part as any).type === 'text-delta') {
          return (part as any).textDelta || ''
        }
        return ''
      })
      .join('')
  }
  return ''
}

export const ensureUiMessage = (
  message: UIMessage | undefined,
  fallbackText: string,
  role: 'user' | 'assistant'
): UIMessage => {
  if (message) {
    const ensuredId = message.id || `${role}_${Date.now()}`
    return {
      ...message,
      id: ensuredId,
      parts:
        Array.isArray(message.parts) && message.parts.length > 0
          ? message.parts
          : [{ type: 'text', text: fallbackText }],
    }
  }

  return {
    id: `${role}_${Date.now()}`,
    role,
    parts: [{ type: 'text', text: fallbackText }],
  }
}

export const toModelRole = (role: string): 'user' | 'assistant' =>
  role.toLowerCase() === 'assistant' ? 'assistant' : 'user'

export type HistoryMessage = {
  role: string
  content: string
}

export const buildModelMessages = ({
  systemPrompt,
  history,
  userMessage,
}: {
  systemPrompt: string
  history: HistoryMessage[]
  userMessage: string
}): Array<{ role: 'system' | 'user' | 'assistant'; content: string }> => {
  const messages: Array<{
    role: 'system' | 'user' | 'assistant'
    content: string
  }> = [{ role: 'system', content: systemPrompt }]

  history.forEach(message => {
    messages.push({
      role: toModelRole(message.role),
      content: message.content,
    })
  })

  messages.push({ role: 'user', content: userMessage })
  return messages
}
