import type { UIMessage } from 'ai'

const BASE_PROMPT = `You are a helpful AI assistant with access to web search tools.
When using tools, always provide a comprehensive response that incorporates the tool results.
IMPORTANT: After using any tools, you MUST provide a final response that summarizes the results and answers the user's question.`

export type SystemPromptOptions = {
  contextText?: string
  contextHeader?: string
}

export const buildSystemPrompt = ({
  contextText,
  contextHeader = "Selected context items from user's workspace:",
}: SystemPromptOptions = {}): string => {
  if (!contextText || !contextText.trim()) {
    return BASE_PROMPT
  }

  return `${BASE_PROMPT}

${contextHeader}

${contextText.trim()}`
}

export const buildSystemUiMessage = (
  id: string,
  prompt: string,
  metadata: Record<string, unknown> = {}
): UIMessage => ({
  id,
  role: 'system',
  parts: [
    {
      type: 'text',
      text: prompt,
    },
  ],
  ...metadata,
})

export const isCanonicalSystemMessage = (message: {
  role: string
  content: string
  metadata?: Record<string, unknown> | null
}): boolean => {
  if ((message.metadata as any)?.systemMessage === true) {
    return true
  }

  if (typeof message.content !== 'string') {
    return false
  }

  const normalized = message.content.trim().toLowerCase()
  if (!normalized) return false

  return normalized.startsWith('you are a helpful ai assistant')
}
