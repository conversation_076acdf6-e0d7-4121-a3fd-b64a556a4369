import { NextRequest, NextResponse } from 'next/server'
import prisma from '@/app/libs/prismadb'
import { DynamicPricing } from '@/app/subscription/components/ModernPricingConfig'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

export async function GET(_req: NextRequest) {
  try {
    // Fetch active prices with product information
    const prices = await prisma.price.findMany({
      where: { active: true },
      select: {
        id: true,
        active: true,
        currency: true,
        unit_amount: true,
        interval: true,
        interval_count: true,
        product_id: true,
        product: {
          select: {
            id: true,
            name: true,
            active: true,
          },
        },
      },
      orderBy: [{ interval: 'asc' }, { unit_amount: 'asc' }],
    })

    // Structure pricing data for Pro plan (assuming single active product)
    const activeProduct = prices.find(p => p.product.active)?.product
    if (!activeProduct) {
      return NextResponse.json({
        error: 'No active product found',
        pricing: null,
      })
    }

    // Group prices by interval
    const monthlyPrice = prices.find(
      p =>
        p.product_id === activeProduct.id && p.interval === 'month' && p.active
    )

    const yearlyPrice = prices.find(
      p =>
        p.product_id === activeProduct.id && p.interval === 'year' && p.active
    )

    if (!monthlyPrice) {
      return NextResponse.json({
        error: 'No monthly price found for active product',
        pricing: null,
      })
    }

    // Create structured pricing data
    const dynamicPricing: DynamicPricing = {
      monthly: {
        amount: Number(monthlyPrice.unit_amount),
        priceId: monthlyPrice.id,
      },
      yearly: yearlyPrice
        ? {
            amount: Number(yearlyPrice.unit_amount),
            priceId: yearlyPrice.id,
          }
        : undefined,
      currency: monthlyPrice.currency,
    }

    // Serialize BigInt fields for JSON (e.g., unit_amount)
    const serializablePrices = prices.map(p => ({
      ...p,
      unit_amount: Number(p.unit_amount),
    }))

    // Return both legacy format and new structured format with light caching
    const res = NextResponse.json({
      prices: serializablePrices, // Legacy format for backward compatibility
      pricing: dynamicPricing, // New structured format
      product: activeProduct,
    })
    // Cache guidance:
    // - max-age=60: browsers can reuse for 60s
    // - s-maxage=300: CDN (Vercel) can cache for 5m
    // - stale-while-revalidate=86400: serve stale for a day while revalidating
    // Price changes propagate quickly via webhooks updating the DB; cache keeps load low
    res.headers.set(
      'Cache-Control',
      'public, max-age=60, s-maxage=300, stale-while-revalidate=86400'
    )
    return res
  } catch (error) {
    console.error('Failed to fetch prices:', error)
    return NextResponse.json(
      { error: 'Failed to load pricing' },
      { status: 500 }
    )
  }
}
