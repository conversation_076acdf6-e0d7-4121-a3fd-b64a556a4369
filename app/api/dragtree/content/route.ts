import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import prisma from '@/app/libs/prismadb'

export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const contentId = searchParams.get('contentId')
    const isPrime = searchParams.get('prime') === 'true'

    // Early exit: API warm-up hit. This avoids 400s in the logs while still
    // compiling / warming the serverless function. No DB queries or further
    // processing are necessary.
    if (isPrime) {
      console.log(
        `🔥 [Content Prime] Warm-up request received from user: ${session?.user?.id ?? 'guest'}`
      )
      return NextResponse.json({ success: true, prime: true })
    }

    if (!contentId) {
      return NextResponse.json(
        { error: 'Content ID is required' },
        { status: 400 }
      )
    }

    const content = await prisma.dragTreeNodeContent.findUnique({
      where: {
        id: contentId,
      },
      include: {
        drag_tree: {
          select: {
            id: true,
            user_id: true,
          },
        },
      },
    })

    if (!content) {
      console.log(
        `❌ [Content Load] Content not found: ${contentId} for user: ${session.user.id}`
      )
      return NextResponse.json({ error: 'Content not found' }, { status: 404 })
    }

    // Defensive: handle potential data integrity issues where the parent drag tree is missing
    if (!content.drag_tree) {
      console.warn(
        `⚠️ [Content Load] Missing parent drag tree for content: ${contentId}`
      )
      return NextResponse.json(
        { error: 'Parent drag tree not found' },
        { status: 404 }
      )
    }

    // Add ownership validation for security
    if (content.drag_tree.user_id !== session.user.id) {
      console.log(
        `🔒 [Content Load] Access denied: ${contentId} - user ${session.user.id} tried to access content owned by ${content.drag_tree.user_id}`
      )
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    console.log(
      `✅ [Content Load] Successfully retrieved content: ${contentId} for user: ${session.user.id}`
    )

    return NextResponse.json({
      success: true,
      data: content,
    })
  } catch (error) {
    console.error('💥 [Content Load] Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
