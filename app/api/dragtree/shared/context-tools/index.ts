import { DragTreeNodeContentStatus, DragTreeNodeType } from '@prisma/client'
import type { J<PERSON><PERSON>ontent } from '@tiptap/core'
import { encode } from 'gpt-tokenizer'
import { extractNodeContentText } from '@/app/utils/nodeContent'
import { getResearchPreviewData } from '@/app/server-actions/drag-tree/get-preview-snapshot'
import type {
  CategoryContextItem,
  CategoryContextResult,
  DragTreeNodeContentSnapshot,
  DragTreeNodeSnapshot,
  DragTreeResearchSnapshot,
  QuestionContextResult,
} from './types'

// Priority map ensures ACTIVE research wins over drafts or archived items
const CONTENT_STATUS_PRIORITY: Record<DragTreeNodeContentStatus, number> = {
  [DragTreeNodeContentStatus.ACTIVE]: 3,
  [DragTreeNodeContentStatus.PROCESSING]: 2,
  [DragTreeNodeContentStatus.INITIALIZED]: 1,
  [DragTreeNodeContentStatus.INACTIVE]: 0,
}

const ALLOWED_PREVIEW_STATUSES: Set<DragTreeNodeContentStatus> = new Set([
  DragTreeNodeContentStatus.ACTIVE,
])

type NormalizedSnapshot = {
  dragTreeId: string
  treeTitle?: string | null
  rootId?: string
  nodesById: Record<string, DragTreeNodeSnapshot>
  childrenById: Record<string, string[]>
  contentsByNodeId: Record<string, DragTreeNodeContentSnapshot[]>
  contentById: Record<string, DragTreeNodeContentSnapshot>
  primaryContentByNodeId: Record<
    string,
    DragTreeNodeContentSnapshot | undefined
  >
}

type TokenTruncationResult = {
  text: string
  tokensUsed: number
  truncated: boolean
}

/**
 * Parse ISO strings safely into numeric timestamps. Undefined values are treated as 0.
 */
const toTimestamp = (value?: string | Date | null): number => {
  if (!value) return 0
  const date = value instanceof Date ? value : new Date(value)
  const ts = date.getTime()
  return Number.isNaN(ts) ? 0 : ts
}

const normalizeSnapshot = (
  snapshot: DragTreeResearchSnapshot
): NormalizedSnapshot => {
  const nodesById: Record<string, DragTreeNodeSnapshot> = {}
  snapshot.nodes.forEach(node => {
    nodesById[node.id] = node
  })

  const childrenById: Record<string, string[]> = {}
  Object.entries(snapshot.hierarchy.children).forEach(([id, children]) => {
    childrenById[id] = Array.isArray(children) ? [...children] : []
  })

  // Ensure every node has a children array to avoid undefined checks later
  Object.keys(nodesById).forEach(nodeId => {
    if (!childrenById[nodeId]) {
      childrenById[nodeId] = []
    }
  })

  const contentsByNodeId: Record<string, DragTreeNodeContentSnapshot[]> = {}
  const contentById: Record<string, DragTreeNodeContentSnapshot> = {}

  snapshot.contents.forEach(content => {
    contentById[content.id] = content
    if (!contentsByNodeId[content.nodeId]) {
      contentsByNodeId[content.nodeId] = []
    }
    contentsByNodeId[content.nodeId].push(content)
  })

  const primaryContentByNodeId: Record<
    string,
    DragTreeNodeContentSnapshot | undefined
  > = {}

  Object.entries(contentsByNodeId).forEach(([nodeId, contentItems]) => {
    const sorted = [...contentItems].sort((a, b) => {
      const statusDelta =
        CONTENT_STATUS_PRIORITY[b.status] - CONTENT_STATUS_PRIORITY[a.status]
      if (statusDelta !== 0) return statusDelta
      const updatedDelta = toTimestamp(b.updatedAt) - toTimestamp(a.updatedAt)
      if (updatedDelta !== 0) return updatedDelta
      return toTimestamp(b.createdAt) - toTimestamp(a.createdAt)
    })
    primaryContentByNodeId[nodeId] = sorted[0]
  })

  return {
    dragTreeId: snapshot.dragTreeId,
    treeTitle: snapshot.title,
    rootId: snapshot.hierarchy.rootId,
    nodesById,
    childrenById,
    contentsByNodeId,
    contentById,
    primaryContentByNodeId,
  }
}

/**
 * Lightweight markdown → plain text converter that preserves basic structure while
 * stripping formatting tokens. This avoids pulling heavy markdown parsers into the bundle.
 */
export const markdownToPlainText = (markdown: string): string => {
  if (!markdown) return ''

  let text = markdown

  // Remove fenced code blocks but keep their inner content
  text = text.replace(/```[\s\S]*?```/g, block =>
    block.replace(/```[a-zA-Z0-9_-]*\n?/, '').replace(/```$/, '')
  )

  // Inline code → plain text
  text = text.replace(/`([^`]+)`/g, '$1')

  // Images are removed entirely
  text = text.replace(/!\[[^\]]*\]\([^)]*\)/g, '')

  // Links keep the readable label, fall back to url if label missing
  text = text.replace(/\[([^\]]*)\]\(([^)]+)\)/g, (_match, label, url) =>
    label ? label : url
  )

  // Headings, blockquotes, and bullet markers
  text = text.replace(/^#{1,6}\s*/gm, '')
  text = text.replace(/^>+\s*/gm, '')
  text = text.replace(/^\s*[-*+]\s+/gm, '- ')
  text = text.replace(/\*\*(.*?)\*\*/g, '$1')
  text = text.replace(/__(.*?)__/g, '$1')
  text = text.replace(/\*(.*?)\*/g, '$1')
  text = text.replace(/_(.*?)_/g, '$1')

  // Collapse excessive whitespace while keeping intentional new lines
  text = text.replace(/\r/g, '')
  text = text.replace(/\n{3,}/g, '\n\n')
  text = text.replace(/[\t ]+/g, ' ')

  return text
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
    .join('\n')
}

/**
 * Convert TipTap JSON (or stringified JSON) into plain text to minimise token usage.
 */
export const convertTipTapJsonToPlainText = (value: unknown): string => {
  if (!value) return ''

  const resolveJson = (): JSONContent | null => {
    if (typeof value === 'string') {
      const trimmed = value.trim()
      if (trimmed.startsWith('{') && trimmed.endsWith('}')) {
        try {
          const parsed = JSON.parse(trimmed)
          if (parsed && parsed.type === 'doc') {
            return parsed as JSONContent
          }
        } catch {
          return null
        }
      }
      return null
    }

    if (typeof value === 'object' && (value as JSONContent)?.type === 'doc') {
      return value as JSONContent
    }

    return null
  }

  const json = resolveJson()
  if (!json) {
    return typeof value === 'string' ? markdownToPlainText(value) : ''
  }

  const chunks: string[] = []

  const walk = (node?: JSONContent | null): void => {
    if (!node) return

    if (node.type === 'text' && typeof node.text === 'string') {
      chunks.push(node.text)
      return
    }

    if (Array.isArray(node.content)) {
      node.content.forEach(child => walk(child))

      if (
        node.type === 'paragraph' ||
        node.type === 'heading' ||
        node.type === 'listItem'
      ) {
        chunks.push('\n')
      }
    }
  }

  walk(json)

  const raw = chunks
    .join('')
    .replace(/\n{3,}/g, '\n\n')
    .trim()

  if (!raw) return ''

  return raw
    .split('\n')
    .map(line => line.trim())
    .filter(Boolean)
    .join('\n')
}

const normalizeContentValue = (value: unknown): string => {
  if (!value) return ''

  if (typeof value === 'string') {
    const trimmed = value.trim()
    if (trimmed.startsWith('{') && trimmed.endsWith('}')) {
      const converted = convertTipTapJsonToPlainText(trimmed)
      if (converted) return converted
    }
    return trimmed
  }

  if (typeof value === 'object') {
    const converted = convertTipTapJsonToPlainText(value)
    if (converted) return converted
    try {
      return JSON.stringify(value)
    } catch {
      return ''
    }
  }

  return ''
}

const getRenderableTextFromContent = (
  content: DragTreeNodeContentSnapshot
): string => {
  const extracted = extractNodeContentText({
    contentText: content.contentText ?? '',
    metadata: content.contentMetadata ?? {},
    messages: content.messages ?? [],
  })

  const normalizedExtracted = normalizeContentValue(extracted)
  if (normalizedExtracted) return normalizedExtracted

  const normalizedContentText = normalizeContentValue(content.contentText)
  if (normalizedContentText) return normalizedContentText

  if (Array.isArray(content.messages)) {
    const assistant = content.messages.find(
      (message: any) =>
        message?.role === 'assistant' && typeof message?.content === 'string'
    )
    const normalizedMessage = normalizeContentValue(assistant?.content)
    if (normalizedMessage) return normalizedMessage
  }

  return ''
}

const truncateToTokenLimit = (
  text: string,
  tokenLimit?: number
): TokenTruncationResult => {
  if (!text) {
    return { text: '', tokensUsed: 0, truncated: false }
  }

  const tokens = encode(text)
  if (!tokenLimit || tokenLimit <= 0 || tokens.length <= tokenLimit) {
    return { text, tokensUsed: tokens.length, truncated: false }
  }

  const ellipsis = '…'
  const ellipsisTokens = encode(ellipsis).length || 1
  if (tokenLimit <= ellipsisTokens) {
    return { text: ellipsis, tokensUsed: ellipsisTokens, truncated: true }
  }

  const effectiveLimit = Math.max(1, tokenLimit - ellipsisTokens)
  let low = 0
  let high = text.length
  let bestIndex = 0

  while (low <= high) {
    const mid = Math.floor((low + high) / 2)
    const slice = text.slice(0, mid)
    const sliceTokens = encode(slice)
    if (sliceTokens.length <= effectiveLimit) {
      bestIndex = mid
      low = mid + 1
    } else {
      high = mid - 1
    }
  }

  let truncatedText = text.slice(0, bestIndex)
  if (bestIndex < text.length) {
    const lastWhitespace = truncatedText.lastIndexOf(' ')
    if (lastWhitespace > 40) {
      truncatedText = truncatedText.slice(0, lastWhitespace)
    }
    truncatedText = `${truncatedText.trimEnd()}${ellipsis}`
  }

  const tokensUsed = encode(truncatedText).length
  if (tokenLimit && tokensUsed > tokenLimit) {
    let adjusted = truncatedText
    let adjustedTokens = tokensUsed
    while (adjustedTokens > tokenLimit && adjusted.length > 1) {
      adjusted = adjusted
        .slice(0, Math.max(0, adjusted.length - ellipsis.length - 1))
        .trimEnd()
      adjusted = `${adjusted}${ellipsis}`
      adjustedTokens = encode(adjusted).length
    }
    return {
      text: adjusted,
      tokensUsed: Math.min(adjustedTokens, tokenLimit),
      truncated: true,
    }
  }

  return { text: truncatedText, tokensUsed, truncated: true }
}

/**
 * Generate a markdown outline limited to questions with ACTIVE research content.
 */
export const generateResearchPreviewMarkdownFromSnapshot = (
  snapshot: DragTreeResearchSnapshot
): string => {
  const normalized = normalizeSnapshot(snapshot)
  const rootNode = normalized.rootId
    ? normalized.nodesById[normalized.rootId]
    : undefined

  const title =
    snapshot.title?.trim() ||
    rootNode?.label?.trim() ||
    'Research Context Preview'
  const topIdentifier = rootNode ? ` [${rootNode.id}]` : ''
  const lines: string[] = [`# ${title}${topIdentifier}`]

  if (!rootNode) {
    lines.push('', '_No researched questions available yet._')
    return lines.join('\n').trim()
  }

  const renderNode = (nodeId: string, depth: number): string[] => {
    const node = normalized.nodesById[nodeId]
    if (!node) return []

    if (node.nodeType === DragTreeNodeType.QUESTION) {
      const content = normalized.primaryContentByNodeId[node.id]
      if (!content || !ALLOWED_PREVIEW_STATUSES.has(content.status)) {
        return []
      }
      const indent = depth > 1 ? '  '.repeat(depth - 1) : ''
      const label = node.label?.trim() || 'Untitled question'
      return [`${indent}- ${label} [${content.id}]`]
    }

    const childIds = normalized.childrenById[node.id] || []
    const childSections = childIds.flatMap(childId =>
      renderNode(childId, depth + 1)
    )
    if (childSections.length === 0) {
      return []
    }

    const headingLevel = Math.min(depth + 1, 6)
    const headingPrefix = '#'.repeat(headingLevel)
    const label = node.label?.trim() || 'Untitled category'

    return ['', `${headingPrefix} ${label} [${node.id}]`, '', ...childSections]
  }

  const rootChildren = normalized.childrenById[rootNode.id] || []
  const sections = rootChildren.flatMap(childId => renderNode(childId, 1))

  if (sections.length === 0) {
    lines.push('', '_No researched questions available yet._')
    return lines.join('\n').trim()
  }

  lines.push(...sections)
  return lines
    .join('\n')
    .replace(/\n{3,}/g, '\n\n')
    .trim()
}

/**
 * Retrieve a single research response by content id, optionally enforcing a token budget.
 */
export const getQuestionContextByContentId = (
  snapshot: DragTreeResearchSnapshot,
  contentId: string,
  options?: { tokenLimit?: number }
): QuestionContextResult => {
  const normalized = normalizeSnapshot(snapshot)
  const content = normalized.contentById[contentId]

  if (!content) {
    throw new Error(`No research content found for id ${contentId}`)
  }

  const node = normalized.nodesById[content.nodeId]
  if (!node) {
    throw new Error(
      `Content ${contentId} references missing node ${content.nodeId}`
    )
  }

  const responseText = getRenderableTextFromContent(content)
  const { text, tokensUsed, truncated } = truncateToTokenLimit(
    responseText,
    options?.tokenLimit
  )

  return {
    dragTreeId: normalized.dragTreeId,
    nodeId: node.id,
    contentId: content.id,
    questionText: node.label?.trim() || 'Untitled question',
    responseText: text,
    status: content.status,
    tokenCount: tokensUsed,
    truncated,
  }
}

/**
 * Collect all leaf questions under a category and respect a shared token budget.
 */
export const getCategoryContextByCategoryId = (
  snapshot: DragTreeResearchSnapshot,
  categoryId: string,
  options?: { totalTokens?: number }
): CategoryContextResult => {
  const normalized = normalizeSnapshot(snapshot)
  const category = normalized.nodesById[categoryId]

  if (!category) {
    throw new Error(`Category ${categoryId} was not found in drag tree`)
  }

  if (category.nodeType !== DragTreeNodeType.CATEGORY) {
    throw new Error(`Node ${categoryId} is not a category`)
  }

  const collectQuestions = (
    nodeId: string
  ): Array<{
    node: DragTreeNodeSnapshot
    content: DragTreeNodeContentSnapshot
  }> => {
    const node = normalized.nodesById[nodeId]
    if (!node) return []

    if (node.nodeType === DragTreeNodeType.QUESTION) {
      const primary = normalized.primaryContentByNodeId[nodeId]
      if (primary && ALLOWED_PREVIEW_STATUSES.has(primary.status)) {
        return [{ node, content: primary }]
      }
      return []
    }

    return (normalized.childrenById[nodeId] || []).flatMap(childId =>
      collectQuestions(childId)
    )
  }

  const questionEntries = (normalized.childrenById[categoryId] || []).flatMap(
    childId => collectQuestions(childId)
  )

  if (questionEntries.length === 0) {
    return {
      dragTreeId: normalized.dragTreeId,
      categoryId,
      categoryLabel: category.label?.trim() || 'Untitled category',
      totalTokensAllocated: options?.totalTokens,
      totalTokensUsed: 0,
      items: [],
    }
  }

  const totalTokens = options?.totalTokens
  const budgets: number[] = []

  if (totalTokens && totalTokens > 0) {
    const base = Math.floor(totalTokens / questionEntries.length)
    const remainder = totalTokens % questionEntries.length

    questionEntries.forEach((_, index) => {
      const budget = base + (index < remainder ? 1 : 0)
      budgets.push(budget)
    })
  } else {
    questionEntries.forEach(() => budgets.push(0))
  }

  const items: CategoryContextItem[] = []
  let totalTokensUsed = 0

  questionEntries.forEach(({ node, content }, index) => {
    const plainText = getRenderableTextFromContent(content)
    const { text, tokensUsed, truncated } = truncateToTokenLimit(
      plainText,
      budgets[index]
    )

    totalTokensUsed += tokensUsed
    items.push({
      nodeId: node.id,
      contentId: content.id,
      questionText: node.label?.trim() || 'Untitled question',
      responseText: text,
      tokensUsed,
      truncated,
    })
  })

  return {
    dragTreeId: normalized.dragTreeId,
    categoryId,
    categoryLabel: category.label?.trim() || 'Untitled category',
    totalTokensAllocated: options?.totalTokens,
    totalTokensUsed,
    items,
  }
}

export { truncateToTokenLimit }

type TreeStructureJson = {
  root_id?: string
  hierarchy?: Record<string, unknown>
}

const buildHierarchyFromTreeStructure = (
  treeStructure: TreeStructureJson | null | undefined,
  dragTreeId: string
): DragTreeResearchSnapshot['hierarchy'] => {
  if (!treeStructure || typeof treeStructure !== 'object') {
    throw new Error(
      `Drag tree ${dragTreeId} is missing tree_structure metadata`
    )
  }

  const rootId = treeStructure.root_id
  if (!rootId || typeof rootId !== 'string') {
    throw new Error(`Drag tree ${dragTreeId} has no root_id in tree_structure`)
  }

  const childrenRaw = treeStructure.hierarchy || {}
  const children: Record<string, string[]> = {}

  Object.entries(childrenRaw).forEach(([key, value]) => {
    if (typeof key !== 'string') return
    if (Array.isArray(value)) {
      children[key] = value.filter(
        entry => typeof entry === 'string'
      ) as string[]
    } else {
      children[key] = []
    }
  })

  return { rootId, children }
}

export const buildResearchPreviewSnapshot = async (
  dragTreeId: string
): Promise<DragTreeResearchSnapshot> => {
  const preview = await getResearchPreviewData(dragTreeId)

  const hierarchy = buildHierarchyFromTreeStructure(
    (preview.treeStructure as TreeStructureJson | null | undefined) ?? null,
    dragTreeId
  )

  const nodes: DragTreeNodeSnapshot[] = preview.nodes.map(node => ({
    id: node.id,
    label: node.label,
    nodeType: node.node_type,
  }))

  const contents: DragTreeNodeContentSnapshot[] = preview.contents.map(
    content => ({
      id: content.id,
      nodeId: content.drag_tree_node_id,
      status: content.status,
      contentText: content.content_text ?? undefined,
      contentType: undefined,
      contentMetadata: undefined,
      createdAt: undefined,
      updatedAt: undefined,
    })
  )

  return {
    dragTreeId,
    title: preview.title,
    hierarchy,
    nodes,
    contents,
  }
}

export const generateResearchPreviewMarkdown = async (
  dragTreeId: string
): Promise<string> => {
  const snapshot = await buildResearchPreviewSnapshot(dragTreeId)
  return generateResearchPreviewMarkdownFromSnapshot(snapshot)
}

export const generateResearchPreviewMarkdownSnapshot =
  generateResearchPreviewMarkdownFromSnapshot
