export type OpenAIFunctionTool = {
  type: 'function'
  function: {
    name: string
    description: string
    parameters: {
      type: 'object'
      properties: Record<string, unknown>
      required?: string[]
    }
  }
}

export const previewResearchTreeTool: OpenAIFunctionTool = {
  type: 'function',
  function: {
    name: 'preview_drag_tree_research',
    description:
      'Return a markdown outline of researched drag tree questions with content ids for later retrieval.',
    parameters: {
      type: 'object',
      properties: {
        dragTreeId: {
          type: 'string',
          description:
            'Unique drag tree id. Use this to scope subsequent question/category lookups.',
        },
      },
      required: ['dragTreeId'],
    },
  },
}

export const fetchQuestionContextTool: OpenAIFunctionTool = {
  type: 'function',
  function: {
    name: 'get_drag_tree_question_context',
    description:
      'Fetch a researched question by content id and return the plain text response ready for LLM consumption.',
    parameters: {
      type: 'object',
      properties: {
        dragTreeId: {
          type: 'string',
          description: 'Drag tree id where the research lives.',
        },
        contentId: {
          type: 'string',
          description:
            'DragTreeNodeContent id (e.g. que_xxx) obtained from the preview markdown.',
        },
        tokenLimit: {
          type: 'integer',
          description:
            'Optional safety budget. When provided the response is truncated to this many GPT tokens.',
          minimum: 1,
        },
      },
      required: ['dragTreeId', 'contentId'],
    },
  },
}

export const fetchCategoryContextTool: OpenAIFunctionTool = {
  type: 'function',
  function: {
    name: 'get_drag_tree_category_context',
    description:
      'Collect all researched leaf questions under a category id and enforce a shared token budget.',
    parameters: {
      type: 'object',
      properties: {
        dragTreeId: {
          type: 'string',
          description: 'Drag tree id where the category belongs.',
        },
        categoryId: {
          type: 'string',
          description:
            'Category node id (e.g. cat_xxx) as referenced in the preview outline.',
        },
        totalTokens: {
          type: 'integer',
          description:
            'Total tokens the model wants to spend across all returned question snippets.',
          minimum: 1,
        },
      },
      required: ['dragTreeId', 'categoryId'],
    },
  },
}

export const getContextToolDefinitions = (): OpenAIFunctionTool[] => [
  previewResearchTreeTool,
  fetchQuestionContextTool,
  fetchCategoryContextTool,
]
