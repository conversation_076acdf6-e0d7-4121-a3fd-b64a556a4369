import type {
  DragTreeN<PERSON><PERSON><PERSON><PERSON><PERSON>tatus,
  DragTreeNodeContentType,
  DragTreeNodeStatus,
  DragTreeNodeType,
} from '@prisma/client'

export type DragTreeHierarchySnapshot = {
  /** Root node id for the stored hierarchy */
  rootId: string
  /** Map of node id -> child node ids (categories and questions) */
  children: Record<string, string[]>
}

export type DragTreeNodeSnapshot = {
  id: string
  label: string
  nodeType: DragTreeNodeType
  status?: DragTreeNodeStatus
  metadata?: Record<string, unknown> | null
}

export type DragTreeNodeContentSnapshot = {
  id: string
  nodeId: string
  status: DragTreeNodeContentStatus
  contentType?: DragTreeNodeContentType
  contentText?: string | null
  contentMetadata?: unknown
  messages?: unknown
  createdAt?: string | Date | null
  updatedAt?: string | Date | null
}

export type DragTreeResearchSnapshot = {
  dragTreeId: string
  title?: string | null
  hierarchy: DragTreeHierarchySnapshot
  nodes: DragTreeNodeSnapshot[]
  contents: Drag<PERSON>reeNodeContentSnapshot[]
}

export type QuestionContextResult = {
  dragTreeId: string
  nodeId: string
  contentId: string
  questionText: string
  responseText: string
  status: DragTreeNodeContentStatus
  tokenCount: number
  truncated: boolean
}

export type CategoryContextItem = {
  nodeId: string
  contentId: string
  questionText: string
  responseText: string
  tokensUsed: number
  truncated: boolean
}

export type CategoryContextResult = {
  dragTreeId: string
  categoryId: string
  categoryLabel: string
  totalTokensAllocated?: number
  totalTokensUsed: number
  items: CategoryContextItem[]
}
