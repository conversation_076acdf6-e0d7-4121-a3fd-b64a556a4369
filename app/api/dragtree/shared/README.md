# DragTree Research API

This directory contains shared utilities and configurations for the DragTree research functionality.

## Research Context Toolkit

Location: `app/api/dragtree/shared/context-tools`

- **Preview generation**: `generateResearchPreviewMarkdown(dragTreeId)` calls `getResearchPreviewData` (server action) which fetches the tree structure plus minimal node/content metadata (latest active quick research id) in one query. For unit composition, `generateResearchPreviewMarkdownFromSnapshot(snapshot)` remains available.
- **Single-question retrieval**: `getQuestionContextByContentId(snapshot, contentId, { tokenLimit })` returns clean plain-text research content, automatically converting stored TipTap JSON and respecting optional token budgets.
- **Category retrieval**: `getCategoryContextByCategoryId(snapshot, categoryId, { totalTokens })` walks the hierarchy, collects every researched leaf question, and divides a shared token budget evenly (with remainder tokens distributed to the earliest items).
- **Content helpers**: `convertTipTapJsonToPlainText` + `markdownToPlainText` collapse rich TipTap payloads into compact text; `truncateToTokenLimit` uses `gpt-tokenizer` to preserve readability while staying within GPT token limits.
- **OpenAI tools**: `tool-definitions.ts` exports three function-calling definitions (`preview_drag_tree_research`, `get_drag_tree_question_context`, `get_drag_tree_category_context`) ready to embed in agent toolkits.
- **Tests**: `__tests__/api/dragtree/context-tools.test.ts` covers preview formatting, database-backed preview generation, TipTap handling, token budget enforcement, error paths, and schema sanity checks for the tool definitions.

**Data flow overview**

1. Upstream Prisma fetch (future integration) populates a lightweight `DragTreeResearchSnapshot` shape (`hierarchy`, `nodes`, `contents`).
2. Toolkit helpers normalise the snapshot, pick the most relevant `ACTIVE` content per node, and emit either markdown previews or plain-text responses.
3. Tool definitions ensure an LLM agent can browse (`preview`), focus (`question`), and batch-select (`category`) research without pulling the entire drag tree into context.

Status guards rely on `DragTreeNodeContentStatus.ACTIVE` to prevent partially generated research from leaking into prompts. Token trimming uses the same tokenizer as the AI Pane to keep budgeting consistent across frontend and backend flows.

## Quick Research API Endpoint

### Overview

The research API provides intelligent research generation using GPT-4o-mini, following the same patterns as the screening API for consistency.

### API Endpoints

#### `POST /api/dragtree/research_generate_v2`

Real LLM API endpoint using OpenAI Responses (`gpt-5-mini`) with native web search. The original Brave-based endpoint (`/api/dragtree/research_generate`) was retired in June 2024 and now returns `410 Gone`.

**Request Type**: `ResearchGenerateRequestType`

```typescript
{
  contentId: string
  questionText: string
  researchType?: string
}
```

**Features**:

- Uses OpenAI Responses with GPT-5-mini model
- **Language Support**: Automatically detects preferred language from drag tree settings
- **Context Awareness**: Includes original screening question for better research focus
- Unified prompt system for consistency across all research methods
- Structured research output with markdown formatting
- Database logging with OpenAI usage tracking
- Status updates (INITIALIZED → PROCESSING → COMPLETED)

#### `POST /api/dragtree/research_generate-simulator`

Simulator endpoint for development and testing.

### Centralized Prompts (`research-prompts.ts`)

All research prompts are centralized in one location for consistency with a **unified approach** for both internal and external use:

#### `createResearchPrompt(questionText: string, screeningQuestion?: string, language: string = 'English')`

**Unified prompt** for both API and external tools with comprehensive guidelines:

- **Language Support**: Automatically includes `Please generate the language in ${language}` following the same pattern as question generation
- **Context Awareness**: Includes original screening question when available
- **Structured Format**: Overview, Key Findings, Analysis, Recommendations, Risk Assessment, Next Steps
- **Quality Standards**: Actionable insights with practical implementation focus
- **Markdown Output**: Clean formatting for both API responses and external tools

#### Legacy Compatibility Functions

These functions now redirect to the unified prompt for backward compatibility:

- `createExternalToolPrompt()` - Generates a **simplified** prompt for external services:
  - If an original user ask is provided → `This is the original user ask: "<original ask>". Now we want you to research: "<research question>".`
  - Otherwise → `Now we want you to research: "<research question>".`
- `createCopyPastePrompt()` - Alias for unified prompt

**Benefits of Unified Approach**:

- Single prompt for all use cases (internal API + external tools)
- Consistent quality across different research methods
- Simplified maintenance and updates
- Language support for all research types

### UI Toggle Integration

#### Header Toggle

- **Location**: DragTree header component
- **Icon**: FiCpu (CPU icon)
- **States**:
  - "Real LLM" (green) - Uses GPT-4o-mini API
  - "Simulator" (gray) - Uses mock data

#### Global State Management

- **Store**: `useUIStore` in `app/stores/ui_store.ts`
- **Key**: `useRealLLMAPI: boolean`
- **Setter**: `setUseRealLLMAPI(useReal: boolean)`

### Research Lifecycle Integration

The `useResearchLifecycle` hook automatically detects the UI toggle state:

- **Real API Mode**: Calls `/api/dragtree/research_generate_v2` (Brave route retired June 2024)
- **Simulator Mode**: Calls `/api/dragtree/research_generate-simulator`

### Type Safety

All request/response types are defined in `app/types/api.ts`:

- `ResearchGenerateRequestType` - API request structure
- Input validation enforced at API level
- TypeScript types ensure consistency across components

### Usage Examples

#### Component Usage

```typescript
// Research button - automatically uses UI toggle
const { startResearch } = useResearchLifecycle({
  nodeId: 'node-id',
  questionText: 'Research question',
})

// Access toggle state
const { useRealLLMAPI, setUseRealLLMAPI } = useUIStore()
```

#### API Call

```typescript
const response = await fetch('/api/dragtree/research_generate_v2', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    contentId: 'content-id',
    questionText: 'What are the market opportunities?',
    researchType: 'RESEARCH',
  }),
})
```

### Benefits

1. **Consistency**: Same patterns as screening API
2. **Centralized Prompts**: Single source of truth for all research prompts
3. **Type Safety**: Full TypeScript support with proper types
4. **Flexible Usage**: Toggle between real API and simulator
5. **Quality Research**: Structured prompts ensure comprehensive analysis
6. **External Tool Support**: Optimized prompts for ChatGPT, Claude, etc.

### Future Enhancements

- Additional research types (competitive analysis, market research, etc.)
- Prompt versioning and A/B testing
- Research template customization
- Integration with external research APIs
