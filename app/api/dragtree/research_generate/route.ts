import { NextResponse } from 'next/server'

/**
 * Legacy DragTree research endpoint (Brave search path) retired in favor of OpenAI native search.
 *
 * We keep this stub so older clients receive a clear signal and to document why the Brave codebase
 * disappeared. New callers must use `/api/dragtree/research_generate_v2`, which handles both single
 * stage and two-stage research depending on the `ENABLE_TWO_STAGE_RESEARCH` flag.
 */
export const maxDuration = 300

export async function POST() {
  return NextResponse.json(
    {
      error:
        'The Brave-based DragTree research endpoint has been retired. Use /api/dragtree/research_generate_v2 instead.',
    },
    { status: 410 }
  )
}

/**
 * -----------------------------------------------------------------------------
 * Legacy Brave search implementation (retired)
 * -----------------------------------------------------------------------------
 * The full implementation is preserved below for audit/reference only. Each line
 * is prefixed with `//` to keep the TypeScript compiler from parsing it while
 * still giving future maintainers easy access to the original flow.
 */
// import { streamText, stepCountIs } from 'ai'
// import { NextRequest, NextResponse } from 'next/server'
// import { auth } from '@/auth'
// import {
//   DragTreeNodeContentStatus,
//   Prisma,
//   AIUsageType,
//   DragTreeNodeContentType,
// } from '@prisma/client'
// import {
//   createResearchPrompt,
//   createCustomTemplatePrompt,
// } from '@/app/api/dragtree/shared/research-prompts'
// import { createAIUsage } from '@/app/server-actions/log_ai_usage'
// import {
//   getLanguageName,
//   type SupportedLanguageCode,
// } from '@/app/constants/languages'
// import { SearchMetadata } from '@/app/api/dragtree/shared/brave-search'
// import prisma from '@/app/libs/prismadb'
// import { buildSearchTools } from '@/app/api/dragtree/shared/search-tools'
// import {
//   getModelConfigFromSession,
//   getModelFromConfig,
//   getProviderNameForUsage,
// } from '@/app/libs/model-config'
//
// import { enforceRateLimit } from '@/app/libs/llmRateLimit'
// import { ENABLE_TWO_STAGE_RESEARCH } from '@/app/configs/feature-flags'
// import {
//   assessTemplate,
//   DECISION_ANALYST_TEMPLATE,
// } from '@/app/api/dragtree/shared/assess-template'
// import { researchGenerateSchema } from '@/app/libs/api-schemas'
// import { validateRequestBody } from '@/app/libs/validation-utils'
//
// export const maxDuration = 300
//
// // Timeout for stuck PROCESSING status (5 minutes)
// const PROCESSING_TIMEOUT_MS = 5 * 60 * 1000
//
// export type ResearchGenerateRequestType = {
//   contentId: string
//   // questionText and researchType are extracted from database
// }
//
// export async function POST(req: NextRequest) {
//   // Declare contentId at function level for error handling
//   let contentId: string | undefined
//   let timeoutId: NodeJS.Timeout | undefined
//
//   // Helper function to clear timeout safely
//   const clearTimeoutSafely = () => {
//     if (timeoutId) {
//       try {
//         clearTimeout(timeoutId)
//         timeoutId = undefined
//       } catch (error) {
//         console.error('Failed to clear timeout:', error)
//       }
//     }
//   }
//
//   try {
//     // Get session for authentication
//     const session = await auth()
//     const userId = session?.user?.id
//
//     if (!userId) {
//       return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
//     }
//
//     // Validate request body with Zod schema
//     const validationResult = await validateRequestBody(
//       req,
//       researchGenerateSchema
//     )
//     if (!validationResult.success) {
//       return validationResult.error
//     }
//
//     // Distributed rate-limit per user & route (KV-backed if configured)
//     const rl = await enforceRateLimit(session, 'dragtree_research_generate')
//     if (rl) return rl
//
//     // Extract contentId from validated request
//     const requestData = validationResult.data
//     contentId = requestData.contentId
//
//     if (!contentId) {
//       return NextResponse.json(
//         { error: 'Missing required field: contentId' },
//         { status: 400 }
//       )
//     }
//
//     console.log(
//       `🔬 [Research Generate] Starting research for content: ${contentId}`
//     )
//
//     // Fetch the drag tree and node to get preferred language, screening question, and question text
//     const nodeContent = await prisma.dragTreeNodeContent.findUnique({
//       where: { id: contentId },
//       select: {
//         id: true,
//         drag_tree_node: {
//           select: {
//             label: true, // This contains the question text
//             drag_tree: {
//               select: {
//                 id: true,
//                 preferred_language: true,
//                 user_prompt: true,
//               },
//             },
//           },
//         },
//       },
//     })
//
//     if (!nodeContent?.drag_tree_node?.drag_tree) {
//       throw new Error('Drag tree not found for content')
//     }
//
//     const dragTree = nodeContent.drag_tree_node.drag_tree
//     const dragTreeNode = nodeContent.drag_tree_node
//
//     // The node label is the question text
//     const questionText = dragTreeNode.label
//
//     if (!questionText || !questionText.trim()) {
//       return NextResponse.json(
//         { error: 'Question text not found in drag tree node label' },
//         { status: 400 }
//       )
//     }
//
//     // This route is specifically for quick research
//     const researchType: DragTreeNodeContentType =
//       DragTreeNodeContentType.QUICK_RESEARCH
//
//     console.log(
//       `🔍 [Research Generate] Raw preferred_language from DB:`,
//       dragTree.preferred_language,
//       `(type: ${typeof dragTree.preferred_language})`
//     )
//
//     const preferredLanguage =
//       (dragTree.preferred_language as SupportedLanguageCode) || 'en'
//     const language = getLanguageName(preferredLanguage)
//     // Require user_prompt - fail early if missing since we haven't started expensive API calls
//     if (!dragTree.user_prompt || !dragTree.user_prompt.trim()) {
//       return NextResponse.json(
//         {
//           error:
//             'Missing user_prompt in drag tree. Cannot proceed with research generation.',
//         },
//         { status: 400 }
//       )
//     }
//     const screeningQuestion = dragTree.user_prompt
//
//     console.log(
//       `🔍 [Research Generate] After processing: preferredLanguage="${preferredLanguage}", language="${language}"`
//     )
//
//     console.log(
//       `🌍 [Research Generate] Language detected: ${preferredLanguage} -> "${language}", Screening: ${screeningQuestion ? 'Yes' : 'No'}`
//     )
//     console.log(
//       `📝 [Research Generate] Prompt will end with: "Please generate the language in ${language}"`
//     )
//
//     // Select model configuration based on feature flag
//     const modelConfig = await getModelConfigFromSession(
//       session,
//       ENABLE_TWO_STAGE_RESEARCH
//         ? 'dragtree_2steps_research_generate'
//         : 'dragtree_research_generate'
//     )
//     const model_name = modelConfig.model
//     const model = getModelFromConfig(modelConfig)
//
//     // 1. Create a single, authoritative metadata object upfront.
//     const generationMetadata = {
//       researchType,
//       originalQuestion: questionText,
//       model: model_name,
//       language: preferredLanguage,
//       startedAt: new Date().toISOString(),
//       completedAt: '', // Will be set on finish
//       hasWebSearch: false, // Will be updated on finish
//       tokenUsage: {
//         inputTokens: 0,
//         outputTokens: 0,
//         totalTokens: 0,
//       },
//     }
//
//     // Update content status to PROCESSING
//     await prisma.dragTreeNodeContent.update({
//       where: { id: contentId },
//       data: {
//         status: DragTreeNodeContentStatus.PROCESSING,
//         // Store the initial metadata
//         generation_metadata: generationMetadata,
//       },
//     })
//
//     // Set up a timeout to reset status if API hangs
//     timeoutId = setTimeout(async () => {
//       try {
//         console.warn(
//           `⏰ [Research Generate] Timeout reached for content: ${contentId}`
//         )
//         await prisma.dragTreeNodeContent.update({
//           where: { id: contentId },
//           data: {
//             status: DragTreeNodeContentStatus.INACTIVE,
//             generation_metadata: {
//               error: 'Research timed out after 5 minutes',
//               failedAt: new Date().toISOString(),
//               timeoutReason: 'API_TIMEOUT',
//             },
//           },
//         })
//       } catch (error) {
//         console.error(
//           '💥 [Research Generate] Failed to reset timeout status:',
//           error
//         )
//       }
//     }, PROCESSING_TIMEOUT_MS)
//
//     // Stage 1: Template Assessment (if two-stage research is enabled)
//     let systemMessage: string
//
//     if (ENABLE_TWO_STAGE_RESEARCH) {
//       console.log(' [Two-Stage Research] Assessing template suitability...')
//
//       try {
//         const assessment = await assessTemplate(
//           userId,
//           contentId,
//           screeningQuestion || '',
//           questionText
//         )
//
//         if (assessment && assessment.suitable) {
//           console.log(
//             '🔬 [Two-Stage Research] Using decision analyst template:',
//             assessment.reasons
//           )
//           const decisionAnalystTemplate = DECISION_ANALYST_TEMPLATE
//
//           systemMessage = createCustomTemplatePrompt(
//             decisionAnalystTemplate,
//             questionText,
//             screeningQuestion,
//             language
//           )
//         } else if (
//           assessment &&
//           !assessment.suitable &&
//           assessment.customTemplate
//         ) {
//           console.log(
//             '🔬 [Two-Stage Research] Using custom template:',
//             assessment.reasons
//           )
//           systemMessage = createCustomTemplatePrompt(
//             assessment.customTemplate,
//             questionText,
//             screeningQuestion,
//             language
//           )
//         } else {
//           console.log(
//             '🔬 [Two-Stage Research] Using default template:',
//             assessment?.reasons || ['Assessment failed']
//           )
//           systemMessage = createResearchPrompt(
//             questionText,
//             screeningQuestion,
//             language
//           )
//         }
//       } catch (error) {
//         console.error(
//           '🔬 [Two-Stage Research] Assessment failed, using default template:',
//           error
//         )
//         systemMessage = createResearchPrompt(
//           questionText,
//           screeningQuestion,
//           language
//         )
//       }
//     } else {
//       // Use default template when feature flag is disabled
//       systemMessage = createResearchPrompt(
//         questionText,
//         screeningQuestion,
//         language
//       )
//     }
//
//     console.log(
//       `🔍 [Research Generate] Generated prompt preview (last 200 chars):`,
//       systemMessage.slice(-200)
//     )
//
//     // v1 route uses Brave search implementation
//     console.log('🔧 [Research Generate] Using Brave search implementation')
//
//     // Request-scoped metadata collector for thread safety
//     // Each API request gets its own collector to prevent cross-request contamination
//     const collectedSearchMetadata: SearchMetadata[] = []
//
//     // AI SDK v5: Use stopWhen to control multi-step execution
//     // Allow up to 10 steps, but ensure the model provides a final answer
//     const MAX_STEPS = 10
//
//     // Legacy Brave search implementation
//     const result = streamText({
//       model: model,
//       messages: [
//         {
//           role: 'system',
//           content: systemMessage,
//         },
//       ],
//       tools: buildSearchTools(collectedSearchMetadata),
//       // Force the model to use at least one web search call before drafting the final answer
//       toolChoice: 'required',
//       // AI SDK v5: Use stopWhen instead of maxSteps for server-side control
//       stopWhen: stepCountIs(MAX_STEPS),
//       temperature: modelConfig.temperature,
//       maxOutputTokens: modelConfig.maxOutputTokens,
//       providerOptions: modelConfig.providerOptions as any,
//       // AI SDK v5: Use stable prepareStep API (no longer experimental)
//       prepareStep: async ({ stepNumber }: { stepNumber: number }) => {
//         // Reserve the last 2 steps for final answer generation
//         const RESERVED_STEPS = 2
//         const remaining = MAX_STEPS - stepNumber
//         // If no search has been executed yet, keep requiring the webSearch tool exclusively
//         if (collectedSearchMetadata.length === 0) {
//           return {
//             toolChoice: 'required',
//             activeTools: ['webSearch'],
//           }
//         }
//
//         if (remaining <= RESERVED_STEPS) {
//           // Disable tools for the last reserved step(s) to force final answer
//           return {
//             toolChoice: 'none',
//             activeTools: [],
//           }
//         }
//         return undefined
//       },
//       onFinish: async result => {
//         // Clear timeout as soon as the run finishes
//         clearTimeoutSafely()
//         try {
//           console.log(
//             `✅ [Research Generate] Completed research for content: ${contentId}`
//           )
//
//           const searchMetadata = collectedSearchMetadata
//           console.log(
//             `📊 [Research Generate] Collected ${searchMetadata.length} search metadata entries`
//           )
//
//           // Update metadata with completion details
//           generationMetadata.completedAt = new Date().toISOString()
//           generationMetadata.tokenUsage = {
//             inputTokens: (result.usage as any)?.promptTokens || 0,
//             outputTokens: (result.usage as any)?.completionTokens || 0,
//             totalTokens:
//               ((result.usage as any)?.promptTokens || 0) +
//               ((result.usage as any)?.completionTokens || 0),
//           }
//           generationMetadata.hasWebSearch = searchMetadata.length > 0
//
//           // Compress search results by stripping heavy snippet arrays to minimize storage
//           const compressedSearchMetadata = searchMetadata.map(
//             ({ snippets: _snippets, ...rest }) => ({
//               ...rest,
//               snippets: [], // Preserve key for UI logic but omit payload
//             })
//           )
//
//           const contentMetadata: Prisma.InputJsonValue = {
//             searchResults: compressedSearchMetadata,
//             generationInfo: generationMetadata,
//           }
//
//           // Store complete conversation history including system prompt for future continuation
//           const completeConversation = [
//             {
//               role: 'system',
//               content: systemMessage,
//             },
//             ...result.response.messages,
//           ]
//
//           // Extract final text with fallback logic
//           let finalText: string = (
//             typeof result.text === 'string' ? result.text : ''
//           ).trim()
//
//           // Fallback: Extract from assistant messages
//           if (!finalText) {
//             const assistantMessages =
//               result.response.messages?.filter(m => m.role === 'assistant') ||
//               []
//
//             finalText = assistantMessages
//               .map(m => {
//                 let extracted = ''
//
//                 // Handle different message formats from AI SDK
//                 if (typeof m.content === 'string') {
//                   extracted = m.content
//                 } else if (Array.isArray(m.content)) {
//                   // Extract text and tool result content
//                   extracted = m.content
//                     .filter(part => {
//                       return (
//                         ((part as any).type === 'text' && (part as any).text) ||
//                         ((part as any).type === 'tool-result' &&
//                           (part as any).result)
//                       )
//                     })
//                     .map(part => {
//                       if ((part as any).text) {
//                         return (part as any).text
//                       }
//                       if ((part as any).result) {
//                         return typeof (part as any).result === 'string'
//                           ? (part as any).result
//                           : JSON.stringify((part as any).result)
//                       }
//                       return ''
//                     })
//                     .join('')
//                 }
//
//                 return extracted
//               })
//               .filter(Boolean)
//               .join('\n')
//               .trim()
//           }
//
//           // Last resort: Use search results if no text content
//           if (!finalText && searchMetadata.length > 0) {
//             const searchSummary = searchMetadata
//               .slice(0, 3)
//               .map(result => `**${result.url}**\n${result.snippets.join(' ')}`)
//               .join('\n\n')
//
//             if (searchSummary.trim()) {
//               finalText = `Based on the search results, here are the key findings:\n\n${searchSummary}\n\n*Note: This response was generated from search results due to an AI processing issue.*`
//             }
//           }
//
//           // Final fallback to avoid empty DB writes
//           if (!finalText) {
//             finalText = '[No answer returned]'
//           }
//
//           // Update content and log AI usage in parallel
//           await Promise.all([
//             prisma.dragTreeNodeContent.update({
//               where: { id: contentId },
//               data: {
//                 status: DragTreeNodeContentStatus.ACTIVE,
//                 content_text: finalText, // ← use robust text
//                 content_metadata: contentMetadata,
//                 messages:
//                   completeConversation as unknown as Prisma.InputJsonValue,
//                 generation_metadata: generationMetadata, // Overwrite with the completed metadata
//               },
//             }),
//             createAIUsage({
//               userId: userId,
//               entityType: 'drag_tree_node_content',
//               entityId: contentId!,
//               aiProvider: getProviderNameForUsage(modelConfig),
//               modelName: model_name,
//               usageType: AIUsageType.NODE_QUICK_RESEARCH,
//               inputPrompt: systemMessage,
//               messages: completeConversation,
//               metadata: {
//                 tokenUsage: result.usage,
//                 searchMetadataCount: searchMetadata.length,
//                 hasWebSearch: searchMetadata.length > 0,
//                 language: preferredLanguage,
//                 researchType,
//               },
//               config: {
//                 maxSteps: MAX_STEPS,
//                 reservedSteps: 2, // RESERVED_STEPS constant
//               },
//             }),
//           ])
//
//           console.log(
//             `🎉 [Research Generate] Successfully saved research to DB for content: ${contentId} with ${searchMetadata.length} search metadata entries`
//           )
//         } catch (error) {
//           console.error(`💥 [Research Generate] Error saving to DB:`, error)
//
//           // Mark as failed if DB update fails
//           await prisma.dragTreeNodeContent.update({
//             where: { id: contentId },
//             data: {
//               status: DragTreeNodeContentStatus.INACTIVE,
//               generation_metadata: {
//                 error: error instanceof Error ? error.message : 'Unknown error',
//                 failedAt: new Date().toISOString(),
//               },
//             },
//           })
//         }
//       },
//     })
//
//     return result.toUIMessageStreamResponse({
//       onFinish: async () => {
//         // No DB writes here; server-side onFinish already persisted.
//         console.log(
//           `✅ [Research Generate] UI Stream completed for content: ${contentId}`
//         )
//       },
//     })
//   } catch (error) {
//     console.error('💥 [Research Generate] API error:', error)
//
//     // Clear timeout to prevent duplicate status updates
//     clearTimeoutSafely()
//
//     // Reset status from PROCESSING to prevent blocking if we have contentId
//     if (typeof contentId === 'string') {
//       try {
//         await prisma.dragTreeNodeContent.update({
//           where: { id: contentId },
//           data: {
//             status: DragTreeNodeContentStatus.INACTIVE,
//             generation_metadata: {
//               error:
//                 error instanceof Error ? error.message : 'Unknown API error',
//               failedAt: new Date().toISOString(),
//             },
//           },
//         })
//         console.log(
//           `🔄 [Research Generate] Reset status for content: ${contentId}`
//         )
//       } catch (dbError) {
//         console.error('💥 [Research Generate] Failed to reset status:', dbError)
//       }
//     }
//
//     return NextResponse.json(
//       { error: 'Internal server error' },
//       { status: 500 }
//     )
//   }
// }
