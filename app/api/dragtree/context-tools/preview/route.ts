import { NextRequest, NextResponse } from 'next/server'
import { generateResearchPreviewMarkdown } from '@/app/api/dragtree/shared/context-tools'

export async function GET(request: NextRequest) {
  const dragTreeId = request.nextUrl.searchParams.get('dragTreeId')

  if (!dragTreeId) {
    return NextResponse.json(
      {
        success: false,
        error: 'Missing dragTreeId parameter',
      },
      { status: 400 }
    )
  }

  try {
    const markdown = await generateResearchPreviewMarkdown(dragTreeId)

    return NextResponse.json({
      success: true,
      data: {
        dragTreeId,
        markdown,
      },
    })
  } catch (error) {
    const message =
      error instanceof Error
        ? error.message
        : 'Failed to build preview markdown'
    const status = message === 'Unauthorized' ? 401 : 500

    return NextResponse.json(
      {
        success: false,
        error: message,
      },
      { status }
    )
  }
}
