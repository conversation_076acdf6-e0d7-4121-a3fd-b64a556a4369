import { NextRequest, NextResponse } from 'next/server'
import {
  buildResearchPreviewSnapshot,
  getCategoryContextByCategoryId,
} from '@/app/api/dragtree/shared/context-tools'

export async function GET(request: NextRequest) {
  const dragTreeId = request.nextUrl.searchParams.get('dragTreeId')
  const categoryId = request.nextUrl.searchParams.get('categoryId')
  const totalTokensParam = request.nextUrl.searchParams.get('totalTokens')

  if (!dragTreeId || !categoryId) {
    return NextResponse.json(
      {
        success: false,
        error: 'Missing dragTreeId or categoryId parameter',
      },
      { status: 400 }
    )
  }

  const totalTokens = totalTokensParam ? Number(totalTokensParam) : undefined

  try {
    const snapshot = await buildResearchPreviewSnapshot(dragTreeId)
    const context = getCategoryContextByCategoryId(snapshot, categoryId, {
      totalTokens: Number.isFinite(totalTokens) ? totalTokens : undefined,
    })

    return NextResponse.json({
      success: true,
      data: context,
    })
  } catch (error) {
    const message =
      error instanceof Error
        ? error.message
        : 'Failed to fetch category context'
    const status = message === 'Unauthorized' ? 401 : 500

    return NextResponse.json(
      {
        success: false,
        error: message,
      },
      { status }
    )
  }
}
