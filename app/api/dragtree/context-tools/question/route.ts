import { NextRequest, NextResponse } from 'next/server'
import {
  buildResearchPreviewSnapshot,
  getQuestionContextByContentId,
} from '@/app/api/dragtree/shared/context-tools'

export async function GET(request: NextRequest) {
  const dragTreeId = request.nextUrl.searchParams.get('dragTreeId')
  const contentId = request.nextUrl.searchParams.get('contentId')
  const tokenLimitParam = request.nextUrl.searchParams.get('tokenLimit')

  if (!dragTreeId || !contentId) {
    return NextResponse.json(
      {
        success: false,
        error: 'Missing dragTreeId or contentId parameter',
      },
      { status: 400 }
    )
  }

  const tokenLimit = tokenLimitParam ? Number(tokenLimitParam) : undefined

  try {
    const snapshot = await buildResearchPreviewSnapshot(dragTreeId)
    const context = getQuestionContextByContentId(snapshot, contentId, {
      tokenLimit: Number.isFinite(tokenLimit) ? tokenLimit : undefined,
    })

    return NextResponse.json({
      success: true,
      data: context,
    })
  } catch (error) {
    const message =
      error instanceof Error
        ? error.message
        : 'Failed to fetch question context'
    const status = message === 'Unauthorized' ? 401 : 500

    return NextResponse.json(
      {
        success: false,
        error: message,
      },
      { status }
    )
  }
}
