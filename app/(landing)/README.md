# Landing Area

This directory hosts the primary landing experience and its legacy versions.

- `page.tsx`: New landing page implementation (2025) using components in `components/`.
- `components/`: All landing UI components (header, hero, sections, footer, pricing preview). Reused across `app/page.tsx` and `(landing)/page.tsx`.
- `_legacy/landing_page_2024/`: Fully functional 2024 landing preserved for reference.

Key notes

- Typography utilities (`.text-h1`, `.text-h2`, `.text-h3`, `.text-body`, `.text-body-reduced`) are defined in `app/globals.css`.
- Auth CTA uses `app/(landing)/components/AuthForm.tsx` and routes sign-ins to `/screening`.
- Pricing preview (`LandingPricingPreview`) reads plan features from `app/subscription/components/ModernPricingConfig.ts` and deliberately omits subscription CTAs for the landing page.
- Legal links point to existing pages: `/privacy` and `/tos`.
- Challenge selector in `SolutionSection` scrolls visitors directly to the Issue Tree visualization (centered in view) so the refreshed strategic map is immediately visible. Each option now shows the full persona narrative with its own scroll, while the CTA remains pinned.
- The Issue Tree visualization auto-fits with a wider canvas, starts at a readable zoom, and gently zooms (slightly out) toward the clicked node; leaf nodes use a distinct highlight and show a zoom-in cursor on hover.
- Strategic demo data lives in `app/(landing)/components/strategic-examples.ts`; add new entries there to power both the issue tree layout and the research snapshot UI. The CTA now pulses until the visitor clicks, the reasoning stream runs first, and then the answer markdown (including tables) streams quickly into a scrollable panel that auto-follows unless the visitor interrupts (Jump to top, manual scroll); the Step 1 CTA is reused once the transcript appears.

SEO & performance

- Global metadata (title, description, robots, Open Graph, Twitter) is defined in `app/layout.tsx` using Next Metadata. A canonical URL is set based on `NEXT_PUBLIC_SITE_URL`.
- JSON-LD for `Organization` and `WebSite` is injected from `app/layout.tsx`.
- `app/robots.ts` and `app/sitemap.ts` are provided for proper crawlability.
- Hero video defers mounting with IntersectionObserver and `requestIdleCallback` to improve first paint. A lightweight image placeholder is shown until the video mounts. The `<video>` uses `preload="none"`.

Integration

- Root route (`app/page.tsx`) renders the new landing.
- The prior production landing is available at `/(landing)/_legacy/landing_page_2024`.
