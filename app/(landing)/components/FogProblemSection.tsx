'use client'

import { motion } from 'framer-motion'
import { Compass, Target, Calendar } from 'lucide-react'
import { useEffect, useRef } from 'react'

export default function FogProblemSection() {
  const canvasRef = useRef<HTMLCanvasElement | null>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    let raf = 0
    let t = 0
    const baseTerms = [
      'synergy',
      'growth',
      'TAM',
      'churn',
      'runway',
      'CAC',
      'NPS',
      'LTV',
      'activation',
      'OKR',
      'retention',
      'ARR',
      'MRR',
      'personas',
      'roadmap',
      'leads',
      'pricing',
      'PMF',
      'expansion',
      'margin',
      'unit economics',
    ]
    const terms = Array.from(
      { length: 150 },
      (_, i) => baseTerms[i % baseTerms.length]
    )

    const resize = () => {
      canvas.width = canvas.clientWidth * devicePixelRatio
      canvas.height = canvas.clientHeight * devicePixelRatio
    }
    resize()
    window.addEventListener('resize', resize)

    const draw = () => {
      t += 0.003
      const { width, height } = canvas
      ctx.clearRect(0, 0, width, height)

      // fog background (layered soft gradients for thicker fog)
      for (let i = 0; i < 5; i++) {
        const gx = (0.5 + 0.25 * Math.sin(t * (0.5 + i * 0.2) + i)) * width
        const gy = (0.5 + 0.25 * Math.cos(t * (0.6 + i * 0.15) - i)) * height
        const grad = ctx.createRadialGradient(
          gx,
          gy,
          width * 0.05,
          width / 2,
          height / 2,
          width * 0.9
        )
        grad.addColorStop(0, `rgba(40,40,48,${0.12 + i * 0.05})`)
        grad.addColorStop(1, 'rgba(10,10,12,0.94)')
        ctx.fillStyle = grad
        ctx.fillRect(0, 0, width, height)
      }

      // tangled faint lines
      ctx.lineWidth = 1 * devicePixelRatio
      ctx.strokeStyle = 'rgba(120,130,150,0.12)'
      for (let i = 0; i < 40; i++) {
        const y = height / 2 + Math.sin(t * 2 + i) * (height * 0.25)
        ctx.beginPath()
        ctx.moveTo(0, y)
        for (let x = 0; x <= width; x += 40 * devicePixelRatio) {
          const offset =
            Math.sin(x * 0.003 + t * (1 + i * 0.01)) * 30 * devicePixelRatio
          ctx.lineTo(x, y + offset)
        }
        ctx.stroke()
      }

      // drifting terms
      ctx.font = `${12 * devicePixelRatio}px ui-sans-serif, system-ui`
      ctx.fillStyle = 'rgba(200,210,230,0.14)'
      terms.forEach((word, idx) => {
        const x =
          width * (0.2 + (0.6 * ((idx * 97) % 100)) / 100) +
          20 * Math.sin(t * (idx + 1))
        const y =
          height * (0.3 + (0.4 * ((idx * 71) % 100)) / 100) +
          15 * Math.cos(t * (idx + 1.3))
        ctx.fillText(word, x, y)
      })

      raf = requestAnimationFrame(draw)
    }
    raf = requestAnimationFrame(draw)
    return () => {
      cancelAnimationFrame(raf)
      window.removeEventListener('resize', resize)
    }
  }, [])

  return (
    <section className="min-h-screen flex items-center px-6 bg-slate-950 text-slate-50">
      <div className="max-w-6xl mx-auto">
        <motion.h2
          className="text-h2 text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          Every Great Project Begins in Fog.
        </motion.h2>

        <div className="grid lg:grid-cols-3 gap-8 items-center">
          {/* Left text block */}
          <div className="space-y-3">
            <div className="w-10 h-10 rounded-xl bg-slate-800/40 border border-slate-800 flex items-center justify-center">
              <Compass className="w-6 h-6 text-slate-200" />
            </div>
            <h3 className="text-xl font-semibold">
              Noise Leads to a Random Walk.
            </h3>
            <p className="text-slate-400 text-sm">
              You work hard, digging through random searches and unstructured
              notes.{' '}
              <strong>You feel busy, but real progress feels distant.</strong>
            </p>
          </div>

          {/* Center fog visual */}
          <div className="relative h-72 lg:h-96 rounded-2xl overflow-hidden border border-slate-800 bg-slate-900">
            <canvas ref={canvasRef} className="w-full h-full" />
          </div>

          {/* Right text block */}
          <div className="space-y-3">
            <div className="w-10 h-10 rounded-xl bg-slate-800/40 border border-slate-800 flex items-center justify-center">
              <Target className="w-6 h-6 text-slate-200" />
            </div>
            <h3 className="text-xl font-semibold">
              Blind Spots Lead to Dead Ends.
            </h3>
            <p className="text-slate-400 text-sm">
              A single, unasked question can lead your work down a strategic
              dead end.{' '}
              <strong>Months of effort, wasted on a flawed path.</strong>
            </p>
          </div>
        </div>

        {/* Bottom text block */}
        <div className="mt-10 max-w-2xl mx-auto text-center space-y-3">
          <div className="w-10 h-10 rounded-xl bg-slate-800/40 border border-slate-800 flex items-center justify-center mx-auto">
            <Calendar className="w-6 h-6 text-slate-200" />
          </div>
          <h3 className="text-xl font-semibold">
            Inertia Leads to Endless Meetings.
          </h3>
          <p className="text-slate-400 text-sm">
            Without a clear path, decisions get postponed.{' '}
            <strong>
              The team loops in more people, schedules the &quot;next
              meeting,&quot; and momentum dies while great ideas fade into
              memory.
            </strong>
          </p>
        </div>

        {/* Transition Hook removed: dedicated full-screen slide will follow */}
      </div>
    </section>
  )
}
