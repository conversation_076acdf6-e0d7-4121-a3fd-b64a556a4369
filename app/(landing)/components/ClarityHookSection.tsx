'use client'

import { motion } from 'framer-motion'

export default function ClarityHookSection() {
  return (
    <section className="min-h-screen bg-slate-950 text-slate-50 flex items-center px-6">
      <div className="max-w-5xl mx-auto w-full">
        <motion.h2
          className="text-h2 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          What if you could turn ambiguity into a blueprint for action, in
          minutes?
        </motion.h2>
      </div>
    </section>
  )
}
