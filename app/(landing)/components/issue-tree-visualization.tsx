'use client'

import {
  useCallback,
  useMemo,
  useEffect,
  useState,
  type MouseEvent,
} from 'react'
import ReactFlow, {
  type Node,
  type Edge,
  type ReactFlowInstance,
  useNodesState,
  useEdgesState,
  Background,
  Controls,
  Position,
} from 'reactflow'
import 'reactflow/dist/style.css'
import type { IssueTreeItem } from '@/app/(landing)/components/types'

type Props = {
  issueTree: IssueTreeItem
}

type LayoutExtents = {
  minY: number
  maxY: number
}

const HORIZONTAL_GAP = 260
const VERTICAL_GAP = 120

function applyNodeStyle(depth: number, hasChildren: boolean): Node['style'] {
  if (!hasChildren) {
    return {
      background: 'rgb(15, 23, 42)',
      color: 'rgb(226, 232, 240)',
      border: '1px solid rgb(37, 99, 235)',
      borderRadius: '8px',
      fontSize: '11px',
      fontWeight: '500',
      padding: '8px 10px',
      width: '210px',
      whiteSpace: 'normal',
      lineHeight: '1.35',
      boxShadow: '0 0 0 1px rgba(37, 99, 235, 0.25)',
    }
  }
  if (depth === 0) {
    return {
      background: 'rgb(59, 130, 246)',
      color: 'white',
      border: '1px solid rgb(59, 130, 246)',
      borderRadius: '10px',
      fontSize: '14px',
      fontWeight: '600',
      padding: '10px 14px',
      width: '220px',
      whiteSpace: 'normal',
      lineHeight: '1.4',
    }
  }
  if (depth === 1) {
    return {
      background: 'rgb(30,30,30)',
      color: 'rgb(235,235,235)',
      border: '1px solid rgb(70,70,70)',
      borderRadius: '10px',
      fontSize: '12px',
      fontWeight: '600',
      padding: '10px 12px',
      width: '200px',
      whiteSpace: 'normal',
      lineHeight: '1.35',
    }
  }
  return {
    background: 'rgb(10,10,10)',
    color: 'rgb(200,200,200)',
    border: '1px solid rgb(40,40,40)',
    borderRadius: '6px',
    fontSize: '10px',
    fontWeight: '400',
    padding: '6px 8px',
    width: '200px',
    whiteSpace: 'normal',
    lineHeight: '1.3',
  }
}

function buildGraphFromTree(tree: IssueTreeItem): {
  nodes: Node[]
  edges: Edge[]
} {
  const nodes: Node[] = []
  const edges: Edge[] = []
  let leafCounter = 0

  const traverse = (
    item: IssueTreeItem,
    depth: number,
    parentId: string | null
  ): LayoutExtents => {
    const x = depth * HORIZONTAL_GAP
    let minY: number | null = null
    let maxY: number | null = null

    const hasChildren = Array.isArray(item.children) && item.children.length > 0

    if (hasChildren) {
      item.children?.forEach(child => {
        const childExtents = traverse(child, depth + 1, item.id)
        minY =
          minY === null ? childExtents.minY : Math.min(minY, childExtents.minY)
        maxY =
          maxY === null ? childExtents.maxY : Math.max(maxY, childExtents.maxY)

        edges.push({
          id: `${item.id}-${child.id}`,
          source: item.id,
          target: child.id,
          style: { stroke: 'rgb(55,55,55)', strokeWidth: 1.2 },
        })
      })
    }

    const y = (() => {
      if (hasChildren && minY !== null && maxY !== null) {
        return (minY + maxY) / 2
      }
      const leafY = leafCounter * VERTICAL_GAP
      leafCounter += 1
      minY = leafY
      maxY = leafY
      return leafY
    })()

    const style = applyNodeStyle(depth, hasChildren)
    const className = hasChildren
      ? 'cursor-zoom-in transition duration-200 ease-out hover:-translate-y-1 hover:scale-[1.02] hover:shadow-[0_12px_30px_rgba(15,23,42,0.25)]'
      : 'cursor-zoom-in transition duration-200 ease-out hover:-translate-y-1 hover:scale-[1.02] hover:shadow-[0_12px_30px_rgba(30,64,175,0.35)]'

    nodes.push({
      id: item.id,
      position: { x, y },
      data: { label: item.title },
      sourcePosition: hasChildren ? Position.Right : undefined,
      targetPosition: parentId ? Position.Left : undefined,
      className,
      draggable: false,
      selectable: false,
      style,
    })

    return {
      minY: minY ?? y,
      maxY: maxY ?? y,
    }
  }

  traverse(tree, 0, null)

  return { nodes, edges }
}

export default function IssueTreeVisualization({ issueTree }: Props) {
  const { nodes: initialNodes, edges: initialEdges } = useMemo(
    () => buildGraphFromTree(issueTree),
    [issueTree]
  )
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)
  const [rf, setRf] = useState<ReactFlowInstance | null>(null)

  useEffect(() => {
    const { nodes: graphNodes, edges: graphEdges } =
      buildGraphFromTree(issueTree)
    setNodes(graphNodes)
    setEdges(graphEdges)
    if (rf) {
      setTimeout(() => {
        try {
          rf.fitView({ padding: 0.28, minZoom: 0.2, maxZoom: 0.6 })
        } catch {}
      }, 50)
    }
  }, [issueTree, setNodes, setEdges, rf])

  const handleNodeClick = useCallback(
    (_: MouseEvent, node: Node) => {
      if (!rf) {
        return
      }
      const position = node.positionAbsolute ?? node.position
      const width = node.width ?? 0
      const height = node.height ?? 0

      const currentZoom = rf.getZoom()
      const targetZoom = Math.max(0.55, Math.min(currentZoom * 0.95, 0.75))

      rf.setCenter(position.x + width / 2, position.y + height / 2, {
        zoom: targetZoom,
        duration: 420,
      })
    },
    [rf]
  )

  return (
    <div className="bg-slate-100/30 rounded-2xl p-8 border border-slate-200 dark:bg-slate-800/30 dark:border-slate-800">
      <div className="h-[28rem] bg-white rounded-lg border border-slate-200 overflow-hidden dark:bg-slate-950 dark:border-slate-800">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onNodeClick={handleNodeClick}
          fitView
          fitViewOptions={{ padding: 0.28, minZoom: 0.2, maxZoom: 0.6 }}
          nodesDraggable={false}
          nodesConnectable={false}
          elementsSelectable={false}
          minZoom={0.2}
          maxZoom={1.4}
          attributionPosition="bottom-left"
          onInit={setRf}
          className="cursor-default"
        >
          <Background color="rgb(40, 40, 40)" />
          <Controls />
        </ReactFlow>
      </div>
      <p className="text-slate-500 text-center mt-4 text-sm dark:text-slate-400">
        Interactive issue tree - scroll and zoom to explore
      </p>
    </div>
  )
}
