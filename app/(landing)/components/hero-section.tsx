'use client'

import { motion } from 'framer-motion'
import AuthForm from '@/app/(landing)/components/AuthForm'
import { useEffect, useRef, useState } from 'react'
import Image from 'next/image'

export default function HeroSection() {
  // Defer mounting the video until idle or in-view to improve first load.
  const [mountVideo, setMountVideo] = useState<boolean>(false)
  const videoContainerRef = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    let idleId: number | null = null
    let timeoutId: number | null = null
    // Prefer intersection to mount only when user can see it.
    const observer = new IntersectionObserver(
      entries => {
        const entry = entries[0]
        if (entry && entry.isIntersecting) {
          setMountVideo(true)
          observer.disconnect()
        }
      },
      { rootMargin: '200px 0px' }
    )

    if (videoContainerRef.current) {
      observer.observe(videoContainerRef.current)
    }

    // Fallback: if idle first, mount anyway to keep UX parity.
    if (typeof (window as any).requestIdleCallback === 'function') {
      idleId = (window as any).requestIdleCallback(() => setMountVideo(true), {
        timeout: 2000,
      })
    } else {
      timeoutId = (window as any).setTimeout(() => setMountVideo(true), 1500)
    }

    return () => {
      observer.disconnect()
      if (idleId) {
        if ('cancelIdleCallback' in window) {
          ;(window as any).cancelIdleCallback(idleId)
        } else {
          clearTimeout(idleId)
        }
      }
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
    }
  }, [])

  return (
    <section className="min-h-screen pt-32 pb-20 px-6 bg-slate-950 text-slate-50 flex items-center">
      <div className="max-w-4xl mx-auto text-center">
        <motion.h1
          className="text-h1 text-slate-50 mb-8"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          Great Work Starts with Great Questions.
        </motion.h1>

        <motion.p
          className="text-h2 text-slate-400 mb-12 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          AI holds countless answers. Thought Atlas builds the net to capture
          the right ones.
        </motion.p>

        {/* Hero video / fallback */}
        <motion.div
          className="mb-16"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div
            ref={videoContainerRef}
            className="relative mx-auto max-w-3xl rounded-2xl overflow-hidden border border-slate-800 bg-slate-900"
          >
            {mountVideo ? (
              <video
                className="w-full h-full object-cover"
                // Avoid preloading to reduce initial bandwidth
                preload="none"
                autoPlay
                muted
                playsInline
                loop
                poster="/images/placeholder.jpg"
              >
                <source src="/videos/hero.webm" type="video/webm" />
                <source src="/videos/hero.mp4" type="video/mp4" />
              </video>
            ) : (
              // Lightweight placeholder uses optimized Next.js Image
              <div
                className="relative w-full"
                style={{ aspectRatio: '16 / 9' }}
              >
                <Image
                  src="/images/placeholder.jpg"
                  alt="Product preview"
                  fill
                  priority
                  sizes="(min-width: 768px) 768px, 100vw"
                  className="object-cover"
                />
              </div>
            )}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <AuthForm />
        </motion.div>
      </div>
    </section>
  )
}
