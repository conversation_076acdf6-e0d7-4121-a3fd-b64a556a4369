'use client'

import { motion } from 'framer-motion'
import IssueTreeVisualization from '@/app/(landing)/components/issue-tree-visualization'
import { useEffect, useMemo, useRef, useState } from 'react'
import AuthForm from '@/app/(landing)/components/AuthForm'
import {
  Reasoning,
  ReasoningContent,
  ReasoningTrigger,
} from '@/components/ai-elements/reasoning'
import { Response } from '@/components/ai-elements/response'
import { STRATEGIC_EXAMPLES } from '@/app/(landing)/components/strategic-examples'
import { cn } from '@/lib/utils'
import { Sparkles } from 'lucide-react'

export default function SolutionSection() {
  const [selectedExampleId, setSelectedExampleId] = useState<string>(
    STRATEGIC_EXAMPLES[0]?.id ?? ''
  )
  const selectedExample = useMemo(
    () =>
      STRATEGIC_EXAMPLES.find(example => example.id === selectedExampleId) ??
      STRATEGIC_EXAMPLES[0],
    [selectedExampleId]
  )
  const [isReasoningStreaming, setIsReasoningStreaming] =
    useState<boolean>(false)
  const [reasoningLines, setReasoningLines] = useState<string[]>([])
  const [isAnswerStreaming, setIsAnswerStreaming] = useState<boolean>(false)
  const [answerLines, setAnswerLines] = useState<string[]>([])
  const [hasTriggeredResearch, setHasTriggeredResearch] =
    useState<boolean>(false)
  const reasoningTimerRef = useRef<ReturnType<typeof setInterval> | null>(null)
  const answerTimerRef = useRef<ReturnType<typeof setInterval> | null>(null)
  const postReasoningDelayRef = useRef<ReturnType<typeof setTimeout> | null>(
    null
  )
  const responseScrollRef = useRef<HTMLDivElement | null>(null)
  const [isResponseAutoScroll, setIsResponseAutoScroll] =
    useState<boolean>(true)
  const [treeResetSeed, setTreeResetSeed] = useState<number>(0)
  // Keep a handle on the Issue Tree area so we can guide the visitor to the refreshed view.
  const issueTreeRef = useRef<HTMLDivElement | null>(null)
  const scrollToIssueTree = () => {
    if (typeof window === 'undefined') {
      return
    }

    window.requestAnimationFrame(() => {
      issueTreeRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest',
      })
    })
  }
  const resetResearchState = () => {
    clearTimers()
    setIsReasoningStreaming(false)
    setReasoningLines([])
    setIsAnswerStreaming(false)
    setAnswerLines([])
    setHasTriggeredResearch(false)
    setIsResponseAutoScroll(true)
  }

  // Selecting a challenge should both update the state and reveal the refreshed map.
  const handleChallengeSelect = (challenge: string) => {
    resetResearchState()
    setTreeResetSeed(prev => prev + 1)
    if (challenge !== selectedExampleId) {
      setSelectedExampleId(challenge)
    }
    scrollToIssueTree()
  }
  function clearTimers() {
    if (reasoningTimerRef.current) {
      clearInterval(reasoningTimerRef.current)
      reasoningTimerRef.current = null
    }
    if (answerTimerRef.current) {
      clearInterval(answerTimerRef.current)
      answerTimerRef.current = null
    }
    if (postReasoningDelayRef.current) {
      clearTimeout(postReasoningDelayRef.current)
      postReasoningDelayRef.current = null
    }
  }

  useEffect(() => {
    resetResearchState()
  }, [selectedExampleId])

  useEffect(() => () => clearTimers(), [])

  useEffect(() => {
    if (!responseScrollRef.current || !isResponseAutoScroll) {
      return
    }
    responseScrollRef.current.scrollTop = responseScrollRef.current.scrollHeight
  }, [answerLines, isAnswerStreaming, isResponseAutoScroll])

  const reasoningMarkdown = useMemo(() => {
    if (reasoningLines.length === 0) {
      return 'Click the question above to watch how ThoughtAtlas plans its analysis.'
    }
    return reasoningLines.map(line => `- ${line}`).join('\n')
  }, [reasoningLines])

  const answerSegments = useMemo(() => {
    const markdown = selectedExample?.research.answerMarkdown ?? ''
    return markdown
      .split(/\n{2,}/)
      .map(segment => segment.trim())
      .filter(Boolean)
  }, [selectedExample])

  const answerMarkdown = useMemo(() => {
    if (answerLines.length === 0) {
      return ''
    }
    return answerLines.join('\n\n')
  }, [answerLines])

  const playResearchSimulation = () => {
    if (!selectedExample) {
      return
    }
    const chunks = selectedExample.research.reasoningChunks
    clearTimers()
    if (chunks.length === 0) {
      return
    }
    setHasTriggeredResearch(true)
    setIsReasoningStreaming(true)
    setReasoningLines([])
    setIsAnswerStreaming(false)
    setAnswerLines([])
    setIsResponseAutoScroll(true)

    let index = 0
    reasoningTimerRef.current = setInterval(() => {
      setReasoningLines(prev => [...prev, chunks[index]])
      index += 1
      if (index >= chunks.length) {
        if (reasoningTimerRef.current) {
          clearInterval(reasoningTimerRef.current)
          reasoningTimerRef.current = null
        }
        setIsReasoningStreaming(false)
        postReasoningDelayRef.current = setTimeout(() => {
          if (!selectedExample || answerSegments.length === 0) {
            setIsAnswerStreaming(false)
            return
          }
          setIsAnswerStreaming(true)
          let answerIndex = 0
          answerTimerRef.current = setInterval(() => {
            const nextSegment = answerSegments[answerIndex] ?? ''
            setAnswerLines(prev => [...prev, nextSegment])
            answerIndex += 1
            if (answerIndex >= answerSegments.length) {
              if (answerTimerRef.current) {
                clearInterval(answerTimerRef.current)
                answerTimerRef.current = null
              }
              setAnswerLines(answerSegments)
              setIsAnswerStreaming(false)
            }
          }, 150)
        }, 1200)
      }
    }, 400)
  }

  return (
    <section
      id="features"
      className="min-h-screen py-20 px-6 bg-slate-950 text-slate-50 flex items-center"
    >
      <div className="max-w-6xl mx-auto">
        <motion.h3
          className="text-h3 text-slate-50 text-center mb-2"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          From Fog to Finish Line. Experience it Now.
        </motion.h3>
        <p className="text-center text-slate-400 mb-16">
          This is a simplified, interactive preview.
        </p>

        {/* Step 1 */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-24">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h4 className="text-2xl font-semibold text-slate-50 mb-6">
              1. Start with a real-world challenge.
            </h4>
            <p className="text-body-reduced text-slate-400">
              No blank pages. Select one of these common high-stakes goals to
              see how ThoughtAtlas builds your strategic blueprint.
            </p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-slate-900 rounded-2xl p-8 border border-slate-800">
              <div className="bg-slate-950 rounded-lg p-6 border border-slate-800">
                <div className="text-slate-400 text-sm mb-2">
                  What's your challenge?
                </div>
                <article className="relative flex flex-col gap-4">
                  <div className="max-h-72 overflow-y-auto pr-2">
                    <div className="grid gap-2">
                      {STRATEGIC_EXAMPLES.map(example => (
                        <button
                          key={example.id}
                          type="button"
                          onClick={() => handleChallengeSelect(example.id)}
                          className={cn(
                            'text-left text-sm px-3 py-4 rounded-md border transition-colors backdrop-blur-sm w-full text-slate-200',
                            example.id === selectedExampleId
                              ? 'border-blue-500 bg-blue-500/10'
                              : 'border-slate-800 hover:border-slate-700 hover:bg-slate-900/60'
                          )}
                        >
                          <p className="text-xs uppercase tracking-wide text-slate-400">
                            {example.label}
                          </p>
                          <p className="mt-1 text-sm leading-relaxed text-slate-100">
                            {example.persona}
                          </p>
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2 pt-1">
                    <div className="text-xs text-slate-500">
                      Ready to try it?
                    </div>
                    <div className="rounded-md border border-blue-500/30 bg-blue-500/10 p-2 inline-flex">
                      <AuthForm />
                    </div>
                  </div>
                </article>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Step 2 */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-24">
          <motion.div
            className="lg:order-2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h4 className="text-2xl font-semibold text-slate-50 mb-6">
              2. Instantly receive your strategic map.
            </h4>
            <p className="text-body-reduced text-slate-400">
              This isn't a template. It's a research-grade, comprehensive issue
              tree generated in minutes. It's the intellectual net for your
              thinking, ensuring every angle is covered.
            </p>
          </motion.div>
          <motion.div
            className="lg:order-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            ref={issueTreeRef}
          >
            {selectedExample ? (
              <IssueTreeVisualization
                key={`${selectedExampleId}-${treeResetSeed}`}
                issueTree={selectedExample.issueTree}
              />
            ) : null}
          </motion.div>
        </div>

        {/* Step 3 */}
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h4 className="text-2xl font-semibold text-slate-50 mb-6">
              3. Get a head start with focused research.
            </h4>
            <p className="text-body-reduced text-slate-400">
              Stop random prompting. Every question is a hook. With one click,
              you cast a targeted research net to capture the essential insights
              needed to move forward.
            </p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-slate-900 rounded-2xl p-8 border border-slate-800">
              <div className="bg-slate-950 rounded-lg p-6 border border-slate-800">
                <div className="mb-4 flex items-center justify-between">
                  <h5 className="text-slate-50 font-semibold">
                    Example Insights
                  </h5>
                  {!hasTriggeredResearch ? (
                    <button
                      type="button"
                      onClick={playResearchSimulation}
                      className="inline-flex items-center gap-1 rounded-full border border-blue-500/40 bg-blue-500/10 px-3 py-1 text-[11px] font-medium text-blue-100 transition hover:bg-blue-500/20"
                    >
                      <Sparkles className="h-3 w-3" />
                      Click to simulate
                    </button>
                  ) : null}
                </div>
                <div className="space-y-3">
                  <button
                    onClick={playResearchSimulation}
                    className={cn(
                      'group w-full text-left px-4 py-4 rounded-md border border-blue-500/40 bg-blue-500/10 hover:bg-blue-500/20 text-slate-100 transition-colors shadow-[0_0_0_0_rgba(37,99,235,0.45)]',
                      hasTriggeredResearch
                        ? 'animate-none'
                        : 'animate-[pulse_2.4s_ease-in-out_infinite]'
                    )}
                  >
                    <div className="space-y-1">
                      <p className="text-xs uppercase tracking-wide text-blue-300">
                        {selectedExample?.research.focusArea}
                        {selectedExample?.research.subArea
                          ? ` > ${selectedExample.research.subArea}`
                          : ''}
                      </p>
                      <p className="text-sm font-semibold">
                        {selectedExample?.research.question}
                      </p>
                      {!hasTriggeredResearch ? (
                        <p className="text-[11px] text-blue-200/80">
                          Click to simulate the research walkthrough
                        </p>
                      ) : null}
                    </div>
                  </button>
                  {hasTriggeredResearch ? (
                    <>
                      <Reasoning
                        isStreaming={isReasoningStreaming}
                        defaultOpen={false}
                        variant="subtle"
                      >
                        <ReasoningTrigger />
                        <ReasoningContent>{reasoningMarkdown}</ReasoningContent>
                      </Reasoning>
                      <div
                        className="rounded-md border border-slate-800 p-3 max-h-80 overflow-y-auto text-xs"
                        ref={responseScrollRef}
                        onScroll={event => {
                          const target = event.currentTarget
                          const threshold = 12
                          const isAtBottom =
                            target.scrollTop + target.clientHeight >=
                            target.scrollHeight - threshold
                          setIsResponseAutoScroll(isAtBottom)
                        }}
                      >
                        <Response className="text-xs text-slate-300 whitespace-pre-line">
                          {answerMarkdown || ' '}
                        </Response>
                      </div>
                      <div className="flex items-center justify-end gap-2 text-[11px] text-slate-400">
                        <button
                          type="button"
                          className="rounded border border-slate-700 bg-slate-900/60 px-2 py-1 text-[11px] font-medium text-slate-200 hover:bg-slate-800"
                          onClick={() => {
                            setIsResponseAutoScroll(false)
                            responseScrollRef.current?.scrollTo({
                              top: 0,
                              behavior: 'smooth',
                            })
                          }}
                        >
                          Jump to top
                        </button>
                      </div>
                      <div className="pt-4 border-t border-slate-800">
                        <div className="text-xs text-slate-500 mb-2">
                          Ready to try it?
                        </div>
                        <div className="rounded-md border border-blue-500/30 bg-blue-500/10 p-2 inline-flex">
                          <AuthForm />
                        </div>
                      </div>
                    </>
                  ) : null}
                </div>
                <div className="mt-4 pt-4 border-t border-slate-800">
                  <div className="text-xs text-slate-400">
                    Sources vary by topic; examples only
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
