'use client'

import { motion } from 'framer-motion'
import AuthForm from '@/app/(landing)/components/AuthForm'

export default function FinalCTASection() {
  return (
    <section className="py-20 px-6 bg-slate-950 text-slate-50">
      <div className="max-w-4xl mx-auto text-center">
        <motion.h2
          className="text-h2 text-slate-50 mb-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          Clarity is probably just a few clicks away!
        </motion.h2>

        <motion.p
          className="text-body text-slate-400 mb-12 max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          Start Your Next Great Work with unprecedent efficiency.
        </motion.p>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <AuthForm />
        </motion.div>
      </div>
    </section>
  )
}
