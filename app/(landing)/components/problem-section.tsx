'use client'

import { motion } from 'framer-motion'
import { Clock, Eye, Users } from 'lucide-react'

export default function ProblemSection() {
  return (
    <section className="py-20 px-6 bg-slate-950 text-slate-50">
      <div className="max-w-6xl mx-auto">
        <motion.h3
          className="text-h3 text-slate-50 text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          Clarity is Hard Won.
        </motion.h3>

        {/* Problem 1 */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-slate-900 rounded-2xl p-8 border border-slate-800">
              <img
                src="/messy-whiteboard-with-scattered-sticky-notes-and-u.png"
                alt="Messy whiteboard"
                className="w-full h-64 object-cover rounded-lg mb-4"
              />
              <p className="text-slate-400 text-center">Messy Whiteboard</p>
            </div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex flex-col">
              <div className="w-16 h-16 bg-slate-800/30 rounded-2xl flex items-center justify-center mb-6">
                <Clock className="w-8 h-8 text-slate-100" />
              </div>
              <h4 className="text-2xl font-semibold text-slate-50 mb-4">
                Wasted Weeks, Not Days
              </h4>
              <p className="text-body text-slate-400">
                Individuals lose momentum in random searches and unstructured
                notes.
              </p>
            </div>
          </motion.div>
        </div>

        {/* Problem 2 (reverse) */}
        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <motion.div
            className="lg:order-2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-slate-900 rounded-2xl p-8 border border-slate-800">
              <img
                src="/unstructured-document-with-scattered-text-and-no-c.png"
                alt="Unstructured document"
                className="w-full h-64 object-cover rounded-lg mb-4"
              />
              <p className="text-slate-400 text-center">
                Unstructured Document
              </p>
            </div>
          </motion.div>
          <motion.div
            className="lg:order-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex flex-col">
              <div className="w-16 h-16 bg-slate-800/30 rounded-2xl flex items-center justify-center mb-6">
                <Eye className="w-8 h-8 text-slate-100" />
              </div>
              <h4 className="text-2xl font-semibold text-slate-50 mb-4">
                Critical Risks Overlooked
              </h4>
              <p className="text-body text-slate-400">
                Without a structured process, fatal flaws in strategy are often
                missed until it's too late.
              </p>
            </div>
          </motion.div>
        </div>

        {/* Problem 3 */}
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-slate-900 rounded-2xl p-8 border border-slate-800">
              <img
                src="/complex-spreadsheet-with-too-many-columns-and-conf.png"
                alt="Complex spreadsheet"
                className="w-full h-64 object-cover rounded-lg mb-4"
              />
              <p className="text-slate-400 text-center">Complex Spreadsheet</p>
            </div>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="flex flex-col">
              <div className="w-16 h-16 bg-slate-800/30 rounded-2xl flex items-center justify-center mb-6">
                <Users className="w-8 h-8 text-slate-100" />
              </div>
              <h4 className="text-2xl font-semibold text-slate-50 mb-4">
                Team Misalignment
              </h4>
              <p className="text-body text-slate-400">
                When there's no shared map, everyone on the team is navigating
                towards a different destination.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
