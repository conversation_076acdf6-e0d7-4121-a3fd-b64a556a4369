'use client'

import { motion } from 'framer-motion'
import AuthForm from '@/app/(landing)/components/AuthForm'

export default function Header() {
  return (
    <motion.header
      className="fixed top-0 left-0 right-0 z-50 bg-slate-950/80 backdrop-blur-sm border-b border-slate-800"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="max-w-7xl mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-slate-800 rounded-lg flex items-center justify-center">
              <span className="text-slate-50 font-bold text-sm">TA</span>
            </div>
            <span className="text-xl font-semibold text-slate-50">
              Thought Atlas
            </span>
          </div>

          <nav className="hidden md:flex items-center">
            <AuthForm />
          </nav>

          <div className="md:hidden">
            <AuthForm />
          </div>
        </div>
      </div>
    </motion.header>
  )
}
