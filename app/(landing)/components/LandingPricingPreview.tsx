'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  getAllPlanFeatures,
  type PricingPlan,
  type PricingPlanWithDynamicPricing,
  getDisplayPrice,
} from '@/app/subscription/components/ModernPricingConfig'
import { useModernSubscription } from '@/app/subscription/hooks/useModernSubscription'

// Landing page pricing preview: smaller cards, no subscribe buttons
const LandingPricingPreview: React.FC = () => {
  const { pricingPlans } = useModernSubscription()
  const plans: (PricingPlan | PricingPlanWithDynamicPricing)[] =
    pricingPlans.length > 0
      ? pricingPlans
      : (getAllPlanFeatures() as PricingPlan[])

  return (
    <section className="py-12 px-6 bg-slate-950 text-slate-50">
      <div className="max-w-5xl mx-auto">
        <h3 className="text-h3 text-center mb-8"><PERSON>ricing</h3>
        <p className="text-center text-slate-400 mb-10">
          Aligned with subscription page. No sign-up here.
        </p>
        <div className="grid md:grid-cols-2 gap-6">
          {plans.map(plan => (
            <Card key={plan.id} className="border-slate-800 bg-slate-900">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl text-slate-50">
                    {plan.name}
                  </CardTitle>
                  {plan.popular ? (
                    <Badge className="bg-purple-600 text-white">Popular</Badge>
                  ) : null}
                </div>
                <p className="text-sm text-slate-400">{plan.description}</p>
                <div className="mt-2 text-2xl font-semibold text-slate-50">
                  {getDisplayPrice(plan as PricingPlanWithDynamicPricing)}
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-slate-300">
                  {plan.features.slice(0, 6).map((f, idx) => (
                    <li key={idx} className="flex items-start gap-2">
                      <span
                        className={`mt-1 h-2 w-2 rounded-full ${f.included ? 'bg-green-500' : 'bg-slate-600'}`}
                      ></span>
                      <span
                        className={
                          f.included ? '' : 'line-through text-slate-500'
                        }
                      >
                        {f.name}
                      </span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export default LandingPricingPreview
