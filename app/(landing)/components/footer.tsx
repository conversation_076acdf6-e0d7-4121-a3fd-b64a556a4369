'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'

export default function Footer() {
  return (
    <motion.footer
      className="py-16 px-6 border-t border-slate-800 bg-slate-950 text-slate-50"
      initial={{ opacity: 0 }}
      whileInView={{ opacity: 1 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
    >
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center">
          <div>
            <p className="text-slate-400">© 2025 Thought Atlas</p>
          </div>

          <div className="flex space-x-8">
            <Link
              href="/privacy"
              className="text-slate-400 hover:text-slate-50 transition-colors"
            >
              Privacy Policy
            </Link>
            <Link
              href="/tos"
              className="text-slate-400 hover:text-slate-50 transition-colors"
            >
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </motion.footer>
  )
}
