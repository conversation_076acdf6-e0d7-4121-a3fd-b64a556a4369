import './globals.css'
import '@/styles/prosemirror.css'
import type { Metadata, Viewport } from 'next'

import AuthContext from './context/AuthContext'
import ToasterContext from './context/ToasterContext'
import { Inter } from 'next/font/google'
import { Analytics } from '@vercel/analytics/react'
import Providers from './providers'

const inter = Inter({ subsets: ['latin'] })

// Compute canonical site URL from env with safe fallbacks.
const siteUrl =
  process.env.NEXT_PUBLIC_SITE_URL ||
  (process.env.VERCEL_URL
    ? `https://${process.env.VERCEL_URL}`
    : 'http://localhost:3000')

export const metadata: Metadata = {
  metadataBase: new URL(siteUrl),
  applicationName: 'Thought Atlas',
  generator: 'Next.js',
  title: 'Thought Atlas - AI-Powered Structured Thinking',
  description:
    'Transform complex problems into clear, actionable insights with Thought Atlas. Build interactive thinking graphs that help you explore every angle before making decisions. Perfect for deep thinkers, consultants, and knowledge workers.',
  keywords: [
    'ThinkGraph',
    'structured thinking',
    'AI-powered decision making',
    'interactive thinking graphs',
    'problem exploration tool',
    'decision support system',
    'complex problem solving',
    'AI-assisted brainstorming',
    'strategic planning tool',
    'thought organization',
    'knowledge worker productivity',
    'deep thinking tool',
    'comprehensive analysis',
    'decision tree builder',
    'AI for consultants',
    'management decision support',
    'systematic thinking',
    'problem decomposition',
    'insight generation',
    'ChatGPT enhancement',
    'AI productivity tool',
    'INTP thinking tool',
    'overthinking solution',
  ],
  alternates: { canonical: '/' },
  openGraph: {
    type: 'website',
    url: siteUrl,
    siteName: 'Thought Atlas',
    title: 'Thought Atlas - AI-Powered Structured Thinking',
    description:
      'Transform complex problems into clear, actionable insights with Thought Atlas. Build interactive thinking graphs that help you explore every angle before making decisions.',
    images: [
      {
        url: '/images/landing_v3_notebook_screenshot.png',
      },
    ],
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Thought Atlas - AI-Powered Structured Thinking',
    description:
      'Transform complex problems into clear, actionable insights with Thought Atlas.',
    images: ['/images/landing_v3_notebook_screenshot.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: '/images/thinkgraph-favicon.svg',
    shortcut: '/images/thinkgraph-favicon.svg',
    apple: '/images/thinkgraph-icon.svg',
  },
}

export const viewport: Viewport = {
  themeColor: '#0f172a',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="h-full w-full">
      <head>
        {/* Structured data for Organization and WebSite */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'Organization',
              name: 'Thought Atlas',
              url: siteUrl,
              logo: `${siteUrl}/images/thinkgraph-logo.svg`,
            }),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'WebSite',
              name: 'Thought Atlas',
              url: siteUrl,
              potentialAction: {
                '@type': 'SearchAction',
                target: `${siteUrl}/search?q={search_term_string}`,
                'query-input': 'required name=search_term_string',
              },
            }),
          }}
        />
      </head>
      <body
        className={`${inter.className} h-full w-full flex flex-col`}
        suppressHydrationWarning={true}
      >
        <AuthContext>
          <Analytics />
          <ToasterContext />
          <Providers>{children}</Providers>
        </AuthContext>
      </body>
    </html>
  )
}
