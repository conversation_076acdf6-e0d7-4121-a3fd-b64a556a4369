'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'react-hot-toast'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { shallow } from 'zustand/shallow'
import { TreeNode } from '@/app/types'
import {
  getExistingQuestions,
  getExistingSubcategories,
  getOriginalContextFromTree,
  treeNodeToMarkdown,
} from '@/app/(conv)/dragTree/[dragTreeId]/utils/treeHelpers'
import {
  DragtreeGenerateSimilarQuestionsRequestType,
  DragtreeGenerateSimilarCategoriesRequestType,
} from '@/app/types/api'
import {
  DEFAULT_CONVERSATION_ID,
  TOAST_MESSAGES,
  TOAST_DURATION_MS,
} from '@/app/(conv)/dragTree/[dragTreeId]/components/HierarchicalOutline/constants'
import type {
  LoadingStateType,
  NodeGenerationHandlersType,
} from '@/app/(conv)/dragTree/[dragTreeId]/types'

type UseNodeGenerationProps = {
  frontendTreeStructure: TreeNode | null
  onLoadingChange?: (isLoading: boolean) => void
  onGeneratingNodeChange?: (nodeId: string) => void
}

type UseNodeGenerationReturn = {
  loadingState: LoadingStateType
  handlers: NodeGenerationHandlersType
}

/**
 * Custom hook to handle AI-powered node generation (similar questions and categories)
 * Manages loading states, API calls, and error handling
 */
export const useNodeGeneration = ({
  frontendTreeStructure,
  onLoadingChange,
  onGeneratingNodeChange,
}: UseNodeGenerationProps): UseNodeGenerationReturn => {
  const { data: session } = useSession()
  const { findNodeById, dragTreeId } = useDragTreeStore(
    state => ({
      findNodeById: state.findNodeById,
      dragTreeId: state.dragTreeId,
    }),
    shallow
  )

  // Simple loading state to avoid conflicts with useChat loading states
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [currentNodeId, setCurrentNodeId] = useState<string>('')

  /**
   * Set loading state and notify parent components
   */
  const setLoadingState = (loading: boolean, nodeId: string = '') => {
    console.log(`🔄 Setting loading state: ${loading}, nodeId: ${nodeId}`)
    setIsLoading(loading)
    setCurrentNodeId(nodeId)
    onLoadingChange?.(loading)
    onGeneratingNodeChange?.(nodeId)
  }

  /**
   * Validate session and node before AI generation
   * Enhanced with better error handling for post-refresh scenarios
   */
  const validateGenerationRequest = (nodeId: string): TreeNode | null => {
    // Check if session is still loading (common after page refresh)
    if (!session) {
      console.warn('⚠️ Session not loaded yet, retrying...')
      toast.error('Session loading... Please try again in a moment.')
      return null
    }

    if (!session.user?.id) {
      console.error('❌ No user ID in session')
      toast.error(TOAST_MESSAGES.AUTH_REQUIRED)
      return null
    }

    if (!frontendTreeStructure) {
      console.warn('⚠️ Tree data not loaded yet')
      toast.error('Tree data loading... Please try again in a moment.')
      return null
    }

    const targetNode = findNodeById(nodeId)
    if (!targetNode) {
      console.error('❌ Target node not found:', nodeId)
      toast.error('Node not found. Please refresh and try again.')
      return null
    }

    if (targetNode.type !== 'category') {
      console.error('❌ Invalid node type:', targetNode.type)
      toast.error(TOAST_MESSAGES.INVALID_CATEGORY)
      return null
    }

    console.log('✅ Validation passed for node:', targetNode.label)
    return targetNode
  }

  // Unused success message creator - commented out to fix TypeScript error
  // const createSuccessMessage = (
  //   action: string,
  //   targetLabel: string,
  //   contextCount: number,
  //   guidance: string
  // ): string => {
  //   return (
  //     `✅ Generated ${action} for "${targetLabel}"\n` +
  //     `Found context with ${contextCount} items\n` +
  //     `Guidance applied: "${guidance}"`
  //   )
  // }

  /**
   * Handle generation of similar questions for a category
   * Enhanced with better error handling and race condition prevention
   */
  const handleGenerateSimilarQuestions = async (
    nodeId: string,
    _guidance: string = ''
  ): Promise<void> => {
    // Prevent rapid successive calls (debounce)
    if (isLoading) {
      console.warn('⚠️ Already processing, ignoring duplicate request')
      return
    }

    const targetNode = validateGenerationRequest(nodeId)
    if (!targetNode) return

    // Validate that we have dragTreeId for database refresh later
    if (!dragTreeId) {
      console.error('❌ No dragTreeId available for database refresh')
      toast.error('Tree data not loaded. Please refresh and try again.')
      return
    }

    console.log('🚀 Starting similar questions generation for node:', nodeId)

    // Small delay to ensure any UI state has settled after page refresh
    await new Promise(resolve => setTimeout(resolve, 100))

    setLoadingState(true, nodeId)

    try {
      // Gather context data
      console.log('Getting existing questions...')
      const existingQuestions = getExistingQuestions(targetNode)
      console.log('Got existing questions:', existingQuestions.length)

      console.log('Getting original context...')
      const originalContext = getOriginalContextFromTree(frontendTreeStructure!)
      console.log('Got original context, length:', originalContext.length)

      const requestData: DragtreeGenerateSimilarQuestionsRequestType = {
        conversationId: DEFAULT_CONVERSATION_ID,
        userId: session!.user!.id!,
        nodeId: nodeId,
        categoryLabel: targetNode.label,
        existingQuestions,
        originalContext,
      }

      // Show loading feedback
      toast.loading(TOAST_MESSAGES.LOADING_QUESTIONS, {
        duration: TOAST_DURATION_MS,
      })

      console.log('🧪 Making API call for similar questions...')
      console.log('🧪 Request data:', requestData)
      console.log('🧪 dragTreeId available for refresh:', dragTreeId)

      // Make API call
      const response = await fetch('/api/dragtree/generate_similar_questions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      })

      console.log('🧪 Response status:', response.status, response.statusText)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('❌ API call failed:', response.status, errorText)
        throw new Error(`API call failed: ${response.status} - ${errorText}`)
      }

      const responseData = await response.json()
      console.log('✅ [Similar Questions] API response:', responseData)

      if (responseData.success && responseData.shouldRefresh) {
        console.log(
          '📝 [Similar Questions] Database update completed, refreshing frontend tree'
        )

        // Trigger database refresh to get latest data
        console.log('🔄 Calling loadFromDatabase...')
        const { loadFromDatabase } = useDragTreeStore.getState()
        try {
          await loadFromDatabase()
          console.log('✅ Database refresh completed successfully')
        } catch (dbError) {
          console.error('❌ Database refresh failed:', dbError)
          throw new Error('Failed to refresh data from database')
        }

        toast.success(
          responseData.message || 'Similar questions generated successfully'
        )
      } else {
        throw new Error('Invalid response format from API')
      }

      console.log('✅ Questions generation completed successfully')
    } catch (error) {
      console.error('❌ Error in questions generation:', error)
      toast.error(TOAST_MESSAGES.QUESTIONS_FAILED)
    } finally {
      setLoadingState(false)
      console.log('✨ Questions generation cleanup completed')
    }
  }

  /**
   * Handle generation of similar categories for a category
   * Enhanced with better error handling and race condition prevention
   */
  const handleGenerateSimilarCategories = async (
    nodeId: string,
    _guidance: string = ''
  ): Promise<void> => {
    // Prevent rapid successive calls (debounce)
    if (isLoading) {
      console.warn('⚠️ Already processing, ignoring duplicate request')
      return
    }

    const targetNode = validateGenerationRequest(nodeId)
    if (!targetNode) return

    // Validate that we have dragTreeId for database refresh later
    if (!dragTreeId) {
      console.error('❌ No dragTreeId available for database refresh')
      toast.error('Tree data not loaded. Please refresh and try again.')
      return
    }

    console.log('🚀 Starting similar categories generation for node:', nodeId)

    // Small delay to ensure any UI state has settled after page refresh
    await new Promise(resolve => setTimeout(resolve, 100))

    setLoadingState(true, nodeId)

    try {
      // Gather context data
      console.log('Getting existing subcategories...')
      const existingSubcategories = getExistingSubcategories(targetNode)
      console.log('Got existing subcategories:', existingSubcategories.length)

      console.log('Getting original context...')
      const originalContext = getOriginalContextFromTree(frontendTreeStructure!)
      console.log('Got original context, length:', originalContext.length)

      console.log('Converting tree to markdown...')
      const treeContext = treeNodeToMarkdown(frontendTreeStructure!)
      console.log('Got tree markdown, length:', treeContext.length)

      const requestData: DragtreeGenerateSimilarCategoriesRequestType = {
        conversationId: DEFAULT_CONVERSATION_ID,
        userId: session!.user!.id!,
        nodeId: nodeId,
        parentCategoryLabel: targetNode.label,
        existingSubcategories,
        originalContext,
        treeContext,
      }

      // Show loading feedback
      toast.loading(TOAST_MESSAGES.LOADING_CATEGORIES, {
        duration: TOAST_DURATION_MS,
      })

      console.log('🧪 Making API call for similar categories...')

      // Make API call
      const response = await fetch(
        '/api/dragtree/generate_similar_categories',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(requestData),
        }
      )

      if (!response.ok) {
        throw new Error(`API call failed: ${response.status}`)
      }

      const responseData = await response.json()
      console.log('✅ [Similar Categories] API response:', responseData)

      if (responseData.success && responseData.shouldRefresh) {
        console.log(
          '📁 [Similar Categories] Database update completed, refreshing frontend tree'
        )

        // Trigger database refresh to get latest data
        const { loadFromDatabase } = useDragTreeStore.getState()
        await loadFromDatabase()

        toast.success(
          responseData.message || 'Similar categories generated successfully'
        )
      } else {
        throw new Error('Invalid response format from API')
      }

      console.log('✅ Categories generation completed successfully')
    } catch (error) {
      console.error('❌ Error in categories generation:', error)
      toast.error(TOAST_MESSAGES.CATEGORIES_FAILED)
    } finally {
      setLoadingState(false)
      console.log('✨ Categories generation cleanup completed')
    }
  }

  return {
    loadingState: {
      isLoading,
      currentNodeId,
    },
    handlers: {
      handleGenerateSimilarQuestions,
      handleGenerateSimilarCategories,
    },
  }
}
