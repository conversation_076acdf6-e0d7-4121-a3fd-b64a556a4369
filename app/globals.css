@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --ring: 215 20.2% 65.1%;

    --radius: 0.5rem;

    /* Assistant UI theme variables */
    --aui-primary: var(--primary);
    --aui-primary-foreground: var(--primary-foreground);
    --aui-secondary: var(--secondary);
    --aui-secondary-foreground: var(--secondary-foreground);
    --aui-muted: var(--muted);
    --aui-muted-foreground: var(--muted-foreground);
    --aui-accent: var(--accent);
    --aui-accent-foreground: var(--accent-foreground);
    --aui-destructive: var(--destructive);
    --aui-destructive-foreground: var(--destructive-foreground);
    --aui-border: var(--border);
    --aui-input: var(--input);
    --aui-ring: var(--ring);
    --aui-background: var(--background);
    --aui-foreground: var(--foreground);

    --clarify-highlight-default: #ffffff;
    --clarify-highlight-purple: #f6f3f8;
    --clarify-highlight-red: #fdebeb;
    --clarify-highlight-yellow: #fbf4a2;
    --clarify-highlight-blue: #c1ecf9;
    --clarify-highlight-green: #acf79f;
    --clarify-highlight-orange: #faebdd;
    --clarify-highlight-pink: #faf1f5;
    --clarify-highlight-gray: #f1f1ef;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 85.7% 97.3%;

    --ring: 217.2 32.6% 17.5%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Chat V2 Component Styles - Compact design */
  .chat-bubble {
    @apply relative inline-block max-w-[85%] rounded-xl border-2 px-3 py-2 text-xs leading-tight shadow-md transition-all duration-200;
  }
  .chat-bubble:hover {
    @apply shadow-xl;
  }
  /* Professional user message bubble - Light and pleasant colors */
  .user-bubble {
    @apply ml-auto mr-3 border-emerald-400 bg-emerald-400 text-white hover:bg-emerald-500;
  }
  /* Professional assistant message bubble */
  .assistant-bubble {
    @apply ml-3 mr-auto border-gray-300 bg-gray-50 text-gray-800 hover:border-gray-400 hover:bg-gray-100;
  }

  /* Composer Input Styles - Optimized without internal button */
  .composer-input {
    @apply w-full resize-y overflow-auto max-h-48 min-h-[42px] rounded-xl border-2 bg-white px-3 py-2 text-sm leading-normal text-gray-900 placeholder-gray-500 shadow-sm transition-all duration-200 hover:border-gray-300 hover:shadow-md focus:border-blue-500 focus:shadow-lg focus:ring-2 focus:ring-blue-500/20;
  }
  .composer-input-error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500/20;
  }

  /* Send Button Styles */
  .send-button {
    @apply absolute right-2 bottom-2 flex h-10 w-10 items-center justify-center rounded-xl border-0 bg-gray-900 text-white shadow-lg transition-all duration-200 hover:scale-105 hover:bg-black hover:shadow-xl active:scale-95 disabled:cursor-not-allowed disabled:bg-gray-300 disabled:text-gray-500;
  }

  /* Copy Button and Action Button Styles - Professional */
  .copy-button {
    @apply absolute bottom-1 right-1 text-xs text-gray-500 hover:text-gray-700 opacity-0 group-hover:opacity-100 transition-all duration-200 bg-white/95 backdrop-blur-sm rounded-md p-1.5 shadow-sm border border-gray-200 hover:border-gray-300 hover:shadow-md;
  }
  .expand-button {
    @apply mt-2 flex items-center gap-1 text-xs text-gray-700 hover:text-gray-900 bg-white hover:bg-blue-50 px-2 py-1 rounded-md transition-all duration-200 border border-gray-300 hover:border-blue-300 shadow-sm hover:shadow-md;
  }

  /* Smooth animations for message expansion */
  .message-content {
    @apply transition-all duration-300 ease-in-out;
  }

  .message-content.collapsed {
    @apply overflow-hidden;
    max-height: 15rem; /* 240px */
  }

  .message-content.expanded {
    @apply overflow-visible;
    max-height: none;
  }

  .clickable-message {
    @apply cursor-pointer hover:bg-gray-50 transition-colors duration-200;
  }

  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-4px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

@layer utilities {
  /* ThoughtAtlas typography utilities */
  .text-h1 {
    font-size: 72px;
    font-weight: 700;
    line-height: 1.1;
  }
  .text-h2 {
    font-size: 48px;
    font-weight: 600;
    line-height: 1.2;
  }
  .text-h3 {
    font-size: 36px;
    font-weight: 600;
    line-height: 1.3;
  }
  .text-body {
    font-size: 18px;
    font-weight: 400;
    line-height: 1.6;
  }
  .text-body-reduced {
    font-size: 17px;
    font-weight: 400;
    line-height: 1.7;
  }

  @media (max-width: 768px) {
    .text-h1 {
      font-size: 48px;
    }
    .text-h2 {
      font-size: 36px;
    }
    .text-h3 {
      font-size: 28px;
    }
    .text-body-reduced {
      font-size: 16px;
    }
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

/* Ensure global popups render above all content */
body {
  position: relative;
}

/* Source citation popup z-index should be higher than modals */
.source-citation-popup {
  z-index: 9999 !important;
}
