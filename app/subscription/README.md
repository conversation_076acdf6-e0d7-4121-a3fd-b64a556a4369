# Dynamic Subscription Pricing System

This directory contains the implementation of a dynamic pricing system that fetches pricing data from the Supabase database instead of using hardcoded values.

## Overview

The system separates plan features from pricing data, allowing features to be defined in code while prices are fetched dynamically from the database. This approach provides flexibility for price changes and rollouts without requiring code deployments.

## Architecture

### Core Components

1. **ModernPricingConfig.ts** - Configuration file that defines plan features and pricing utilities
2. **ModernSubscriptionPage.tsx** - Main subscription page component
3. **ModernPricingCard.tsx** - Individual pricing plan card component
4. **useModernSubscription.ts** - Hook that manages subscription state and dynamic pricing

### API Endpoints

- `/api/subscription/prices` - Fetches active pricing data from the database

## Key Features

### Dynamic Pricing

- Prices are fetched from the `products` and `prices` Supabase tables
- Supports both monthly and yearly billing intervals
- Automatic fallback to legacy pricing if dynamic pricing fails

### Tier Logic

The following tiers are treated as "FREE" for subscription page display:

- `FREE` - Default tier for new users
- `VIEWER` - Read-only access
- `DUMMY` - Testing tier
- `GUEST` - Friends & testers (has PRO features but shows as FREE for upgrade purposes)

All these users see upgrade options to Pro.

### Plan Configuration

#### FREE Plan Features

- 3 active drag trees
- Basic drag tree functionality
- Limited AI chat & generation
- Community support
- Basic analytics

#### PRO Plan Features

- Unlimited active drag trees
- Advanced drag tree functionality
- Quick research capabilities
- Advanced AI chat & generation
- Priority support
- Advanced analytics & insights
- Export & sharing capabilities
- Custom integrations
- Early access to new features

## Database Schema

The system expects the following database structure:

### Products Table

```sql
products (
  id: string (primary key)
  active: boolean
  name: string
  description: string
  ...
)
```

### Prices Table

```sql
prices (
  id: string (primary key)
  product_id: string (foreign key)
  active: boolean
  unit_amount: bigint (in cents)
  currency: string
  interval: 'month' | 'year'
  ...
)
```

## Usage

### Fetching Dynamic Pricing

```typescript
const { pricingPlans, pricingError, dynamicPricing } = useModernSubscription()
```

### Displaying Prices

```typescript
import { getDisplayPrice } from './ModernPricingConfig'

const price = getDisplayPrice(plan, isYearly)
```

### Checkout Flow

The system automatically selects the correct price ID based on the billing interval:

```typescript
const handleCheckout = (planId: string, isYearly: boolean) => {
  // Uses dynamic pricing if available, falls back to API call
}
```

## Error Handling

- Graceful fallback to legacy pricing if dynamic pricing fails
- User-friendly error messages for pricing data issues
- Maintains backward compatibility with existing pricing structure

## Testing

The system includes comprehensive tests that validate:

- Dynamic pricing data fetching
- Tier logic implementation
- Fallback mechanisms
- Component rendering with dynamic data

Run tests with:

```bash
npm run test
npm run typecheck
npm run build
```

## Future Enhancements

- Support for multiple products
- Promotional pricing
- Regional pricing
- A/B testing for pricing strategies
