/**
 * Modern Pricing Configuration
 * Separates plan features from dynamic pricing data
 * Features are defined here, prices are fetched from database
 */

export type PricingTier = 'FREE' | 'PRO'

export type PricingFeature = {
  name: string
  included: boolean
  description?: string
}

export type PricingPlan = {
  id: PricingTier
  name: string
  description: string
  features: PricingFeature[]
  popular?: boolean
  ctaText: string
  ctaVariant: 'default' | 'outline'
}

export type DynamicPricing = {
  monthly: {
    amount: number
    priceId: string
  }
  yearly?: {
    amount: number
    priceId: string
  }
  currency: string
}

export type PricingPlanWithDynamicPricing = PricingPlan & {
  pricing?: DynamicPricing
}

// Plan feature configurations (static) - prices are fetched dynamically
export const PLAN_FEATURES_CONFIG: Record<PricingTier, PricingPlan> = {
  FREE: {
    id: 'FREE',
    name: 'Free Plan',
    description: 'Perfect for getting started with basic features',
    features: [
      { name: '3 active drag trees', included: true },
      { name: 'Basic drag tree functionality', included: true },
      { name: 'Limited Quick research capabilities', included: true },
      { name: 'Limited AI chat & generation', included: true },
    ],
    ctaText: 'Get Started Free',
    ctaVariant: 'outline',
  },
  PRO: {
    id: 'PRO',
    name: 'Pro Plan',
    description: 'Unlock the full potential with advanced features',
    features: [
      { name: 'Everything in Free plan', included: true },
      { name: 'Advanced Quick research capabilities', included: true },
      { name: 'Advanced AI chat & generation', included: true },
      { name: 'Priority support', included: true },
      { name: 'Export & sharing capabilities', included: true },
      { name: 'Early access to new features', included: true },
    ],
    popular: true,
    ctaText: 'Upgrade to Pro',
    ctaVariant: 'default',
  },
}

// Legacy config for backward compatibility - includes hardcoded prices
export const PRICING_CONFIG: Record<
  PricingTier,
  PricingPlan & {
    price: { monthly: number; yearly?: number; currency: string }
  }
> = {
  FREE: {
    ...PLAN_FEATURES_CONFIG.FREE,
    price: {
      monthly: 0,
      currency: 'USD',
    },
  },
  PRO: {
    ...PLAN_FEATURES_CONFIG.PRO,
    price: {
      monthly: 1,
      yearly: 10,
      currency: 'USD',
    },
  },
}

// New functions for dynamic pricing
export const getPlanFeatures = (tier: PricingTier): PricingPlan => {
  return PLAN_FEATURES_CONFIG[tier]
}

export const getAllPlanFeatures = (): PricingPlan[] => {
  return Object.values(PLAN_FEATURES_CONFIG)
}

export const createPlanWithDynamicPricing = (
  tier: PricingTier,
  pricing: DynamicPricing
): PricingPlanWithDynamicPricing => {
  return {
    ...PLAN_FEATURES_CONFIG[tier],
    pricing,
  }
}

// Legacy functions for backward compatibility
export const getPricingPlan = (tier: PricingTier): PricingPlan => {
  return PRICING_CONFIG[tier]
}

export const getAllPricingPlans = (): PricingPlan[] => {
  return Object.values(PRICING_CONFIG)
}

export const formatPrice = (
  amount: number,
  currency: string = 'USD'
): string => {
  if (amount === 0) return 'Free'

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Format price from cents to dollars
export const formatPriceFromCents = (
  amountInCents: number,
  currency: string = 'USD'
): string => {
  if (amountInCents === 0) return 'Free'

  const amountInDollars = amountInCents / 100
  return formatPrice(amountInDollars, currency)
}

// Get display price for a plan with dynamic pricing
export const getDisplayPrice = (
  plan: PricingPlanWithDynamicPricing,
  isYearly: boolean = false
): string => {
  if (plan.id === 'FREE') return 'Free'

  if (!plan.pricing) {
    // Fallback to legacy pricing if dynamic pricing not available
    const legacyPlan = PRICING_CONFIG[plan.id]
    if ('price' in legacyPlan) {
      const price =
        isYearly && legacyPlan.price.yearly
          ? legacyPlan.price.yearly
          : legacyPlan.price.monthly
      return formatPrice(price, legacyPlan.price.currency)
    }
    return 'Price unavailable'
  }

  const priceData =
    isYearly && plan.pricing.yearly ? plan.pricing.yearly : plan.pricing.monthly

  return formatPriceFromCents(priceData.amount, plan.pricing.currency)
}

// Get price ID for checkout
export const getPriceId = (
  plan: PricingPlanWithDynamicPricing,
  isYearly: boolean = false
): string | null => {
  if (plan.id === 'FREE') return null

  if (!plan.pricing) return null

  const priceData =
    isYearly && plan.pricing.yearly ? plan.pricing.yearly : plan.pricing.monthly

  return priceData.priceId
}
