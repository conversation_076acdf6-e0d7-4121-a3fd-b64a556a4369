'use client'

import React from 'react'
import { ModernSubscriptionHeader } from './ModernSubscriptionHeader'
import { ModernPricingCard } from './ModernPricingCard'
import { BillingToggle } from './BillingToggle'
import { ContactInfo } from './ContactInfo'
import { PricingTier } from './ModernPricingConfig'
import { useModernSubscription } from '../hooks/useModernSubscription'

export const ModernSubscriptionPage: React.FC = () => {
  const {
    session,
    currentTier,
    isPaidUser,
    isLoading,
    handleStripeCheckout,
    pricingPlans,
    pricingError,
    shouldShowFreeUpgrade,
  } = useModernSubscription()

  const [isYearly, setIsYearly] = React.useState<boolean>(false)

  const isCurrentPlan = (planTier: PricingTier): boolean => {
    if (planTier === 'FREE') {
      // Use the shouldShowFreeUpgrade flag to determine FREE tier display
      return shouldShowFreeUpgrade
    }
    if (planTier === 'PRO') {
      return isPaidUser && !shouldShowFreeUpgrade // Only show as current if truly paid and not in free upgrade group
    }
    return false
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      {/* Header Section */}
      <ModernSubscriptionHeader
        userTier={currentTier}
        userName={session?.user?.name || undefined}
      />

      {/* Pricing Cards Section */}
      <div className="max-w-4xl mx-auto px-4 pb-16">
        {/* Billing Toggle */}
        <BillingToggle isYearly={isYearly} onToggle={setIsYearly} />

        {/* Error message for pricing data */}
        {pricingError && (
          <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">
              ⚠️ Using fallback pricing data. {pricingError}
            </p>
          </div>
        )}

        <div className="grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
          {pricingPlans.length > 0 ? (
            pricingPlans.map(plan => (
              <ModernPricingCard
                key={plan.id}
                plan={plan}
                isCurrentPlan={isCurrentPlan(plan.id)}
                onSubscribe={handleStripeCheckout}
                isLoading={isLoading}
                isYearly={isYearly}
              />
            ))
          ) : (
            <div className="col-span-2 text-center py-8">
              <p className="text-gray-500">Loading pricing plans...</p>
            </div>
          )}
        </div>
      </div>

      {/* Contact Info */}
      <ContactInfo />
    </div>
  )
}
