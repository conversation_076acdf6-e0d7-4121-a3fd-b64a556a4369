'use client'

import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Check, X } from 'lucide-react'
import {
  PricingPlanWithDynamicPricing,
  getDisplayPrice,
} from './ModernPricingConfig'
import { cn } from '@/lib/utils'

type ModernPricingCardProps = {
  plan: PricingPlanWithDynamicPricing
  isCurrentPlan?: boolean
  onSubscribe?: (planId: string, isYearly?: boolean) => void
  isLoading?: boolean
  isYearly?: boolean
}

export const ModernPricingCard: React.FC<ModernPricingCardProps> = ({
  plan,
  isCurrentPlan = false,
  onSubscribe,
  isLoading = false,
  isYearly = false,
}) => {
  const handleSubscribe = () => {
    if (onSubscribe && !isCurrentPlan) {
      onSubscribe(plan.id, isYearly)
    }
  }

  return (
    <Card
      className={cn(
        'relative flex flex-col h-full transition-all duration-200',
        plan.popular
          ? 'border-purple-200 shadow-lg ring-2 ring-purple-100'
          : 'border-gray-200 hover:border-gray-300',
        isCurrentPlan && 'bg-gray-50'
      )}
    >
      {/* Popular Badge */}
      {plan.popular && (
        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-4 py-1">
            Most Popular
          </Badge>
        </div>
      )}

      <CardHeader className="text-center pb-4">
        <CardTitle className="text-2xl font-bold text-gray-900">
          {plan.name}
        </CardTitle>
        <p className="text-gray-600 text-sm mt-2">{plan.description}</p>

        {/* Pricing */}
        <div className="mt-4">
          <div className="flex items-baseline justify-center">
            <span className="text-4xl font-bold text-gray-900">
              {getDisplayPrice(plan, isYearly)}
            </span>
            {plan.id !== 'FREE' && (
              <span className="text-gray-600 ml-2">
                /{isYearly ? 'year' : 'month'}
              </span>
            )}
          </div>

          {isYearly && plan.pricing?.yearly && plan.id !== 'FREE' && (
            <div className="mt-2">
              <span className="text-sm text-gray-500">
                ${Math.round(plan.pricing.yearly.amount / 100 / 12)}/month when
                billed yearly
              </span>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col">
        {/* Features List */}
        <div className="flex-1">
          <ul className="space-y-3">
            {plan.features.map((feature, index) => (
              <li key={index} className="flex items-start">
                <div className="flex-shrink-0 mt-0.5">
                  {feature.included ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-gray-300" />
                  )}
                </div>
                <div className="ml-3 flex-1">
                  <span
                    className={cn(
                      'text-sm',
                      feature.included
                        ? 'text-gray-900'
                        : 'text-gray-400 line-through'
                    )}
                  >
                    {feature.name}
                  </span>
                  {feature.description && (
                    <p className="text-xs text-gray-500 mt-1">
                      {feature.description}
                    </p>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </div>

        {/* CTA Button */}
        <div className="mt-6">
          <Button
            onClick={handleSubscribe}
            disabled={isCurrentPlan || isLoading}
            variant={isCurrentPlan ? 'outline' : plan.ctaVariant}
            className={cn(
              'w-full h-12 font-semibold',
              plan.popular && !isCurrentPlan
                ? 'bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white border-0'
                : ''
            )}
          >
            {isLoading
              ? 'Processing...'
              : isCurrentPlan
                ? 'Current Plan'
                : plan.ctaText}
          </Button>
        </div>

        {/* Additional Info */}
        {plan.id === 'FREE' && (
          <p className="text-xs text-gray-500 text-center mt-3">
            No credit card required
          </p>
        )}

        {plan.id === 'PRO' && !isCurrentPlan && (
          <p className="text-xs text-gray-500 text-center mt-3">
            Cancel anytime
          </p>
        )}
      </CardContent>
    </Card>
  )
}
