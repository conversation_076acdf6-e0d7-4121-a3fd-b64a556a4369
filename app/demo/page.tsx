import Link from 'next/link'

const DEMOS: Array<{
  href: string
  title: string
  description: string
  badge?: string
}> = [
  {
    href: '/demo/context-tools',
    title: 'Research Context Toolkit',
    description:
      'Preview researched questions, fetch single answers, or gather entire categories using lightweight snapshots.',
    badge: 'New',
  },
  {
    href: '/dragTree/chat-demo',
    title: 'AI Pane Chat Demo',
    description:
      'Minimal chat experience that showcases the AI pane pipeline without integrating full context selection.',
  },
  {
    href: '/dragTree/chat-with-search-demo',
    title: 'Chat with Search Demo',
    description:
      'Demonstrates chat responses augmented by web search results within the drag tree workspace.',
  },
  {
    href: '/dragTree/loading-demo',
    title: 'Loading & Performance Demo',
    description:
      'Visualises skeleton states, suspense boundaries, and lazy-loading patterns used in drag tree surfaces.',
  },
]

const DemoDirectoryPage = () => {
  return (
    <main className="mx-auto flex w-full max-w-4xl flex-col gap-6 py-12 px-6">
      <header className="space-y-3">
        <p className="text-sm font-medium uppercase tracking-widest text-blue-600">
          Demo Directory
        </p>
        <h1 className="text-3xl font-semibold text-slate-900">
          Explore local-only product demos
        </h1>
        <p className="text-sm text-slate-600">
          These demos mirror real product flows in isolation. They are available
          only on localhost to keep internal tooling secure.
        </p>
      </header>

      <section className="grid gap-4 sm:grid-cols-2">
        {DEMOS.map(demo => (
          <Link
            key={demo.href}
            href={demo.href}
            className="group rounded-xl border border-slate-200 bg-white p-5 shadow-sm transition hover:border-blue-400 hover:shadow-md"
          >
            <div className="flex items-start justify-between gap-3">
              <h2 className="text-lg font-semibold text-slate-900 group-hover:text-blue-600">
                {demo.title}
              </h2>
              {demo.badge ? (
                <span className="rounded-full bg-blue-50 px-2 py-0.5 text-xs font-medium uppercase tracking-wide text-blue-600">
                  {demo.badge}
                </span>
              ) : null}
            </div>
            <p className="mt-2 text-sm text-slate-600">{demo.description}</p>
            <p className="mt-3 text-xs font-medium text-blue-600">
              Open demo →
            </p>
          </Link>
        ))}
      </section>
    </main>
  )
}

export default DemoDirectoryPage
