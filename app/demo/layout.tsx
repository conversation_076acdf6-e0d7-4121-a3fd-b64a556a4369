import type { ReactNode } from 'react'
import { headers } from 'next/headers'

const ALLOWED_HOST_PREFIXES = ['localhost', '127.0.0.1']

const isLocalhost = (host: string | null): boolean => {
  if (!host) return false
  return ALLOWED_HOST_PREFIXES.some(prefix => host.startsWith(prefix))
}

type DemoLayoutProps = {
  children: ReactNode
}

const DemoLayout = async ({ children }: DemoLayoutProps) => {
  const headerList = await headers()
  const host = headerList.get('host')

  if (!isLocalhost(host)) {
    return (
      <main className="flex min-h-screen items-center justify-center bg-slate-100 px-6">
        <div className="max-w-xl rounded-lg border border-slate-200 bg-white p-8 shadow-sm">
          <h1 className="text-xl font-semibold text-slate-900">
            Demo area is limited to localhost
          </h1>
          <p className="mt-2 text-sm text-slate-600">
            For security reasons these demos are only available when accessed
            from a local development environment.
          </p>
          <p className="mt-4 text-sm text-slate-600">
            Ensure you are running the app locally (e.g.{' '}
            <code className="rounded bg-slate-100 px-2 py-1">
              localhost:3000
            </code>
            ) and try again.
          </p>
        </div>
      </main>
    )
  }

  return <div className="min-h-screen bg-slate-50">{children}</div>
}

export default DemoLayout
