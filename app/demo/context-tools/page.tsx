'use client'

import { useState, type ReactElement } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'

const formatJson = (value: unknown): string => {
  try {
    return JSON.stringify(value, null, 2)
  } catch (error) {
    console.error('Failed to format JSON output', error)
    return String(value)
  }
}

const ResearchContextToolkitDemo = (): ReactElement => {
  const [previewTreeId, setPreviewTreeId] = useState<string>('')
  const [previewMarkdown, setPreviewMarkdown] = useState<string>('')
  const [previewError, setPreviewError] = useState<string>('')
  const [isPreviewLoading, setIsPreviewLoading] = useState<boolean>(false)

  const [questionTreeId, setQuestionTreeId] = useState<string>('')
  const [questionContentId, setQuestionContentId] = useState<string>('')
  const [questionTokenLimit, setQuestionTokenLimit] = useState<string>('')
  const [questionResult, setQuestionResult] = useState<string>('')
  const [questionError, setQuestionError] = useState<string>('')
  const [isQuestionLoading, setIsQuestionLoading] = useState<boolean>(false)

  const [categoryTreeId, setCategoryTreeId] = useState<string>('')
  const [categoryId, setCategoryId] = useState<string>('')
  const [categoryTokenBudget, setCategoryTokenBudget] = useState<string>('')
  const [categoryResult, setCategoryResult] = useState<string>('')
  const [categoryError, setCategoryError] = useState<string>('')
  const [isCategoryLoading, setIsCategoryLoading] = useState<boolean>(false)

  const fetchPreview = async () => {
    if (!previewTreeId.trim()) {
      setPreviewError('Drag tree id is required.')
      return
    }

    setPreviewError('')
    setPreviewMarkdown('')
    setIsPreviewLoading(true)

    try {
      const response = await fetch(
        `/api/dragtree/context-tools/preview?dragTreeId=${encodeURIComponent(
          previewTreeId.trim()
        )}`
      )
      const json = await response.json()

      if (!response.ok || !json.success) {
        throw new Error(json.error || 'Failed to fetch preview outline')
      }

      setPreviewMarkdown(json.data?.markdown ?? '')
    } catch (error) {
      setPreviewError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setIsPreviewLoading(false)
    }
  }

  const fetchQuestion = async () => {
    if (!questionTreeId.trim() || !questionContentId.trim()) {
      setQuestionError('Both drag tree id and content id are required.')
      return
    }

    setQuestionError('')
    setQuestionResult('')
    setIsQuestionLoading(true)

    const params = new URLSearchParams({
      dragTreeId: questionTreeId.trim(),
      contentId: questionContentId.trim(),
    })

    if (questionTokenLimit.trim()) {
      params.set('tokenLimit', questionTokenLimit.trim())
    }

    try {
      const response = await fetch(
        `/api/dragtree/context-tools/question?${params}`
      )
      const json = await response.json()

      if (!response.ok || !json.success) {
        throw new Error(json.error || 'Failed to fetch question context')
      }

      setQuestionResult(formatJson(json.data))
    } catch (error) {
      setQuestionError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setIsQuestionLoading(false)
    }
  }

  const fetchCategory = async () => {
    if (!categoryTreeId.trim() || !categoryId.trim()) {
      setCategoryError('Both drag tree id and category id are required.')
      return
    }

    setCategoryError('')
    setCategoryResult('')
    setIsCategoryLoading(true)

    const params = new URLSearchParams({
      dragTreeId: categoryTreeId.trim(),
      categoryId: categoryId.trim(),
    })

    if (categoryTokenBudget.trim()) {
      params.set('totalTokens', categoryTokenBudget.trim())
    }

    try {
      const response = await fetch(
        `/api/dragtree/context-tools/category?${params}`
      )
      const json = await response.json()

      if (!response.ok || !json.success) {
        throw new Error(json.error || 'Failed to fetch category context')
      }

      setCategoryResult(formatJson(json.data))
    } catch (error) {
      setCategoryError(error instanceof Error ? error.message : 'Unknown error')
    } finally {
      setIsCategoryLoading(false)
    }
  }

  return (
    <main className="mx-auto flex w-full max-w-4xl flex-col gap-10 py-12">
      <header className="space-y-2 px-6 sm:px-0">
        <p className="text-sm font-medium uppercase tracking-widest text-blue-600">
          Research Context Toolkit Demo
        </p>
        <h1 className="text-3xl font-semibold text-slate-900">
          Explore lightweight research context utilities
        </h1>
        <p className="text-sm text-slate-600">
          Start with the preview call to discover researched questions. Then
          fetch individual answers or category snippets with optional token
          budgeting.
        </p>
      </header>

      <section className="rounded-xl border border-slate-200 bg-white p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-slate-900">
          1. Preview researched questions
        </h2>
        <p className="mt-1 text-sm text-slate-600">
          Input a drag tree id to receive a markdown outline listing categories
          and question content ids.
        </p>
        <div className="mt-4 flex flex-col gap-3 sm:flex-row">
          <Input
            value={previewTreeId}
            onChange={event => setPreviewTreeId(event.target.value)}
            placeholder="dragTree_123"
            className="sm:max-w-xs"
          />
          <Button onClick={fetchPreview} disabled={isPreviewLoading}>
            {isPreviewLoading ? 'Loading…' : 'Fetch Preview'}
          </Button>
        </div>
        {previewError && (
          <p className="mt-3 text-sm text-red-600">{previewError}</p>
        )}
        {previewMarkdown && (
          <Textarea
            value={previewMarkdown}
            readOnly
            className="mt-4 h-48 font-mono text-sm"
          />
        )}
      </section>

      <section className="rounded-xl border border-slate-200 bg-white p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-slate-900">
          2. Retrieve a single question
        </h2>
        <p className="mt-1 text-sm text-slate-600">
          Paste the drag tree id and content id from the preview. Optionally cap
          the token size for compact responses.
        </p>
        <div className="mt-4 grid gap-3 sm:grid-cols-3">
          <Input
            value={questionTreeId}
            onChange={event => setQuestionTreeId(event.target.value)}
            placeholder="dragTree_123"
          />
          <Input
            value={questionContentId}
            onChange={event => setQuestionContentId(event.target.value)}
            placeholder="content_abc"
          />
          <Input
            value={questionTokenLimit}
            onChange={event => setQuestionTokenLimit(event.target.value)}
            placeholder="token limit (optional)"
          />
        </div>
        <Button
          className="mt-4"
          onClick={fetchQuestion}
          disabled={isQuestionLoading}
        >
          {isQuestionLoading ? 'Loading…' : 'Fetch Question Context'}
        </Button>
        {questionError && (
          <p className="mt-3 text-sm text-red-600">{questionError}</p>
        )}
        {questionResult && (
          <Textarea
            value={questionResult}
            readOnly
            className="mt-4 h-48 font-mono text-sm"
          />
        )}
      </section>

      <section className="rounded-xl border border-slate-200 bg-white p-6 shadow-sm">
        <h2 className="text-xl font-semibold text-slate-900">
          3. Gather a category
        </h2>
        <p className="mt-1 text-sm text-slate-600">
          Fetch all researched questions beneath a given category. Provide a
          total token budget to balance snippet sizes evenly.
        </p>
        <div className="mt-4 grid gap-3 sm:grid-cols-3">
          <Input
            value={categoryTreeId}
            onChange={event => setCategoryTreeId(event.target.value)}
            placeholder="dragTree_123"
          />
          <Input
            value={categoryId}
            onChange={event => setCategoryId(event.target.value)}
            placeholder="category_xyz"
          />
          <Input
            value={categoryTokenBudget}
            onChange={event => setCategoryTokenBudget(event.target.value)}
            placeholder="total tokens (optional)"
          />
        </div>
        <Button
          className="mt-4"
          onClick={fetchCategory}
          disabled={isCategoryLoading}
        >
          {isCategoryLoading ? 'Loading…' : 'Fetch Category Context'}
        </Button>
        {categoryError && (
          <p className="mt-3 text-sm text-red-600">{categoryError}</p>
        )}
        {categoryResult && (
          <Textarea
            value={categoryResult}
            readOnly
            className="mt-4 h-48 font-mono text-sm"
          />
        )}
      </section>

      <footer className="px-6 text-xs text-slate-500 sm:px-0">
        <p>
          API endpoints used:
          <code className="mx-1 rounded bg-slate-100 px-1.5 py-0.5">
            /api/dragtree/context-tools/preview
          </code>
          <code className="mx-1 rounded bg-slate-100 px-1.5 py-0.5">
            /api/dragtree/context-tools/question
          </code>
          <code className="mx-1 rounded bg-slate-100 px-1.5 py-0.5">
            /api/dragtree/context-tools/category
          </code>
        </p>
      </footer>
    </main>
  )
}

export default ResearchContextToolkitDemo
