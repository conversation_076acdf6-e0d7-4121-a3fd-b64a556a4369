export const LOCALES = ['en', 'zh-<PERSON>', 'zh-<PERSON><PERSON>', 'ja', 'es'] as const
export type Locale = (typeof LOCALES)[number]

export const DEFAULT_LOCALE: Locale = 'en'

export function getLocaleFromPath(pathname: string | null | undefined): Locale {
  if (!pathname) return DEFAULT_LOCALE
  // Explicit language only via URL prefix, e.g. /zh-Hant/...
  if (pathname.startsWith('/zh-Hant')) return 'zh-Hant'
  if (pathname.startsWith('/zh-<PERSON>')) return 'zh-Hans'
  if (pathname.startsWith('/ja')) return 'ja'
  if (pathname.startsWith('/es')) return 'es'
  return 'en'
}
