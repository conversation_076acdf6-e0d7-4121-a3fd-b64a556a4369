'use client'

import React, { createContext, useContext, useMemo } from 'react'
import { usePathname } from 'next/navigation'
import {
  DEFAULT_LOCALE,
  getLocaleFromPath,
  type Locale,
} from '@/app/i18n/config'
import en from '@/app/i18n/messages/en.json'
import zhHans from '@/app/i18n/messages/zh-Hans.json'
import zhHant from '@/app/i18n/messages/zh-Hant.json'
import ja from '@/app/i18n/messages/ja.json'
import es from '@/app/i18n/messages/es.json'

type Messages = Record<string, any>

type I18nContextType = {
  locale: Locale
  messages: Messages
  t: (key: string, defaultValue?: string) => string
  useT: (namespace?: string) => (key: string, defaultValue?: string) => string
}

const I18nContext = createContext<I18nContextType | undefined>(undefined)

function resolveKey(messages: Messages, key: string): string | undefined {
  const parts = key.split('.')
  let current: any = messages
  for (const part of parts) {
    if (current && typeof current === 'object' && part in current) {
      current = current[part]
    } else {
      return undefined
    }
  }
  return typeof current === 'string' ? current : undefined
}

export const I18nProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const pathname = usePathname() || ''
  const locale = useMemo(() => getLocaleFromPath(pathname), [pathname])

  const messages = useMemo(() => {
    switch (locale) {
      case 'zh-Hans':
        return zhHans as Messages
      case 'zh-Hant':
        return zhHant as Messages
      case 'ja':
        return ja as Messages
      case 'es':
        return es as Messages
      case 'en':
      default:
        return en as Messages
    }
  }, [locale])

  const t = useMemo(
    () => (key: string, defaultValue?: string) => {
      return (
        resolveKey(messages, key) ??
        defaultValue ??
        resolveKey(en as Messages, key) ??
        key
      )
    },
    [messages]
  )

  const useT = (namespace?: string) => (key: string, defaultValue?: string) => {
    const fullKey = namespace ? `${namespace}.${key}` : key
    return t(fullKey, defaultValue)
  }

  const value: I18nContextType = {
    locale: (locale || DEFAULT_LOCALE) as Locale,
    messages,
    t,
    useT,
  }

  return <I18nContext.Provider value={value}>{children}</I18nContext.Provider>
}

export function useI18n() {
  const ctx = useContext(I18nContext)
  if (!ctx) throw new Error('useI18n must be used within I18nProvider')
  return ctx
}

export function useT(namespace?: string) {
  return useI18n().useT(namespace)
}
