import type { NextRequest } from 'next/server'

// Shared helpers that sanitize and normalize UTM payloads across middleware and auth.

export const UTM_ALLOWED_KEYS = [
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content',
  'utm_id',
  'gclid',
  'fbclid',
  'msclkid',
] as const

export type UtmAllowedKey = (typeof UTM_ALLOWED_KEYS)[number]

export type UtmCookiePayload = Partial<Record<UtmAllowedKey, string>> & {
  captured_at?: string
  landing_url?: string
}

export const UTM_LAST_COOKIE = '__utm_last'
export const UTM_FIRST_COOKIE = '__utm_first'

const MAX_UTM_VALUE_LENGTH = 200
const MAX_LANDING_PATH_LENGTH = 512

export const UTM_LAST_MAX_AGE_SECONDS = 60 * 60 * 24 * 90
export const UTM_FIRST_MAX_AGE_SECONDS = 60 * 60 * 24 * 365

const ISO_DATE_LENGTH = 24

const sanitizeString = (
  value: unknown,
  maxLength: number
): string | undefined => {
  if (typeof value !== 'string') return undefined
  const trimmed = value.trim().slice(0, maxLength)
  return trimmed.length > 0 ? trimmed : undefined
}

const sanitizeLandingPath = (request: NextRequest): string | undefined => {
  const pathWithSearch = `${request.nextUrl.pathname}${request.nextUrl.search}`
  const sanitized = sanitizeString(pathWithSearch, MAX_LANDING_PATH_LENGTH)
  if (!sanitized) return undefined
  if (!sanitized.startsWith('/')) return `/${sanitized}`
  return sanitized
}

const sanitizeIsoDate = (value: unknown): string | undefined => {
  if (typeof value !== 'string') return undefined
  if (value.length > ISO_DATE_LENGTH) return value.slice(0, ISO_DATE_LENGTH)
  return value
}

export const extractUtmParams = (
  searchParams: URLSearchParams
): UtmCookiePayload | null => {
  const nextPayload: UtmCookiePayload = {}
  let hasValues = false

  UTM_ALLOWED_KEYS.forEach(key => {
    const value = sanitizeString(searchParams.get(key), MAX_UTM_VALUE_LENGTH)
    if (value) {
      nextPayload[key] = value
      hasValues = true
    }
  })

  return hasValues ? nextPayload : null
}

export const buildUtmCookiePayload = (
  request: NextRequest
): UtmCookiePayload | null => {
  const params = extractUtmParams(request.nextUrl.searchParams)
  if (!params) return null

  const timestamp = new Date().toISOString()
  const landingUrl = sanitizeLandingPath(request)

  return {
    ...params,
    captured_at: timestamp,
    landing_url: landingUrl,
  }
}

export const parseUtmCookie = (
  value: string | undefined
): UtmCookiePayload | null => {
  if (!value) return null
  try {
    const parsed = JSON.parse(decodeURIComponent(value)) as Record<
      string,
      unknown
    >
    return sanitizeUtmRecord(parsed)
  } catch {
    return null
  }
}

export const sanitizeUtmRecord = (
  record: Record<string, unknown> | null
): UtmCookiePayload | null => {
  if (!record) return null

  const nextPayload: UtmCookiePayload = {}
  let hasValues = false

  UTM_ALLOWED_KEYS.forEach(key => {
    const sanitized = sanitizeString(record[key], MAX_UTM_VALUE_LENGTH)
    if (sanitized) {
      nextPayload[key] = sanitized
      hasValues = true
    }
  })

  const landingUrl = sanitizeString(record.landing_url, MAX_LANDING_PATH_LENGTH)
  if (landingUrl) {
    nextPayload.landing_url = landingUrl.startsWith('/')
      ? landingUrl
      : `/${landingUrl}`
  }

  const capturedAt = sanitizeIsoDate(record.captured_at)
  if (capturedAt) {
    nextPayload.captured_at = capturedAt
  }

  if (!hasValues && !nextPayload.landing_url && !nextPayload.captured_at) {
    return null
  }

  return nextPayload
}

export const encodeUtmCookie = (payload: UtmCookiePayload): string =>
  encodeURIComponent(JSON.stringify(payload))

export const isSecureRequest = (request: NextRequest): boolean =>
  request.nextUrl.protocol === 'https:'

export const sameUtmPayload = (
  left: unknown,
  right: UtmCookiePayload | null
): boolean => {
  if (!left && !right) return true
  if (!left || !right) return false
  try {
    return JSON.stringify(left) === JSON.stringify(right)
  } catch {
    return false
  }
}
