/**
 * Focused coverage for UTM sanitization helpers to guarantee we only persist
 * allowlisted, normalized values when middleware/auth consume URL params.
 */

import { NextRequest } from 'next/server'

import {
  UTM_ALLOWED_KEYS,
  buildUtmCookiePayload,
  encodeUtmCookie,
  parseUtmCookie,
  sanitizeUtmRecord,
  sameUtmPayload,
  type UtmCookiePayload,
} from '@/app/libs/utm/constants'

const freezeTime = (epochMs: number): void => {
  jest.useFakeTimers()
  jest.setSystemTime(epochMs)
}

const resetTime = (): void => {
  jest.useRealTimers()
}

const buildRequest = (url: string): NextRequest =>
  ({
    nextUrl: new URL(url),
  }) as unknown as NextRequest

describe('UTM helper sanitization', () => {
  afterEach(() => {
    resetTime()
  })

  it('captures allowlisted params with landing path and timestamp', () => {
    freezeTime(new Date('2024-12-01T05:41:11.321Z').getTime())
    const request = buildRequest(
      'https://thoughtatlas.ai/screening?utm_source=Newsletter&utm_medium=Email&feature=demand'
    )

    const payload = buildUtmCookiePayload(request)
    expect(payload).toBeTruthy()
    expect(payload?.utm_source).toBe('Newsletter')
    expect(payload?.utm_medium).toBe('Email')
    expect(payload?.landing_url).toBe(
      '/screening?utm_source=Newsletter&utm_medium=Email&feature=demand'
    )
    expect(payload?.captured_at).toBe('2024-12-01T05:41:11.321Z')
  })

  it('returns null when no allowlisted params exist', () => {
    const request = buildRequest('https://thoughtatlas.ai/?ref=friend')
    expect(buildUtmCookiePayload(request)).toBeNull()
  })

  it('truncates oversized values and normalizes landing paths', () => {
    const longValue = 'x'.repeat(500)
    const request = buildRequest(
      `https://thoughtatlas.ai/path?utm_campaign=${longValue}`
    )

    const payload = buildUtmCookiePayload(request)
    expect(payload?.utm_campaign?.length).toBeLessThanOrEqual(200)
    expect(payload?.landing_url?.startsWith('/path?utm_campaign=')).toBe(true)
    expect(payload?.landing_url?.length).toBeLessThanOrEqual(512)
  })

  it('sanitizes decoded cookies and strips unknown fields', () => {
    const record = sanitizeUtmRecord({
      utm_source: '  Paid Ads  ',
      utm_medium: 'CPC',
      other: 'ignored',
      landing_url: 'landing?utm_source=Paid',
      captured_at: '2024-01-02T03:04:05.678Z',
    })

    expect(record).toEqual({
      utm_source: 'Paid Ads',
      utm_medium: 'CPC',
      landing_url: '/landing?utm_source=Paid',
      captured_at: '2024-01-02T03:04:05.678Z',
    })
  })

  it('treats structurally equal payloads as identical', () => {
    const base: UtmCookiePayload = {
      utm_source: 'launch',
      utm_medium: 'email',
      captured_at: '2024-01-01T00:00:00.000Z',
    }

    const encoded = encodeUtmCookie(base)
    const decoded = parseUtmCookie(encoded)

    expect(decoded).toEqual(base)
    expect(sameUtmPayload(base, decoded)).toBe(true)
  })

  it('handles corrupt cookies gracefully', () => {
    expect(parseUtmCookie('%E0%A4%A')).toBeNull()
    expect(sanitizeUtmRecord(null)).toBeNull()
    expect(sameUtmPayload(undefined, null)).toBe(true)
  })

  it('maintains key allowlist integrity', () => {
    expect(UTM_ALLOWED_KEYS).toContain('utm_source')
    expect(UTM_ALLOWED_KEYS).not.toContain('ref')
  })
})
