import { create } from 'zustand'

export type Viewport = { x: number; y: number; zoom: number }

type ReactFlowViewStore = {
  saved: Record<string, Viewport>
  getViewport: (treeId: string) => Viewport | undefined
  saveViewport: (treeId: string, viewport: Viewport) => void
  clearViewport: (treeId: string) => void
  loadFromLocalStorage: (treeId: string) => void
  flushToLocalStorage: (treeId: string) => void
}

const storageKey = (treeId: string) => `clarifyai:reactflow:viewport:${treeId}`

export const useReactFlowViewStore = create<ReactFlowViewStore>((set, get) => ({
  saved: {},

  getViewport: (treeId: string) => {
    const state = get()
    return state.saved[treeId]
  },

  saveViewport: (treeId: string, viewport: Viewport) => {
    set(state => ({ saved: { ...state.saved, [treeId]: viewport } }))
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem(storageKey(treeId), JSON.stringify(viewport))
      }
    } catch {}
  },

  clearViewport: (treeId: string) => {
    set(state => {
      const next = { ...state.saved }
      delete next[treeId]
      return { saved: next }
    })
    try {
      if (typeof window !== 'undefined') {
        localStorage.removeItem(storageKey(treeId))
      }
    } catch {}
  },

  loadFromLocalStorage: (treeId: string) => {
    try {
      if (typeof window === 'undefined') return
      const raw = localStorage.getItem(storageKey(treeId))
      if (!raw) return
      const vp = JSON.parse(raw) as Viewport
      if (
        vp &&
        typeof vp.x === 'number' &&
        typeof vp.y === 'number' &&
        typeof vp.zoom === 'number' &&
        isFinite(vp.x) &&
        isFinite(vp.y) &&
        isFinite(vp.zoom) &&
        !isNaN(vp.x) &&
        !isNaN(vp.y) &&
        !isNaN(vp.zoom)
      ) {
        set(state => ({ saved: { ...state.saved, [treeId]: vp } }))
      }
    } catch {}
  },

  flushToLocalStorage: (treeId: string) => {
    const vp = get().saved[treeId]
    if (!vp) return
    try {
      if (typeof window !== 'undefined') {
        localStorage.setItem(storageKey(treeId), JSON.stringify(vp))
      }
    } catch {}
  },
}))
