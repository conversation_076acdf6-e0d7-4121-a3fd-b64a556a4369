import { create } from 'zustand'

// Navigation state machine types
type NavigationState =
  | 'idle'
  | 'navigating_to_flow'
  | 'navigating_to_tree'
  | 'highlighting_flow'
  | 'highlighting_tree'

type NavigationEvent =
  | { type: 'NAVIGATE_TO_FLOW'; nodeId: string }
  | { type: 'NAVIGATE_TO_TREE'; nodeId: string }
  | { type: 'NAVIGATE_FROM_FLOW'; nodeId: string }
  | { type: 'HIGHLIGHT_COMPLETE' }
  | { type: 'RESET' }

// Global timeout controllers for cleanup
const timeoutControllers = new Map<string, AbortController>()

// Debounce controllers for preventing rapid navigation
const debounceControllers = new Map<string, AbortController>()

/**
 * Utility function to create a timeout with AbortController cleanup
 */
const createAbortableTimeout = (
  key: string,
  callback: () => void,
  delay: number
): void => {
  // Cancel any existing timeout for this key
  const existingController = timeoutControllers.get(key)
  if (existingController) {
    existingController.abort()
  }

  // Create new controller
  const controller = new AbortController()
  timeoutControllers.set(key, controller)

  const timeoutId = setTimeout(() => {
    if (!controller.signal.aborted) {
      callback()
      timeoutControllers.delete(key)
    }
  }, delay)

  // Listen for abort signal to clear timeout
  controller.signal.addEventListener('abort', () => {
    clearTimeout(timeoutId)
    timeoutControllers.delete(key)
  })
}

/**
 * Utility function to create a debounced action with AbortController cleanup
 */
const createDebouncedAction = (
  key: string,
  action: () => void,
  delay: number = 100
): void => {
  // Cancel any existing debounce for this key
  const existingController = debounceControllers.get(key)
  if (existingController) {
    existingController.abort()
  }

  // Create new controller
  const controller = new AbortController()
  debounceControllers.set(key, controller)

  const timeoutId = setTimeout(() => {
    if (!controller.signal.aborted) {
      action()
      debounceControllers.delete(key)
    }
  }, delay)

  // Listen for abort signal to clear timeout
  controller.signal.addEventListener('abort', () => {
    clearTimeout(timeoutId)
    debounceControllers.delete(key)
  })
}

/**
 * Cleanup function to abort all pending timeouts and debounces
 */
export const cleanupNavigationTimeouts = (): void => {
  timeoutControllers.forEach(controller => controller.abort())
  timeoutControllers.clear()
  debounceControllers.forEach(controller => controller.abort())
  debounceControllers.clear()
}

/**
 * State machine transition logic
 */
const getNextState = (
  currentState: NavigationState,
  event: NavigationEvent
): NavigationState => {
  switch (currentState) {
    case 'idle':
      switch (event.type) {
        case 'NAVIGATE_TO_FLOW':
          return 'navigating_to_flow'
        case 'NAVIGATE_TO_TREE':
        case 'NAVIGATE_FROM_FLOW':
          return 'navigating_to_tree'
        default:
          return currentState
      }
    case 'navigating_to_flow':
      switch (event.type) {
        case 'HIGHLIGHT_COMPLETE':
          return 'highlighting_flow'
        case 'RESET':
          return 'idle'
        default:
          return currentState
      }
    case 'navigating_to_tree':
      switch (event.type) {
        case 'HIGHLIGHT_COMPLETE':
          return 'highlighting_tree'
        case 'RESET':
          return 'idle'
        default:
          return currentState
      }
    case 'highlighting_flow':
    case 'highlighting_tree':
      switch (event.type) {
        case 'RESET':
          return 'idle'
        case 'NAVIGATE_TO_FLOW':
          return 'navigating_to_flow'
        case 'NAVIGATE_TO_TREE':
        case 'NAVIGATE_FROM_FLOW':
          return 'navigating_to_tree'
        default:
          return currentState
      }
    default:
      return currentState
  }
}

type NavigationStore = {
  // State machine state
  navigationState: NavigationState

  // React Flow navigation
  targetNodeId: string | null
  setTargetNodeId: (nodeId: string | null) => void
  navigateToNode: (nodeId: string) => void
  // URL-specific navigation with longer TTL so canvases can initialize
  navigateToNodeFromUrl: (nodeId: string) => void

  // Tree View navigation (reverse direction)
  treeTargetNodeId: string | null
  setTreeTargetNodeId: (nodeId: string | null) => void
  navigateToTreeNode: (nodeId: string) => void

  // Flag to prevent React Flow auto-focus when navigation comes from React Flow
  preventReactFlowFocus: boolean
  navigateToTreeNodeFromReactFlow: (nodeId: string) => void

  // Navigation state tracking to prevent race conditions
  isNavigating: boolean
  lastNavigationTime: number
  lastNavigationSource: 'outline' | 'visualization' | null

  // URL focus parameter callback
  onNodeFocusChange: ((nodeId: string | null) => void) | null
  setOnNodeFocusChange: (
    callback: ((nodeId: string | null) => void) | null
  ) => void

  // Handshake mechanism for UI component readiness
  isOutlineReady: boolean
  isCanvasReady: boolean
  pendingNavigation: string | null
  setOutlineReady: (isReady: boolean) => void
  setCanvasReady: (isReady: boolean) => void
  processPendingNavigation: () => void

  // State machine dispatch
  dispatch: (event: NavigationEvent) => void

  // Cleanup function
  cleanup: () => void
}

export const useNavigationStore = create<NavigationStore>((set, get) => ({
  navigationState: 'idle',
  targetNodeId: null,
  treeTargetNodeId: null,
  preventReactFlowFocus: false,
  isNavigating: false,
  lastNavigationTime: 0,
  lastNavigationSource: null,
  onNodeFocusChange: null,

  // Handshake mechanism initial state
  isOutlineReady: false,
  isCanvasReady: false,
  pendingNavigation: null,

  setTargetNodeId: nodeId => set({ targetNodeId: nodeId }),
  setTreeTargetNodeId: nodeId => set({ treeTargetNodeId: nodeId }),
  setOnNodeFocusChange: callback => set({ onNodeFocusChange: callback }),

  // Handshake mechanism actions
  setOutlineReady: (isReady: boolean) => {
    console.log(`🏗️ [NavigationStore] Outline readiness: ${isReady}`)
    set({ isOutlineReady: isReady })
    if (isReady) {
      get().processPendingNavigation()
    }
  },

  setCanvasReady: (isReady: boolean) => {
    console.log(`🎨 [NavigationStore] Canvas readiness: ${isReady}`)
    set({ isCanvasReady: isReady })
    if (isReady) {
      get().processPendingNavigation()
    }
  },

  processPendingNavigation: () => {
    const state = get()
    const { pendingNavigation, isOutlineReady, isCanvasReady } = state

    if (!pendingNavigation) {
      console.log('🔄 [NavigationStore] No pending navigation to process')
      return
    }

    if (!isOutlineReady || !isCanvasReady) {
      console.log(
        `⏳ [NavigationStore] Components not ready yet - Outline: ${isOutlineReady}, Canvas: ${isCanvasReady}`
      )
      return
    }

    console.log(
      `🚀 [NavigationStore] Processing pending navigation to: ${pendingNavigation}`
    )

    // Execute navigation to both components
    state.navigateToNode(pendingNavigation)
    state.navigateToTreeNode(pendingNavigation)

    // Clear pending navigation
    set({ pendingNavigation: null })
  },

  dispatch: (event: NavigationEvent) => {
    const state = get()
    const nextState = getNextState(state.navigationState, event)

    if (nextState !== state.navigationState) {
      console.log(
        `🔄 Navigation state: ${state.navigationState} -> ${nextState}`
      )
      set({ navigationState: nextState })
    }
  },

  navigateToNode: nodeId => {
    const currentTime = Date.now()
    const state = get()

    // Prevent rapid navigation from same source
    if (
      state.isNavigating &&
      state.lastNavigationSource === 'outline' &&
      currentTime - state.lastNavigationTime < 300
    ) {
      console.log('🚫 Navigation debounced: too rapid from outline')
      return
    }

    // Dispatch state machine event
    state.dispatch({ type: 'NAVIGATE_TO_FLOW', nodeId })

    // Use debounced action to prevent race conditions
    createDebouncedAction(
      `navigate-to-node-${nodeId}`,
      () => {
        set({
          targetNodeId: nodeId,
          isNavigating: true,
          lastNavigationTime: currentTime,
          lastNavigationSource: 'outline',
        })

        // Notify focus parameter callback with error handling
        const currentState = get()
        if (currentState.onNodeFocusChange) {
          try {
            currentState.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }

        // Dispatch highlight complete immediately after setting target
        state.dispatch({ type: 'HIGHLIGHT_COMPLETE' })

        // Clear the target after a delay with proper cleanup
        createAbortableTimeout(
          `clear-navigate-to-node-${nodeId}`,
          () => {
            const currentState = get()
            currentState.dispatch({ type: 'RESET' })
            set({
              targetNodeId: null,
              isNavigating: false,
            })
          },
          2000
        )
      },
      100
    )
  },

  // URL-specific navigation using handshake pattern to ensure UI components are ready
  navigateToNodeFromUrl: nodeId => {
    const state = get()
    const { isOutlineReady, isCanvasReady } = state

    console.log(`🎯 [NavigationStore] URL navigation requested for: ${nodeId}`)
    console.log(
      `🔍 [NavigationStore] Component readiness - Outline: ${isOutlineReady}, Canvas: ${isCanvasReady}`
    )

    // Check if both UI components are ready
    if (isOutlineReady && isCanvasReady) {
      console.log(
        `✅ [NavigationStore] Both components ready, executing immediate navigation`
      )

      // Dispatch state machine event
      state.dispatch({ type: 'NAVIGATE_TO_FLOW', nodeId })

      // Execute navigation immediately
      state.navigateToNode(nodeId)
      state.navigateToTreeNode(nodeId)

      // Notify focus parameter callback with error handling
      if (state.onNodeFocusChange) {
        try {
          state.onNodeFocusChange(nodeId)
        } catch (error) {
          console.warn('Focus parameter callback error:', error)
        }
      }
    } else {
      console.log(
        `⏳ [NavigationStore] Components not ready, storing pending navigation: ${nodeId}`
      )

      // Store the nodeId for later execution when components are ready
      set({ pendingNavigation: nodeId })

      // Notify focus parameter callback with error handling (for URL updates)
      if (state.onNodeFocusChange) {
        try {
          state.onNodeFocusChange(nodeId)
        } catch (error) {
          console.warn('Focus parameter callback error:', error)
        }
      }
    }
  },

  navigateToTreeNode: nodeId => {
    const currentTime = Date.now()
    const state = get()

    // Prevent rapid navigation from same source
    if (
      state.isNavigating &&
      state.lastNavigationSource === 'visualization' &&
      currentTime - state.lastNavigationTime < 300
    ) {
      console.log('🚫 Navigation debounced: too rapid from visualization')
      return
    }

    // Dispatch state machine event
    state.dispatch({ type: 'NAVIGATE_TO_TREE', nodeId })

    // Use debounced action to prevent race conditions
    createDebouncedAction(
      `navigate-to-tree-node-${nodeId}`,
      () => {
        set({
          treeTargetNodeId: nodeId,
          isNavigating: true,
          lastNavigationTime: currentTime,
          lastNavigationSource: 'visualization',
        })

        // Notify focus parameter callback with error handling
        const currentState = get()
        if (currentState.onNodeFocusChange) {
          try {
            currentState.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }

        // Dispatch highlight complete immediately after setting target
        state.dispatch({ type: 'HIGHLIGHT_COMPLETE' })

        // Clear the target after a delay with proper cleanup
        createAbortableTimeout(
          `clear-navigate-to-tree-node-${nodeId}`,
          () => {
            const currentState = get()
            currentState.dispatch({ type: 'RESET' })
            set({
              treeTargetNodeId: null,
              isNavigating: false,
            })
          },
          3000
        )
      },
      100
    )
  },

  navigateToTreeNodeFromReactFlow: nodeId => {
    const currentTime = Date.now()
    const state = get()

    // Prevent rapid navigation from same source
    if (
      state.isNavigating &&
      state.lastNavigationSource === 'visualization' &&
      currentTime - state.lastNavigationTime < 300
    ) {
      console.log('🚫 Navigation debounced: too rapid from React Flow')
      return
    }

    // Dispatch state machine event
    state.dispatch({ type: 'NAVIGATE_FROM_FLOW', nodeId })

    // Use debounced action to prevent race conditions
    createDebouncedAction(
      `navigate-from-flow-${nodeId}`,
      () => {
        set({
          treeTargetNodeId: nodeId,
          preventReactFlowFocus: true,
          isNavigating: true,
          lastNavigationTime: currentTime,
          lastNavigationSource: 'visualization',
        })

        // Notify focus parameter callback with error handling
        const currentState = get()
        if (currentState.onNodeFocusChange) {
          try {
            currentState.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }

        // Dispatch highlight complete immediately after setting target
        state.dispatch({ type: 'HIGHLIGHT_COMPLETE' })

        // Clear the target and reset the flag after highlighting with proper cleanup
        createAbortableTimeout(
          `clear-navigate-from-flow-${nodeId}`,
          () => {
            const currentState = get()
            currentState.dispatch({ type: 'RESET' })
            set({
              treeTargetNodeId: null,
              preventReactFlowFocus: false,
              isNavigating: false,
            })
          },
          3000
        )
      },
      100
    )
  },

  cleanup: () => {
    cleanupNavigationTimeouts()
  },
}))
