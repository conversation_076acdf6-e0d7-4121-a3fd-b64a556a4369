/**
 * DragTree Store
 *
 * Central state management for the drag tree functionality.
 *
 * Architecture:
 * - Zustand store for global state management
 * - Modular utilities for specific operations (tree, node, database)
 * - Automatic database synchronization with debouncing
 * - Content management for AI-generated research
 *
 * Key Design Decisions:
 * - Separation of frontend tree structure (hierarchical) from database structure (flat)
 * - Node map for O(1) lookups while maintaining tree hierarchy
 * - Debounced database operations to prevent excessive API calls
 * - Content versioning support for future A/B testing
 *
 * State Structure:
 * - frontendTreeStructure: Hierarchical tree representation for UI
 * - nodeMap: Flat map for fast node lookups
 * - nodeContent: Multi-level map for research content by node and content ID
 * - pendingDatabaseOperations: Prevents race conditions during sync
 * - contentFetchInProgress: Set to track ongoing fetches to avoid duplicate requests
 */

import { create } from 'zustand'
import { TreeNode } from '@/app/types'
import toast from 'react-hot-toast'
import { logEventWithContext } from '@/app/libs/logging'
import { parseQuestionsFromMarkdown } from '@/app/(conv)/dragTree/[dragTreeId]/utils/treeHelpers'
import markdownToTreeNode from '@/app/(conv)/dragTree/[dragTreeId]/utils/markdownToTreeNode'
import {
  updateDragTreeNode,
  createDragTreeNode,
  getDragTree,
  updateDragTree,
} from '@/app/server-actions/drag-tree'
import { DragTreeNodeStatus } from '@prisma/client'
import {
  TreeNodeType,
  NodeContentType,
} from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import debounce from 'lodash/debounce'

// Import utility functions
import {
  DatabaseDragTree,
  reconstructTreeFromDatabase,
  convertTreeToDbStructure,
} from './utils/database-utils'
import {
  calculateNodeLevel,
  buildNodeMap,
  collectInterestedNodes,
  resetAllInterestInTree,
  addNodeToTree,
  deleteNodeFromTree,
  editNodeInTree,
  reorderNodeInTree,
  toggleNodeInterest,
  addMultipleNodesToTree,
  validateTreeConstraints,
  collectAllNodeIds,
  getFormattedPath as getFormattedNodePathFromTree,
} from './utils/tree-utils'
import {
  createNodeMetadata,
  createNewTreeNode,
  createNodesFromText,
} from './utils/node-utils'
import { debouncedDbSync } from './utils/sync-utils'
import {
  validateTreeStructure,
  sanitizeTreeStructure,
} from './utils/tree-validation'
import { performanceMonitor } from '@/app/utils/performance-monitor'

import { DragTreeNodeContentStatus } from '@prisma/client'

/**
 * Node Content Item Type
 *
 * Represents AI-generated content associated with tree nodes.
 * Supports multiple content types and versioning for future extensibility.
 *
 * Design Notes:
 * - contentId: Unique identifier for this content instance
 * - contentType: Allows different types of AI-generated content
 * - contentVersion: Enables A/B testing and content evolution
 * - status: Tracks content lifecycle (INITIALIZED -> PROCESSING -> ACTIVE)
 * - metadata: Stores search results, citations, and other structured data
 * - messages: Full conversation history for research continuation
 */
export type NodeContentItem = {
  contentId: string
  contentType: NodeContentType
  contentVersion: string
  status: DragTreeNodeContentStatus
  contentText: string
  metadata: Record<string, any>
  messages?: any[] // For storing conversation history
}

/**
 * Tree Store Type Definition
 *
 * Complete type definition for the dragTree store.
 * Organized into logical sections for better maintainability.
 */
export type TreeStore = {
  // Core State
  frontendTreeStructure: TreeNode | null // Hierarchical tree for UI rendering
  screeningQuestion: string // Context question for research prompts
  dragTreeId: string | null // Database identifier for persistence
  userId: string | null // User identifier for data ownership
  dragTreeTitle: string | null // Human-readable tree title
  preferredLanguage: string | null // Language preference for AI interactions

  // Performance Optimizations
  nodeMap: Map<string, TreeNode> // Flat map for O(1) node access
  pendingDatabaseOperations: Set<string> // Prevents race conditions during sync

  // Content Management - Nested structure: nodeId -> contentId -> ContentItem
  // This allows multiple AI-generated content pieces per node (future versioning)
  nodeContent: Map<string, Map<string, NodeContentItem>>
  researchedNodeIds: Set<string> // New: Track nodes with completed research
  // Ephemeral nodes exist only on FE until user meaningfully uses them (edit/generate)
  ephemeralNodeIds: Set<string>

  // Track ongoing fetches to avoid duplicate requests
  contentFetchInProgress: Set<string>

  // Version counter for read status changes to trigger reactivity
  readVersion: number
  // Fallback unread counts before content is loaded (for immediate badge display)
  initialUnreadCountByNode: Map<string, number>

  // Tree Structure Methods
  setFrontendTreeStructure: (
    data: TreeNode | DatabaseDragTree | null,
    skipSync?: boolean
  ) => void
  setScreeningQuestion: (question: string) => void

  // Node CRUD Operations (with auto-sync to database)
  addNode: (parentId: string, type: TreeNodeType) => void
  deleteNode: (nodeId: string, nodeType: string) => void
  editNode: (nodeId: string, newLabel: string) => void
  reorderNode: (parentId: string, oldIndex: number, newIndex: number) => void

  // AI-Generated Content Addition
  addSimilarQuestions: (parentId: string, questionsMarkdown: string) => void
  addSimilarCategories: (parentId: string, categoriesMarkdown: string) => void

  // Node Utilities
  findNodeById: (nodeId: string) => TreeNode | null
  getNodePath: (nodeId: string) => string
  markNodeAsInterested: (nodeId: string) => void
  getInterestedNodes: () => TreeNode[]
  resetAllInterest: () => void

  // Database Synchronization
  setDragTreeId: (dragTreeId: string, userId: string) => void
  setDragTreeTitle: (title: string | null) => void
  setPreferredLanguage: (language: string | null) => void
  resetDragTreeData: () => void
  loadFromDatabase: () => Promise<void>

  // Internal Utilities
  rebuildNodeMap: () => void // Rebuilds nodeMap from tree structure
  debouncedRebuildNodeMap: () => void // Debounced version to group rapid rebuilds

  // Research Content Management
  addNodeContent: (nodeId: string, contentItem: NodeContentItem) => void
  updateNodeContent: (
    nodeId: string,
    contentId: string,
    updates: Partial<NodeContentItem>
  ) => void
  getNodeContent: (nodeId: string) => Map<string, NodeContentItem> | undefined
  getSpecificContent: (
    nodeId: string,
    contentId: string
  ) => NodeContentItem | undefined
  markNodeAsResearched: (nodeId: string) => void // New: Action to mark node as researched
  // Lazy loading
  fetchNodeContent: (nodeId: string, contentId: string) => Promise<void>
  fetchNodeContentBatch: (
    pairs: Array<{ nodeId: string; contentId: string }>
  ) => Promise<void>

  isContentFetching: (contentId: string) => boolean

  // Read status management
  isContentRead: (nodeId: string, contentId: string) => boolean
  hasUnreadContent: (nodeId: string) => boolean
  countUnreadInCategory: (categoryNode: TreeNode) => number
  markContentAsReadOptimistic: (nodeId: string, contentId: string) => void

  // Fallback unread count management
  setInitialUnreadCounts: (counts: Record<string, number>) => void
  clearInitialUnreadForNodes: (nodeIds: string[]) => void
}

export const useDragTreeStore = create<TreeStore>((set, get) => ({
  // State
  frontendTreeStructure: null,
  screeningQuestion: '',
  dragTreeId: null,
  userId: null,
  dragTreeTitle: null,
  preferredLanguage: null,
  nodeMap: new Map(),
  pendingDatabaseOperations: new Set(),
  nodeContent: new Map(),
  contentFetchInProgress: new Set(),
  researchedNodeIds: new Set(), // New: Initialize as empty set
  ephemeralNodeIds: new Set(),
  readVersion: 0,
  initialUnreadCountByNode: new Map(), // Fallback unread counts for immediate display

  setFrontendTreeStructure: (data, skipSync = false) => {
    const endTiming = performanceMonitor.startTiming('setFrontendTreeStructure')

    console.log(
      '🏗️ [DragTreeStore] setFrontendTreeStructure called with:',
      data
    )

    if (!data) {
      console.log('🏗️ [DragTreeStore] Clearing tree structure')
      // Single batched update to avoid multiple re-renders
      set({
        frontendTreeStructure: null,
        nodeContent: new Map(),
        nodeMap: new Map(),
      })
      endTiming()
      return
    }

    // Check if data is a DatabaseDragTree (has nodes array)
    if (data && typeof data === 'object' && 'nodes' in data) {
      console.log('🏗️ [DragTreeStore] Converting DatabaseDragTree to TreeNode')
      const dbData = data as DatabaseDragTree
      const treeNode = reconstructTreeFromDatabase(dbData)

      if (treeNode) {
        // Validate the converted tree structure
        const validation = validateTreeStructure(treeNode)

        if (!validation.isValid) {
          console.warn(
            '⚠️ [DragTreeStore] Tree validation failed, attempting sanitization:',
            validation.errors
          )

          const sanitizedTree = sanitizeTreeStructure(treeNode)
          if (sanitizedTree) {
            const sanitizedValidation = validateTreeStructure(sanitizedTree)
            if (sanitizedValidation.isValid) {
              console.log('✅ [DragTreeStore] Tree sanitized successfully')
              // Precompute nodeMap and nodeContent, set in one update
              const computedNodeMap = buildNodeMap(sanitizedTree)
              const newNodeContent = new Map<
                string,
                Map<string, NodeContentItem>
              >()
              if (dbData.nodes) {
                dbData.nodes.forEach((node: any) => {
                  if (node.content_items && node.content_items.length > 0) {
                    const nodeContentMap = new Map<string, NodeContentItem>()
                    node.content_items.forEach((contentItem: any) => {
                      if (contentItem.status !== 'INACTIVE') {
                        nodeContentMap.set(contentItem.id, {
                          contentId: contentItem.id,
                          contentType:
                            contentItem.content_type ||
                            NodeContentType.QUICK_RESEARCH,
                          contentVersion: contentItem.content_version || 'v1',
                          status: contentItem.status,
                          contentText: contentItem.content_text || '',
                          metadata: contentItem.content_metadata || {},
                          messages: contentItem.messages || [],
                        })
                      }
                    })
                    if (nodeContentMap.size > 0) {
                      newNodeContent.set(node.id, nodeContentMap)
                    }
                  }
                })
              }
              set({
                frontendTreeStructure: sanitizedTree,
                nodeMap: computedNodeMap,
                nodeContent: newNodeContent,
              })
              endTiming({ nodeCount: sanitizedValidation.stats.totalNodes })
            } else {
              console.error(
                '❌ [DragTreeStore] Tree sanitization failed, using original'
              )
              const computedNodeMap = buildNodeMap(treeNode)
              const newNodeContent = new Map<
                string,
                Map<string, NodeContentItem>
              >()
              if (dbData.nodes) {
                dbData.nodes.forEach((node: any) => {
                  if (node.content_items && node.content_items.length > 0) {
                    const nodeContentMap = new Map<string, NodeContentItem>()
                    node.content_items.forEach((contentItem: any) => {
                      if (contentItem.status !== 'INACTIVE') {
                        nodeContentMap.set(contentItem.id, {
                          contentId: contentItem.id,
                          contentType:
                            contentItem.content_type ||
                            NodeContentType.QUICK_RESEARCH,
                          contentVersion: contentItem.content_version || 'v1',
                          status: contentItem.status,
                          contentText: contentItem.content_text || '',
                          metadata: contentItem.content_metadata || {},
                          messages: contentItem.messages || [],
                        })
                      }
                    })
                    if (nodeContentMap.size > 0) {
                      newNodeContent.set(node.id, nodeContentMap)
                    }
                  }
                })
              }
              set({
                frontendTreeStructure: treeNode,
                nodeMap: computedNodeMap,
                nodeContent: newNodeContent,
              })
              endTiming({
                nodeCount: validation.stats.totalNodes,
                errorCount: 1,
              })
            }
          } else {
            console.error(
              '❌ [DragTreeStore] Tree sanitization returned null, using original'
            )
            const computedNodeMap = buildNodeMap(treeNode)
            const newNodeContent = new Map<
              string,
              Map<string, NodeContentItem>
            >()
            if (dbData.nodes) {
              dbData.nodes.forEach((node: any) => {
                if (node.content_items && node.content_items.length > 0) {
                  const nodeContentMap = new Map<string, NodeContentItem>()
                  node.content_items.forEach((contentItem: any) => {
                    if (contentItem.status !== 'INACTIVE') {
                      nodeContentMap.set(contentItem.id, {
                        contentId: contentItem.id,
                        contentType:
                          contentItem.content_type ||
                          NodeContentType.QUICK_RESEARCH,
                        contentVersion: contentItem.content_version || 'v1',
                        status: contentItem.status,
                        contentText: contentItem.content_text || '',
                        metadata: contentItem.content_metadata || {},
                        messages: contentItem.messages || [],
                      })
                    }
                  })
                  if (nodeContentMap.size > 0) {
                    newNodeContent.set(node.id, nodeContentMap)
                  }
                }
              })
            }
            set({
              frontendTreeStructure: treeNode,
              nodeMap: computedNodeMap,
              nodeContent: newNodeContent,
            })
            endTiming({ nodeCount: validation.stats.totalNodes, errorCount: 1 })
          }
        } else {
          console.log(
            '✅ [DragTreeStore] Successfully converted and validated TreeNode:',
            validation.stats
          )
          const computedNodeMap = buildNodeMap(treeNode)
          const newNodeContent = new Map<string, Map<string, NodeContentItem>>()
          if (dbData.nodes) {
            dbData.nodes.forEach((node: any) => {
              if (node.content_items && node.content_items.length > 0) {
                const nodeContentMap = new Map<string, NodeContentItem>()
                node.content_items.forEach((contentItem: any) => {
                  if (contentItem.status !== 'INACTIVE') {
                    nodeContentMap.set(contentItem.id, {
                      contentId: contentItem.id,
                      contentType:
                        contentItem.content_type ||
                        NodeContentType.QUICK_RESEARCH,
                      contentVersion: contentItem.content_version || 'v1',
                      status: contentItem.status,
                      contentText: contentItem.content_text || '',
                      metadata: contentItem.content_metadata || {},
                      messages: contentItem.messages || [],
                    })
                  }
                })
                if (nodeContentMap.size > 0) {
                  newNodeContent.set(node.id, nodeContentMap)
                }
              }
            })
          }
          set({
            frontendTreeStructure: treeNode,
            nodeMap: computedNodeMap,
            nodeContent: newNodeContent,
          })
          endTiming({ nodeCount: validation.stats.totalNodes })
        }
      } else {
        console.warn('❌ [DragTreeStore] Failed to convert DatabaseDragTree')
        set({
          frontendTreeStructure: null,
          nodeContent: new Map(),
          nodeMap: new Map(),
        })
      }
    } else {
      // Direct TreeNode assignment
      console.log('🏗️ [DragTreeStore] Direct TreeNode assignment')
      const computedNodeMap = buildNodeMap(data as TreeNode)
      set({ frontendTreeStructure: data as TreeNode, nodeMap: computedNodeMap })
    }

    // Note: rebuildNodeMap() already called in each branch above
    // Sync changes with the database unless explicitly skipped (initial load)
    if (!skipSync) {
      debouncedDbSync(async () => {
        const { dragTreeId, frontendTreeStructure } = get()
        if (dragTreeId && frontendTreeStructure) {
          await updateDragTree({
            treeId: dragTreeId,
            treeStructure: convertTreeToDbStructure(frontendTreeStructure),
          })
        }
      })
    }
  },

  setScreeningQuestion: question => set({ screeningQuestion: question }),

  setDragTreeId: (dragTreeId: string, userId: string) => {
    console.log(
      '🔗 [DragTreeStore] setDragTreeId called with:',
      dragTreeId,
      userId
    )

    // If switching to a different drag tree, reset all data first
    const currentDragTreeId = get().dragTreeId
    if (currentDragTreeId && currentDragTreeId !== dragTreeId) {
      console.log(
        '🔄 [DragTreeStore] Switching drag trees, resetting data first'
      )
      get().resetDragTreeData()
    }

    set({ dragTreeId, userId })
  },

  setDragTreeTitle: (title: string | null) => {
    console.log('🏷️ [DragTreeStore] setDragTreeTitle called with:', title)
    set({ dragTreeTitle: title })
  },

  setPreferredLanguage: (language: string | null) => {
    console.log(
      '🌐 [DragTreeStore] setPreferredLanguage called with:',
      language
    )
    set({ preferredLanguage: language })
  },

  resetDragTreeData: () => {
    console.log('🧹 [DragTreeStore] Resetting drag tree data')
    set({
      frontendTreeStructure: null,
      screeningQuestion: '', // Also reset screening question to prevent mixed state
      dragTreeTitle: null,
      preferredLanguage: null,
      nodeMap: new Map(),
      pendingDatabaseOperations: new Set(),
      nodeContent: new Map(),
      contentFetchInProgress: new Set(),
      researchedNodeIds: new Set(), // Reset researchedNodeIds
      readVersion: 0, // Reset read version counter for clean state
      initialUnreadCountByNode: new Map(), // Reset fallback unread counts
    })
  },

  loadFromDatabase: async () => {
    const { dragTreeId, userId } = get()

    if (!dragTreeId || !userId) {
      console.warn(
        '🚫 [DragTreeStore] Cannot load: missing dragTreeId or userId'
      )
      return
    }

    console.log('📡 [DragTreeStore] Loading from database...', dragTreeId)

    try {
      const result = await getDragTree(dragTreeId)

      if (result.success && result.data) {
        console.log(
          '✅ [DragTreeStore] Successfully loaded from database:',
          result.data
        )
        // This is a fresh pull from the DB – no need to immediately write it back
        get().setFrontendTreeStructure(result.data, true)
        // Always set the title (null if not available)
        get().setDragTreeTitle(result.data.title || null)
        // Set the preferred language (null if not available)
        get().setPreferredLanguage(result.data.preferred_language || null)
        // Set the screening question (empty string if not available)
        get().setScreeningQuestion(result.data.user_prompt || '')

        // nodeContent is already set within setFrontendTreeStructure for DatabaseDragTree
      } else {
        console.error(
          '❌ [DragTreeStore] Failed to load from database:',
          result.error
        )
      }
    } catch (error) {
      console.error('💥 [DragTreeStore] Database load error:', error)
    }
  },

  addNode: (parentId, type) => {
    // NOTE: Add-node (category/question) is not exposed in the UI for now.
    // Leave current behavior intact but avoid wiring new flows to it until
    // we migrate to the atomic server action pathway for persistence.
    // When enabling add-node in the future:
    // 1) Keep FE update for responsiveness
    // 2) Persist via a single atomic action (create node(s) + update tree_structure)
    // 3) Validate alignment at transaction end before commit
    console.log(
      `🌱 [DragTreeStore] addNode called: parentId=${parentId}, type=${type}`
    )
    const { frontendTreeStructure, dragTreeId, userId } = get()
    if (!frontendTreeStructure || !dragTreeId) {
      console.warn('❌ [DragTreeStore] Missing tree structure or dragTreeId')
      return
    }

    // Find parent node and calculate level
    const parentNode = get().findNodeById(parentId)
    if (!parentNode) {
      console.warn(`❌ [DragTreeStore] Parent node ${parentId} not found`)
      return
    }

    const parentLevel = calculateNodeLevel(frontendTreeStructure, parentId)
    const newNodeLevel = parentLevel + 1

    // Create new node using utility function
    const newNode = createNewTreeNode(dragTreeId, type)
    const nodeMetadata = createNodeMetadata(parentId, newNodeLevel)

    console.log(`🏗️ [DragTreeStore] Creating new node:`, {
      id: newNode.id,
      label: newNode.label,
      type,
      parentId,
      level: newNodeLevel,
      metadata: nodeMetadata,
    })

    // Add node to tree using utility function
    const newTree = addNodeToTree(frontendTreeStructure, parentId, newNode)
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to add node to tree')
      return
    }

    set(state => {
      const newMap = new Map(state.nodeMap)
      newMap.set(newNode.id, newNode)
      if (type === TreeNodeType.CATEGORY && newNode.children.length > 0) {
        const child = newNode.children[0]
        newMap.set(child.id, child)
      }
      return { frontendTreeStructure: newTree, nodeMap: newMap }
    })
    toast.success(
      `Added new ${type}${
        type === TreeNodeType.CATEGORY ? ' with question' : ''
      }`
    )

    // Database sync - IMMEDIATE atomic operations for node addition
    if (dragTreeId && userId) {
      console.log(
        `💾 [DragTreeStore] Syncing new ${type} node: ${newNode.id} (IMMEDIATE)`
      )

      // Track this operation to prevent overwrites
      const operationKey = `addNode:${newNode.id}`
      const currentPending = get().pendingDatabaseOperations
      currentPending.add(operationKey)
      set({ pendingDatabaseOperations: new Set(currentPending) })

      // Immediate async operation (no debouncing for critical operations)
      ;(async () => {
        try {
          // Prepare database operations
          const dbOperations: Promise<any>[] = [
            // Create the new node in database with metadata
            createDragTreeNode({
              dragTreeId,
              nodeType:
                newNode.type === TreeNodeType.CATEGORY
                  ? 'CATEGORY'
                  : 'QUESTION',
              label: newNode.label,
              nodeId: newNode.id,
              metadata: nodeMetadata,
            }),
          ]

          // If category, also create the child question
          if (type === TreeNodeType.CATEGORY && newNode.children.length > 0) {
            const childQuestion = newNode.children[0]
            const childQuestionMetadata = createNodeMetadata(
              newNode.id,
              newNodeLevel + 1
            )

            dbOperations.push(
              createDragTreeNode({
                dragTreeId,
                nodeType: 'QUESTION',
                label: childQuestion.label,
                nodeId: childQuestion.id,
                metadata: childQuestionMetadata,
              })
            )
          }

          // Add tree structure update
          dbOperations.push(
            updateDragTree({
              treeId: dragTreeId,
              treeStructure: convertTreeToDbStructure(newTree),
            })
          )

          // Execute all operations atomically
          const results = await Promise.all(dbOperations)
          const nodeResult = results[0]
          const treeResult = results[results.length - 1]

          if (nodeResult.success && treeResult.success) {
            console.log(
              `✅ [DragTreeStore] Successfully synced new ${type} node: ${newNode.id}`
            )
          } else {
            console.error(
              `❌ [DragTreeStore] Failed to sync new node:`,
              nodeResult.error || treeResult.error
            )
            toast.error('Failed to save new node')
          }
        } catch (error) {
          console.error(`❌ [DragTreeStore] Sync error:`, error)
          toast.error('Failed to save new node')
        } finally {
          // Remove the pending operation tracking
          const finalPending = get().pendingDatabaseOperations
          finalPending.delete(operationKey)
          set({ pendingDatabaseOperations: new Set(finalPending) })
          console.log(
            `🧹 [DragTreeStore] Removed pending operation: ${operationKey}`
          )
        }
      })()
    }
  },

  deleteNode: (nodeId, nodeType) => {
    console.log('🗑️ [DragTreeStore] deleteNode called with:', nodeId, nodeType)
    const { frontendTreeStructure, dragTreeId, nodeMap, userId } = get()

    if (!frontendTreeStructure || !dragTreeId) {
      console.warn(
        '❌ [DragTreeStore] Early return - missing frontendTreeStructure or dragTreeId'
      )
      return
    }

    // Validate constraints before deletion
    const validation = validateTreeConstraints(
      frontendTreeStructure,
      nodeId,
      'delete'
    )
    if (!validation.valid) {
      console.warn('❌ [DragTreeStore] Validation failed:', validation.message)
      toast.error(validation.message || 'Cannot delete node')
      return
    }

    // Find the node to collect all affected node IDs
    const nodeToDelete = nodeMap.get(nodeId)
    if (!nodeToDelete) {
      console.warn(`Node ${nodeId} not found`)
      toast.error('Node not found')
      return
    }

    const nodesToMarkInactive = collectAllNodeIds(nodeToDelete)

    // Delete node using utility function
    const prevTree = frontendTreeStructure
    const newTree = deleteNodeFromTree(frontendTreeStructure, nodeId)
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to delete node from tree')
      toast.error('Failed to delete node')
      return
    }

    console.log(
      '✅ [DragTreeStore] Successfully deleted node from tree, updating state'
    )
    set(state => {
      const newMap = new Map(state.nodeMap)
      nodesToMarkInactive.forEach(id => newMap.delete(id))
      return { frontendTreeStructure: newTree, nodeMap: newMap }
    })

    const deletedCount = nodesToMarkInactive.length
    toast.success(
      `Deleted ${nodeType}${
        deletedCount > 1
          ? ` and ${deletedCount - 1} child node${deletedCount > 2 ? 's' : ''}`
          : ''
      }`
    )

    // Database sync (atomic) with rollback on failure
    if (dragTreeId && userId) {
      ;(async () => {
        try {
          const { applyTreeUpdateAtomic } = await import(
            '@/app/server-actions/drag-tree/atomic'
          )
          await applyTreeUpdateAtomic({
            treeId: dragTreeId,
            updateStatuses: nodesToMarkInactive.map(id => ({
              id,
              status: DragTreeNodeStatus.INACTIVE,
            })),
            nextTreeStructure: convertTreeToDbStructure(newTree),
          })
          console.log('✅ [DragTreeStore] Deletion persisted atomically')
        } catch (error) {
          console.error(
            '💥 [DragTreeStore] Atomic delete failed, reverting:',
            error
          )
          // Revert UI to previous tree
          set({ frontendTreeStructure: prevTree })
          get().rebuildNodeMap()
          toast.error('Delete failed; changes reverted')
        }
      })()
    }
  },

  editNode: (nodeId, newLabel) => {
    console.log('✏️ [DragTreeStore] editNode called:', nodeId, newLabel)
    const { frontendTreeStructure, dragTreeId, userId } = get()
    if (!frontendTreeStructure) return

    // Get the old label before editing for logging
    const oldNode = get().findNodeById(nodeId)
    const oldLabel = oldNode?.label || ''

    // Edit node using utility function
    const newTree = editNodeInTree(frontendTreeStructure, nodeId, newLabel)
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to edit node')
      return
    }

    // Log the outline title edit event
    logEventWithContext(
      'edit_outline_title',
      userId || undefined,
      dragTreeId || undefined,
      {
        old_title: oldLabel,
        new_title: newLabel,
        title_length: newLabel.length,
      }
    )

    set(state => {
      const newMap = new Map(state.nodeMap)
      const node = newMap.get(nodeId)
      if (node) {
        node.label = newLabel
        newMap.set(nodeId, node)
      }
      return { frontendTreeStructure: newTree, nodeMap: newMap }
    })
    toast.success(`Updated label to "${newLabel}"`)

    // Persist behavior: if node is ephemeral, promote using atomic create + structure
    const state = get()
    const isEphemeral = state.ephemeralNodeIds.has(nodeId)
    if (dragTreeId && userId && isEphemeral) {
      ;(async () => {
        try {
          const { applyTreeUpdateAtomic } = await import(
            '@/app/server-actions/drag-tree/atomic'
          )
          const node = state.nodeMap.get(nodeId)
          if (!node) return
          await applyTreeUpdateAtomic({
            treeId: dragTreeId,
            creates: [
              {
                id: nodeId,
                label: newLabel,
                node_type:
                  node.type === 'category'
                    ? ('CATEGORY' as const)
                    : ('QUESTION' as const),
              },
              // If this is a category with an auto child, we should include it as well
              ...(node.type === 'category' && node.children[0]
                ? [
                    {
                      id: node.children[0].id,
                      label: node.children[0].label,
                      node_type: 'QUESTION' as const,
                    },
                  ]
                : []),
            ],
            nextTreeStructure: convertTreeToDbStructure(newTree),
          })
          // Clear ephemeral status for promoted node (and auto child)
          set(cur => {
            const next = new Set(cur.ephemeralNodeIds)
            next.delete(nodeId)
            if (node.type === 'category' && node.children[0]) {
              next.delete(node.children[0].id)
            }
            return { ephemeralNodeIds: next }
          })
          console.log('✅ [DragTreeStore] Ephemeral node promoted:', nodeId)
        } catch (error) {
          console.error('💥 [DragTreeStore] Promote ephemeral failed:', error)
          toast.error('Save failed; try again')
        }
      })()
    } else if (dragTreeId && userId) {
      // Regular label update for non-ephemeral nodes
      console.log('💾 [DragTreeStore] Syncing label update for:', nodeId)
      debouncedDbSync(async () => {
        try {
          const nodeResult = await updateDragTreeNode({
            nodeId,
            label: newLabel,
          })
          if (!nodeResult.success) {
            console.error('❌ [DragTreeStore] Failed to sync label update')
            toast.error('Failed to save changes')
          }
        } catch (error) {
          console.error('💥 [DragTreeStore] Edit sync error:', error)
          toast.error('Failed to save changes')
        }
      })
    }
  },

  reorderNode: (parentId, oldIndex, newIndex) => {
    const { frontendTreeStructure, dragTreeId } = get()
    if (!frontendTreeStructure) return

    const prevTree = frontendTreeStructure
    const newTree = reorderNodeInTree(
      frontendTreeStructure,
      parentId,
      oldIndex,
      newIndex
    )

    if (newTree) {
      set({ frontendTreeStructure: newTree })
      if (dragTreeId) {
        ;(async () => {
          try {
            const { applyTreeUpdateAtomic } = await import(
              '@/app/server-actions/drag-tree/atomic'
            )
            await applyTreeUpdateAtomic({
              treeId: dragTreeId,
              nextTreeStructure: convertTreeToDbStructure(newTree),
            })
          } catch (error) {
            console.error(
              '💥 [DragTreeStore] Reorder persist failed, reverting:',
              error
            )
            set({ frontendTreeStructure: prevTree })
            get().rebuildNodeMap()
            toast.error('Reorder failed; changes reverted')
          }
        })()
      }
    }
  },

  markNodeAsInterested: nodeId => {
    console.log('🔍 [DragTreeStore] markNodeAsInterested called with:', nodeId)
    const { frontendTreeStructure, dragTreeId, userId } = get()
    if (!frontendTreeStructure) return

    // Toggle interest using utility function
    const newTree = toggleNodeInterest(frontendTreeStructure, nodeId)
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to toggle node interest')
      return
    }

    set(state => {
      const newMap = new Map(state.nodeMap)
      const updatedNode = get().findNodeById(nodeId)
      if (updatedNode) {
        newMap.set(nodeId, updatedNode)
      }
      return { frontendTreeStructure: newTree, nodeMap: newMap }
    })

    const nodeAfterUpdate = get().findNodeById(nodeId)
    const action = nodeAfterUpdate?.isInterestedIn
      ? 'marked as interested'
      : 'unmarked'
    console.log('🔍 [DragTreeStore] Node after update:', action)

    // Database sync with graceful error handling
    if (dragTreeId && userId) {
      const finalInterestStatus = nodeAfterUpdate?.isInterestedIn ?? false
      console.log(
        '💾 [DragTreeStore] Syncing interest status for:',
        nodeId,
        '-> isInterestedIn:',
        finalInterestStatus
      )
      debouncedDbSync(async () => {
        try {
          const result = await updateDragTreeNode({
            nodeId,
            isInterestedIn: finalInterestStatus,
          })

          if (result.success) {
            console.log(
              '✅ [DragTreeStore] Successfully synced interest status'
            )
          } else {
            console.error('❌ [DragTreeStore] Failed to sync interest status')
            toast.error('Failed to save interest status')
          }
        } catch (error) {
          console.error('💥 [DragTreeStore] Interest sync error:', error)
          toast.error('Failed to save interest status')
        }
      })
    }
  },

  resetAllInterest: () => {
    console.log('🔄 [DragTreeStore] resetAllInterest called')
    const { frontendTreeStructure } = get()
    if (!frontendTreeStructure) return

    // Reset all interest using utility function
    const newTree = resetAllInterestInTree(frontendTreeStructure)
    const computedNodeMap = buildNodeMap(newTree)
    set({ frontendTreeStructure: newTree, nodeMap: computedNodeMap })
    toast.success('All interest markers cleared')
  },

  getInterestedNodes: () => {
    const { frontendTreeStructure } = get()
    return collectInterestedNodes(frontendTreeStructure)
  },

  findNodeById: nodeId => {
    return get().nodeMap.get(nodeId) || null
  },

  getNodePath: nodeId => {
    const tree = get().frontendTreeStructure
    if (!tree) return ''
    return getFormattedNodePathFromTree(tree, nodeId)
  },

  addSimilarQuestions: (parentId, questionsMarkdown) => {
    // TODO (persistence): When we re-enable structural editing,
    // route persistence through applyTreeUpdateAtomic (create + tree_structure)
    console.log(
      '❓ [DragTreeStore] addSimilarQuestions called for parent:',
      parentId
    )
    const { frontendTreeStructure, dragTreeId } = get()
    if (!frontendTreeStructure) return

    const questions = parseQuestionsFromMarkdown(questionsMarkdown)
    if (questions.length === 0) return

    console.log('📝 [DragTreeStore] Parsed questions:', questions)

    const effectiveTreeId = dragTreeId ?? 'current'
    const questionNodes = createNodesFromText(
      effectiveTreeId,
      questions,
      TreeNodeType.QUESTION
    )

    const newTree = addMultipleNodesToTree(
      frontendTreeStructure,
      parentId,
      questionNodes
    )
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to add question nodes')
      return
    }

    set(state => {
      const newMap = new Map(state.nodeMap)
      const nextEphemeral = new Set(state.ephemeralNodeIds)
      questionNodes.forEach(node => {
        newMap.set(node.id, node)
        nextEphemeral.add(node.id)
      })
      return {
        frontendTreeStructure: newTree,
        nodeMap: newMap,
        ephemeralNodeIds: nextEphemeral,
      }
    })

    // Do NOT persist immediately. These are ephemeral until user edits/uses them.
  },

  addSimilarCategories: (parentId, categoriesMarkdown) => {
    // TODO (persistence): When we re-enable structural editing,
    // route persistence through applyTreeUpdateAtomic (create + tree_structure)
    console.log(
      '📁 [DragTreeStore] addSimilarCategories called for parent:',
      parentId
    )
    const { frontendTreeStructure } = get()
    if (!frontendTreeStructure) return

    // Convert markdown to tree nodes
    const subtree = markdownToTreeNode(categoriesMarkdown, 1)
    if (!subtree || subtree.children.length === 0) {
      console.warn('❌ [DragTreeStore] No valid categories found in markdown')
      return
    }

    console.log('📝 [DragTreeStore] Parsed categories:', subtree.children)

    // Add categories to tree using utility function
    const newTree = addMultipleNodesToTree(
      frontendTreeStructure,
      parentId,
      subtree.children
    )
    if (!newTree) {
      console.warn('❌ [DragTreeStore] Failed to add similar categories')
      return
    }

    set(state => {
      const newMap = new Map(state.nodeMap)
      const nextEphemeral = new Set(state.ephemeralNodeIds)
      const addRecursively = (node: TreeNode) => {
        newMap.set(node.id, node)
        nextEphemeral.add(node.id)
        node.children.forEach(addRecursively)
      }
      subtree.children.forEach(addRecursively)
      return {
        frontendTreeStructure: newTree,
        nodeMap: newMap,
        ephemeralNodeIds: nextEphemeral,
      }
    })
    toast.success(`Added new categories with questions`)

    // Do NOT persist immediately; nodes remain ephemeral until meaningful use.
  },

  markNodeAsResearched: (nodeId: string) => {
    set(state => ({
      researchedNodeIds: new Set(state.researchedNodeIds).add(nodeId),
    }))
  },

  // Internal helper method to rebuild the flat map
  rebuildNodeMap: () => {
    const { frontendTreeStructure } = get()
    const newNodeMap = buildNodeMap(frontendTreeStructure)

    set({ nodeMap: newNodeMap })
    console.log(
      `🗂️ [DragTreeStore] Rebuilt node map with ${newNodeMap.size} nodes`
    )
  },

  // Node content methods - thread-safe for concurrent operations
  addNodeContent: (nodeId, contentItem) => {
    set(state => {
      const newNodeContent = new Map(state.nodeContent)
      const currentNodeContent = newNodeContent.get(nodeId) || new Map()
      const newNodeContentMap = new Map(currentNodeContent)

      newNodeContentMap.set(contentItem.contentId, contentItem)
      newNodeContent.set(nodeId, newNodeContentMap)

      console.log(
        `📝 [DragTreeStore] Added content ${contentItem.contentId} to node ${nodeId}`
      )
      return { nodeContent: newNodeContent }
    })
  },

  updateNodeContent: async (
    nodeId: string,
    contentId: string,
    updates: Partial<NodeContentItem>
  ) => {
    // Optimistically update the store for a responsive UI
    set(state => {
      const nodeContent = state.nodeContent.get(nodeId)
      if (!nodeContent) {
        console.warn(
          `[DragTreeStore] Attempted to update content for non-existent node: ${nodeId}`
        )
        return state
      }

      const currentContent = nodeContent.get(contentId)
      if (!currentContent) {
        console.warn(
          `[DragTreeStore] Attempted to update non-existent content: ${contentId}`
        )
        return state
      }

      // Check if read status is changing from false to true
      const prevIsRead = currentContent.metadata?.isRead === true

      // Ensure metadata merges when provided (handles undefined vs explicit metadata)
      const nextMetadata =
        updates.metadata !== undefined
          ? { ...currentContent.metadata, ...updates.metadata }
          : currentContent.metadata

      const updatedContent = {
        ...currentContent,
        ...updates,
        metadata: nextMetadata,
      }

      const nextIsRead = nextMetadata?.isRead === true
      const shouldBumpReadVersion = !prevIsRead && nextIsRead
      const newNodeContentMap = new Map(nodeContent)
      newNodeContentMap.set(contentId, updatedContent)

      const newNodeContent = new Map(state.nodeContent)
      newNodeContent.set(nodeId, newNodeContentMap)

      console.log(
        `📝 [DragTreeStore] Optimistically updated content ${contentId} for node ${nodeId}${shouldBumpReadVersion ? ' (marked as read)' : ''}`
      )

      return {
        nodeContent: newNodeContent,
        ...(shouldBumpReadVersion
          ? { readVersion: state.readVersion + 1 }
          : {}),
      }
    })

    // If the update includes text content, persist it to the database
    if (updates.contentText !== undefined) {
      try {
        // Inline server action - no separate function needed
        const { updateDragTreeNodeContent } = await import(
          '@/app/server-actions/drag-tree/research-update'
        )

        const result = await updateDragTreeNodeContent(
          contentId,
          updates.contentText
        )

        if (!result.success) {
          throw new Error(result.error || 'Failed to save content to database')
        }

        console.log(
          `✅ [DragTreeStore] Successfully persisted content ${contentId}`
        )
      } catch (error) {
        console.error(
          `❌ [DragTreeStore] Failed to persist content ${contentId}:`,
          error
        )
        // TODO: Implement error handling, e.g., show a toast, revert optimistic update
      }
    }
  },

  getNodeContent: nodeId => {
    return get().nodeContent.get(nodeId)
  },

  getSpecificContent: (nodeId, contentId) => {
    const nodeContentMap = get().nodeContent.get(nodeId)
    return nodeContentMap?.get(contentId)
  },

  // Lazy loading
  fetchNodeContent: async (nodeId: string, contentId: string) => {
    // Avoid fetching if content already loaded or currently loading
    const { nodeContent, contentFetchInProgress } = get()

    const nodeContentMap = nodeContent.get(nodeId)
    const currentContent = nodeContentMap?.get(contentId)

    // Only skip fetching if we have both content text AND messages
    // This ensures we fetch messages even if content text exists
    if (
      currentContent &&
      currentContent.contentText.trim().length > 0 &&
      currentContent.messages &&
      Array.isArray(currentContent.messages) &&
      currentContent.messages.length > 0
    ) {
      console.log(
        `[DragTreeStore] Content ${contentId} already fully loaded (text + messages)`
      )
      return
    }

    // Log why we're fetching
    if (currentContent) {
      console.log(`[DragTreeStore] Re-fetching content ${contentId} because:`, {
        hasText: currentContent.contentText.trim().length > 0,
        hasMessages:
          currentContent.messages &&
          Array.isArray(currentContent.messages) &&
          currentContent.messages.length > 0,
        messagesLength: Array.isArray(currentContent.messages)
          ? currentContent.messages.length
          : 0,
      })
    }

    try {
      // Check if the content is already being fetched
      if (contentFetchInProgress.has(contentId)) {
        console.log(
          `[DragTreeStore] Content ${contentId} is already being fetched`
        )
        return
      }

      // Mark the content as being fetched
      set(state => {
        const newContentFetchInProgress = new Set(state.contentFetchInProgress)
        newContentFetchInProgress.add(contentId)
        return { contentFetchInProgress: newContentFetchInProgress }
      })

      const res = await fetch(`/api/dragtree/content?contentId=${contentId}`)
      const json = await res.json()
      if (json.success && json.data?.content_text) {
        get().updateNodeContent(nodeId, contentId, {
          contentText: json.data.content_text as string,
          metadata: json.data.content_metadata || {},
          messages: json.data.messages || [],
        })

        // Clear fallback count for this node since we now have real content data
        set(state => {
          const next = new Map(state.initialUnreadCountByNode)
          next.delete(nodeId)
          return { initialUnreadCountByNode: next }
        })
      } else {
        console.error('[DragTreeStore] Failed to fetch content', json.error)
      }
    } catch (err) {
      console.error('[DragTreeStore] Error fetching content:', err)
    } finally {
      // Remove the content from being fetched
      set(state => {
        const newContentFetchInProgress = new Set(state.contentFetchInProgress)
        newContentFetchInProgress.delete(contentId)
        return { contentFetchInProgress: newContentFetchInProgress }
      })
    }
  },

  fetchNodeContentBatch: async pairs => {
    // Deduplicate by contentId
    const uniquePairs = pairs.reduce(
      (acc, p) => {
        if (!acc.some(a => a.contentId === p.contentId)) acc.push(p)
        return acc
      },
      [] as typeof pairs
    )

    const { contentFetchInProgress } = get()
    const toFetch = uniquePairs.filter(
      p => !contentFetchInProgress.has(p.contentId)
    )
    if (toFetch.length === 0) return

    // Mark as in progress
    set(state => {
      const newContentFetchInProgress = new Set(state.contentFetchInProgress)
      toFetch.forEach(p => newContentFetchInProgress.add(p.contentId))
      return { contentFetchInProgress: newContentFetchInProgress }
    })

    try {
      const { getNodeContentsBatch } = await import(
        '@/app/server-actions/drag-tree/get_node_contents_batch'
      )

      const data = await getNodeContentsBatch(toFetch.map(p => p.contentId))

      // Update store
      set(state => {
        const newNodeContent = new Map(state.nodeContent)
        data.forEach(item => {
          const nodeId = item.node_id
          const nodeMap = newNodeContent.get(nodeId) || new Map()
          nodeMap.set(item.id, {
            contentId: item.id,
            contentType: NodeContentType.QUICK_RESEARCH,
            contentVersion: 'v1',
            status: 'ACTIVE',
            contentText: item.content_text,
            metadata: item.content_metadata,
            messages: item.messages,
          })
          newNodeContent.set(nodeId, nodeMap)
        })

        // Clear fallback counts for nodes that now have real content data
        const nodeIds = Array.from(new Set(data.map(item => item.node_id)))
        const nextInitialUnreadCountByNode = new Map(
          state.initialUnreadCountByNode
        )
        nodeIds.forEach(id => nextInitialUnreadCountByNode.delete(id))

        return {
          nodeContent: newNodeContent,
          initialUnreadCountByNode: nextInitialUnreadCountByNode,
        }
      })
    } catch (err) {
      console.error('[DragTreeStore] Batch fetch failed', err)
    } finally {
      set(state => {
        const newContentFetchInProgress = new Set(state.contentFetchInProgress)
        toFetch.forEach(p => newContentFetchInProgress.delete(p.contentId))
        return { contentFetchInProgress: newContentFetchInProgress }
      })
    }
  },

  // Internal Utilities
  // debouncedRebuildNodeMap is attached after store creation; this is a temporary no-op for type safety
  debouncedRebuildNodeMap: () => {},

  isContentFetching: (contentId: string) => {
    const { contentFetchInProgress } = get()
    return contentFetchInProgress.has(contentId)
  },

  // Read status helper functions
  isContentRead: (nodeId: string, contentId: string) => {
    const nodeContentMap = get().nodeContent.get(nodeId)
    const content = nodeContentMap?.get(contentId)
    return content?.metadata?.isRead === true
  },

  hasUnreadContent: (nodeId: string) => {
    const nodeContentMap = get().nodeContent.get(nodeId)
    if (nodeContentMap) {
      // Check if any content item is unread (content is loaded)
      const contentItems = Array.from(nodeContentMap.values())
      for (const content of contentItems) {
        if (content.status === 'ACTIVE' && content.metadata?.isRead !== true) {
          return true
        }
      }
      return false
    }
    // Fallback to initial counts before content is fetched
    return (get().initialUnreadCountByNode.get(nodeId) || 0) > 0
  },

  countUnreadInCategory: (categoryNode: TreeNode) => {
    let unreadCount = 0

    const countUnreadInNode = (node: TreeNode) => {
      if (node.type === 'question') {
        // Check if this question node has unread content
        if (get().hasUnreadContent(node.id)) {
          unreadCount++
        }
      } else if (node.type === 'category' && node.children) {
        // Recursively check children
        node.children.forEach(countUnreadInNode)
      }
    }

    // Count unread items in all descendants
    if (categoryNode.children) {
      categoryNode.children.forEach(countUnreadInNode)
    }

    return unreadCount
  },

  markContentAsReadOptimistic: (nodeId: string, contentId: string) => {
    // Use the centralized updateNodeContent for consistent read status handling
    get().updateNodeContent(nodeId, contentId, {
      metadata: { isRead: true },
    })

    // Also decrement fallback if present and content not yet loaded
    set(state => {
      const hasLoaded = state.nodeContent.get(nodeId)?.has(contentId)
      if (hasLoaded) return {}
      const current = state.initialUnreadCountByNode.get(nodeId) || 0
      if (current <= 0) return {}
      const next = new Map(state.initialUnreadCountByNode)
      next.set(nodeId, Math.max(0, current - 1))
      return { initialUnreadCountByNode: next }
    })

    console.log(
      `📖 [DragTreeStore] Optimistically marked content ${contentId} as read`
    )
  },

  // Fallback unread count management
  setInitialUnreadCounts: (counts: Record<string, number>) => {
    set(() => ({
      initialUnreadCountByNode: new Map(
        Object.entries(counts).map(([k, v]) => [k, Number(v) || 0])
      ),
    }))
  },

  clearInitialUnreadForNodes: (nodeIds: string[]) => {
    set(state => {
      const next = new Map(state.initialUnreadCountByNode)
      nodeIds.forEach(id => next.delete(id))
      return { initialUnreadCountByNode: next }
    })
  },
}))

// Create debounced version of rebuildNodeMap outside the store to avoid recreating it
const createDebouncedRebuildNodeMap = (rebuildFn: () => void) =>
  debounce(rebuildFn, 50) // 50ms debounce - groups rapid updates

// Add debounced functionality to the store
const debouncedRebuildNodeMap = createDebouncedRebuildNodeMap(() =>
  useDragTreeStore.getState().rebuildNodeMap()
)

// Enhance the store state with the debounced method
useDragTreeStore.setState(state => ({
  ...state,
  debouncedRebuildNodeMap,
}))
