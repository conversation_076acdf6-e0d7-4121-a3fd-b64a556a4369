/**
 * @jest-environment node
 */
import {
  normalizeProviderChunk,
  mergeReasoningMetadata,
} from '../step-normalizer'

describe('step-normalizer', () => {
  it('normalizes tool-call chunks', () => {
    const metadata = normalizeProviderChunk({
      type: 'tool-call',
      toolCallId: 'call_1',
      toolName: 'web_search',
      args: { query: '  hello world  ' },
    })

    expect(metadata.toolCallId).toBe('call_1')
    expect(metadata.toolName).toBe('web_search')
    expect(metadata.args).toEqual({ query: '  hello world  ' })
    expect(metadata.providerChunkType).toBe('tool-call')
  })

  it('normalizes tool-result chunks with success flag', () => {
    const metadata = normalizeProviderChunk({
      type: 'tool-result',
      toolCallId: 'call_1',
      toolName: 'web_search',
      result: { value: 'example' },
    })

    expect(metadata.success).toBe(true)
    expect(metadata.result).toEqual({ value: 'example' })
    expect(metadata.resultPreview).toContain('example')
  })

  it('captures errors for tool-result chunks', () => {
    const metadata = normalizeProviderChunk({
      type: 'tool-result',
      toolCallId: 'call_1',
      toolName: 'web_search',
      errorText: 'timeout',
    })

    expect(metadata.success).toBe(false)
    expect(metadata.error).toBe('timeout')
  })

  it('merges reasoning metadata', () => {
    const merged = mergeReasoningMetadata(
      {
        providerChunkType: 'reasoning',
        timestamp: 123,
        reasoningText: 'first ',
        summary: 'A',
      },
      {
        providerChunkType: 'reasoning-delta',
        timestamp: 124,
        reasoningText: 'second',
        summary: 'B',
      }
    )

    expect(merged.reasoningText).toBe('first second')
    expect(merged.summary).toBe('A\nB')
  })
})
