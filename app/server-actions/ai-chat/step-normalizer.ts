import type { NormalizedExecutionStepMetadata } from './types'

const DEFAULT_TIMESTAMP = () => Date.now()
const MAX_STRING_LENGTH = 400
const MAX_ARRAY_LENGTH = 5
const MAX_OBJECT_KEYS = 12
const MAX_PREVIEW_LENGTH = 600

const isPlainObject = (value: unknown): value is Record<string, unknown> => {
  if (!value || typeof value !== 'object') return false
  return Object.getPrototypeOf(value) === Object.prototype
}

const truncateString = (
  value: string,
  maxLength: number = MAX_STRING_LENGTH
) => {
  if (value.length <= maxLength) return value
  return `${value.slice(0, maxLength)}…`
}

const pruneValue = (value: unknown, depth: number = 0): unknown => {
  if (value === null || value === undefined) return value
  if (depth > 3) {
    if (Array.isArray(value)) return `[array(${value.length}) truncated]`
    if (typeof value === 'object') return '[object truncated]'
    if (typeof value === 'string') return truncateString(value)
    return value
  }

  if (typeof value === 'string') {
    return truncateString(value)
  }

  if (Array.isArray(value)) {
    const sliced = value.slice(0, MAX_ARRAY_LENGTH)
    return sliced.map(item => pruneValue(item, depth + 1))
  }

  if (isPlainObject(value)) {
    const entries = Object.entries(value).slice(0, MAX_OBJECT_KEYS)
    return entries.reduce<Record<string, unknown>>((acc, [key, val]) => {
      acc[key] = pruneValue(val, depth + 1)
      return acc
    }, {})
  }

  return value
}

const buildResultPreview = (result: unknown): string | undefined => {
  if (result === null || result === undefined) return undefined
  if (typeof result === 'string')
    return truncateString(result, MAX_PREVIEW_LENGTH)

  try {
    const json = JSON.stringify(pruneValue(result), null, 2)
    return truncateString(json, MAX_PREVIEW_LENGTH)
  } catch {
    return undefined
  }
}

export const normalizeProviderChunk = (
  chunk: unknown
): NormalizedExecutionStepMetadata => {
  if (!chunk || typeof chunk !== 'object') {
    return {
      providerChunkType: 'unknown',
      timestamp: DEFAULT_TIMESTAMP(),
    }
  }

  const typedChunk = chunk as Record<string, any>
  const providerType =
    typeof typedChunk.type === 'string' ? typedChunk.type : 'unknown'

  const timestamp =
    typeof typedChunk.timestamp === 'number'
      ? typedChunk.timestamp
      : DEFAULT_TIMESTAMP()

  if (providerType === 'tool-call') {
    const args = typedChunk.args ?? typedChunk.input
    return {
      providerChunkType: providerType,
      timestamp,
      toolCallId: typedChunk.toolCallId ?? typedChunk.id,
      toolName: typedChunk.toolName ?? typedChunk.name ?? 'tool',
      args: isPlainObject(args)
        ? (pruneValue(args) as Record<string, unknown>)
        : undefined,
    }
  }

  if (providerType === 'tool-result') {
    const result = typedChunk.result ?? typedChunk.output
    const error = typedChunk.errorText ?? typedChunk.error
    return {
      providerChunkType: providerType,
      timestamp,
      toolCallId: typedChunk.toolCallId ?? typedChunk.id,
      toolName: typedChunk.toolName ?? typedChunk.name ?? 'tool',
      result: result !== undefined ? pruneValue(result) : undefined,
      resultPreview: buildResultPreview(result),
      success: !error,
      error: typeof error === 'string' ? truncateString(error) : undefined,
    }
  }

  if (providerType === 'reasoning-delta' || providerType === 'thinking') {
    const text = typedChunk.textDelta ?? typedChunk.text ?? ''
    return {
      providerChunkType: providerType,
      timestamp,
      reasoningText:
        typeof text === 'string'
          ? truncateString(text, MAX_PREVIEW_LENGTH)
          : undefined,
      summary:
        typeof typedChunk.summary === 'string'
          ? truncateString(typedChunk.summary, MAX_PREVIEW_LENGTH)
          : undefined,
    }
  }

  if (providerType === 'reasoning') {
    return {
      providerChunkType: providerType,
      timestamp,
      summary:
        typeof typedChunk.text === 'string'
          ? truncateString(typedChunk.text, MAX_PREVIEW_LENGTH)
          : undefined,
    }
  }

  return {
    providerChunkType: providerType,
    timestamp,
  }
}

export const mergeReasoningMetadata = (
  previous: NormalizedExecutionStepMetadata | undefined,
  next: NormalizedExecutionStepMetadata
): NormalizedExecutionStepMetadata => {
  if (!previous) return next

  if (next.reasoningText && previous.reasoningText) {
    next.reasoningText = truncateString(
      `${previous.reasoningText}${next.reasoningText}`,
      MAX_PREVIEW_LENGTH
    )
  } else if (previous.reasoningText && !next.reasoningText) {
    next.reasoningText = previous.reasoningText
  }

  if (next.summary && previous.summary) {
    next.summary = truncateString(
      `${previous.summary}\n${next.summary}`,
      MAX_PREVIEW_LENGTH
    )
  } else if (previous.summary && !next.summary) {
    next.summary = previous.summary
  }

  return { ...previous, ...next }
}
