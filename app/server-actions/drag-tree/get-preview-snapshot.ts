'use server'

import prisma from '@/app/libs/prismadb'
import {
  DragTreeNodeContentStatus,
  DragTreeNodeContentType,
  DragTreeNodeType,
} from '@prisma/client'

export type DragTreePreviewSnapshot = {
  dragTreeId: string
  title?: string | null
  treeStructure: any
  nodes: Array<{
    id: string
    label: string
    node_type: DragTreeNodeType
  }>
  contents: Array<{
    id: string
    drag_tree_node_id: string
    status: DragTreeNodeContentStatus
    content_text: string | null
  }>
}

type ParsedTreeStructure = {
  root_id?: string
  hierarchy: Record<string, string[]>
}

const parseTreeStructure = (value: unknown): ParsedTreeStructure => {
  if (!value || typeof value !== 'object') {
    return { hierarchy: {} }
  }

  const record = value as Record<string, unknown>
  const rootId = typeof record.root_id === 'string' ? record.root_id : undefined

  const hierarchy: Record<string, string[]> = {}
  if (record.hierarchy && typeof record.hierarchy === 'object') {
    Object.entries(record.hierarchy as Record<string, unknown>).forEach(
      ([parentId, children]) => {
        if (!Array.isArray(children)) return
        hierarchy[parentId] = children.flatMap(child =>
          typeof child === 'string' ? [child] : []
        )
      }
    )
  }

  return { root_id: rootId, hierarchy }
}

export async function getResearchPreviewData(
  dragTreeId: string
): Promise<DragTreePreviewSnapshot> {
  const dragTree = await prisma.dragTree.findUnique({
    where: {
      id: dragTreeId,
    },
    select: {
      id: true,
      title: true,
      tree_structure: true,
    },
  })

  if (!dragTree) {
    throw new Error(`Drag tree ${dragTreeId} not found`)
  }

  const rawContents = await prisma.dragTreeNodeContent.findMany({
    where: {
      drag_tree_id: dragTreeId,
      status: DragTreeNodeContentStatus.ACTIVE,
      content_type: DragTreeNodeContentType.QUICK_RESEARCH,
    },
    select: {
      id: true,
      drag_tree_node_id: true,
      status: true,
      content_text: true,
    },
    orderBy: {
      updated_at: 'desc',
    },
    distinct: ['drag_tree_node_id'],
  })

  const treeStructure = parseTreeStructure(dragTree.tree_structure)

  const parents = new Map<string, string>()
  Object.entries(treeStructure.hierarchy || {}).forEach(
    ([parent, children]) => {
      if (Array.isArray(children)) {
        children.forEach(child => {
          if (typeof child === 'string') {
            parents.set(child, parent)
          }
        })
      }
    }
  )

  const relevantNodeIds = new Set<string>()

  if (treeStructure.root_id) {
    relevantNodeIds.add(treeStructure.root_id)
  }

  rawContents.forEach(item => {
    let current: string | undefined = item.drag_tree_node_id
    while (current) {
      if (relevantNodeIds.has(current)) break
      relevantNodeIds.add(current)
      current = parents.get(current)
    }
  })

  const nodes = relevantNodeIds.size
    ? await prisma.dragTreeNode.findMany({
        where: {
          id: { in: Array.from(relevantNodeIds) },
        },
        select: {
          id: true,
          label: true,
          node_type: true,
        },
      })
    : []

  const payloadSize =
    JSON.stringify(dragTree).length +
    JSON.stringify(nodes).length +
    JSON.stringify(rawContents).length

  console.log('📦 [getResearchPreviewData] payload size', {
    dragTreeId,
    kb: Number((payloadSize / 1024).toFixed(2)),
    nodeCount: nodes.length,
    contentCount: rawContents.length,
  })

  return {
    dragTreeId,
    title: dragTree.title,
    treeStructure: dragTree.tree_structure,
    nodes,
    contents: rawContents,
  }
}
