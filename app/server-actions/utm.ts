'use server'

import { cookies } from 'next/headers'
import prisma from '@/app/libs/prismadb'
import { auth } from '@/auth'
import { Prisma } from '@prisma/client'
import {
  UTM_FIRST_COOKIE,
  UTM_LAST_COOKIE,
  parseUtmCookie,
  sameUtmPayload,
} from '@/app/libs/utm/constants'

/**
 * Persist UTM parameters captured in cookie into User.metadata.
 * Behavior:
 * - On first successful auth with a cookie present:
 *   - If metadata.utm_first is missing, set both utm_first and utm_last from cookie
 *   - If metadata.utm_first exists but utm_last missing, set utm_last
 *   - Else, only update utm_last
 * - Clear the cookie after persisting
 * - If no cookie or not authenticated, no-op
 */

export async function persistUtmFromCookie(): Promise<{
  success: boolean
}> /** adds utm_first and utm_last into user.metadata if cookie is present */ {
  const session = await auth()
  const userId = session?.user?.id
  if (!userId) return { success: false }

  // Next.js 15: cookies() must be awaited in server actions/routes
  const cookieStore = await cookies()
  const last = parseUtmCookie(cookieStore.get(UTM_LAST_COOKIE)?.value)
  const first = parseUtmCookie(cookieStore.get(UTM_FIRST_COOKIE)?.value)
  if (!last && !first) return { success: false }

  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { metadata: true },
  })

  const existing = (user?.metadata as Prisma.JsonObject | null) ?? {}

  const nextMetadata: Prisma.JsonObject = { ...existing }
  let updated = false

  const firstToPersist = first ?? last
  const existingFirst = existing.utm_first as Prisma.JsonObject | undefined
  if (firstToPersist && !existingFirst) {
    nextMetadata.utm_first = firstToPersist as Prisma.JsonObject
    updated = true
  }
  // Always update utm_last when we have a cookie
  const resolvedLast = last ?? firstToPersist
  const existingLast = existing.utm_last as Prisma.JsonObject | undefined
  if (resolvedLast && !sameUtmPayload(existingLast, resolvedLast)) {
    nextMetadata.utm_last = resolvedLast as Prisma.JsonObject
    updated = true
  }

  if (!updated) {
    if (last) cookieStore.delete(UTM_LAST_COOKIE)
    return { success: true }
  }

  await prisma.user.update({
    where: { id: userId },
    data: { metadata: nextMetadata, updated_at: new Date() },
  })

  if (last) cookieStore.delete(UTM_LAST_COOKIE)
  return { success: true }
}
