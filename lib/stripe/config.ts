import Stripe from 'stripe'

export const stripe = new Stripe(
  process.env.STRIPE_SECRET_KEY_LIVE ?? process.env.STRIPE_SECRET_KEY ?? '',
  {
    // https://github.com/stripe/stripe-node#configuration
    // https://stripe.com/docs/api/versioning
    // Pin the API version to avoid accidental breaking changes
    // Keep this in sync with your Stripe dashboard version when upgrading
    // Use the latest supported type-safe version for this SDK
    apiVersion: '2024-04-10',
    // Register this as an official Stripe plugin.
    // https://stripe.com/docs/building-plugins#setappinfo
    appInfo: {
      name: 'Thought Atlas',
      version: '0.0.0',
      url: 'https://www.thoughtatlas.ai',
    },
  }
)
