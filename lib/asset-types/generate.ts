import React from 'react'
import { FiFileText } from 'react-icons/fi'
import type { AssetTypeDefinition } from './registry'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'

// Dynamic import to avoid circular dependencies
const GenerateTabContent = React.lazy(
  () =>
    import(
      '@/app/(conv)/dragTree/[dragTreeId]/components/tabs/GenerateTabContent'
    )
)

/**
 * Generate Asset Type Definition
 * Handles AI content generation assets
 */
export const generateAssetType: AssetTypeDefinition = {
  id: 'generate',
  displayName: 'Generate',
  icon: FiFileText,
  tabComponent: GenerateTabContent,

  getAssetTypeColor: () => 'text-blue-600',

  createTabTitle: (asset: any) => {
    const title = asset.title || 'AI Generation'
    return `Generate - ${title}`
  },

  isAssetType: (type: string) => type === 'generate',

  createAssetTabData: (asset: any) => ({
    type: 'generate',
    model: asset.model,
    prompt: asset.prompt,
    contextIds: asset.contextIds || [],
    settings: {},
    // Add asset data for immediate display
    assetContent: asset.content,
    assetId: asset.id,
    // For generate assets mark as completed to skip streaming UI
    generationPhase: 'completed',
  }),
}

/**
 * Helper function to check if a tab is a generate tab
 */
export const isGenerateTab = (tab: Tab): boolean => {
  return tab.type === 'generate' || tab.aiPaneData?.type === 'generate'
}

/**
 * Helper function to create a generate tab ID
 */
export const createGenerateTabId = (assetId: string): string => {
  return `asset-generate-${assetId}`
}

/**
 * Helper function to extract generate-specific data from a tab
 */
export const getGenerateTabData = (tab: Tab) => {
  if (!isGenerateTab(tab)) return null

  return {
    assetId: tab.aiPaneData?.assetId,
    content: tab.aiPaneData?.assetContent,
    generationPhase: tab.aiPaneData?.generationPhase,
    model: tab.aiPaneData?.model,
    prompt: tab.aiPaneData?.prompt,
    contextIds: tab.aiPaneData?.contextIds || [],
  }
}
