import React from 'react'
import { FiMessageSquare } from 'react-icons/fi'
import type { AssetTypeDefinition } from './registry'
import type { Tab } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useWorkspaceLayoutStore'

// Dynamic import of consolidated chat component (wrapper removed after consolidation)
const ChatTabContent = React.lazy(
  () =>
    import('@/app/(conv)/dragTree/[dragTreeId]/components/tabs/ChatTabContent')
)

/**
 * Chat Asset Type Definition
 * Handles AI chat conversation assets
 */
export const chatAssetType: AssetTypeDefinition = {
  id: 'chat',
  displayName: 'Chat',
  icon: FiMessageSquare,
  tabComponent: ChatTabContent,

  getAssetTypeColor: () => 'text-green-600',

  createTabTitle: (asset: any) => {
    // Remove version prefix for simplicity per user request
    const title = asset.title || 'Untitled'
    return title
  },

  isAssetType: (type: string) => type === 'chat',

  createAssetTabData: (asset: any) => ({
    type: 'chat',
    model: asset.model,
    prompt: asset.prompt,
    contextIds: asset.contextIds || [],
    settings: {},
    // If this is a chat asset, wire up the existing conversation ID so the
    // chat tab loads historical messages instead of initializing a new thread.
    conversationId: asset.id,
    // Add asset data for immediate display
    assetContent: asset.content,
    assetMessages: asset.messages,
    assetId: asset.id,
  }),
}

/**
 * Helper function to check if a tab is a chat tab
 */
export const isChatTab = (tab: Tab): boolean => {
  return tab.type === 'chat' || tab.aiPaneData?.type === 'chat'
}

/**
 * Helper function to create a chat tab ID
 */
export const createChatTabId = (assetId: string): string => {
  return `asset-chat-${assetId}`
}

/**
 * Helper function to extract chat-specific data from a tab
 */
export const getChatTabData = (tab: Tab) => {
  if (!isChatTab(tab)) return null

  return {
    assetId: tab.aiPaneData?.assetId,
    conversationId: tab.aiPaneData?.conversationId,
    messages: tab.aiPaneData?.assetMessages,
    model: tab.aiPaneData?.model,
    prompt: tab.aiPaneData?.prompt,
    contextIds: tab.aiPaneData?.contextIds || [],
  }
}

/**
 * Helper function to generate chat asset title from first user message
 */
export const generateChatAssetTitle = (firstUserMessage: string): string => {
  // Take first 50 characters and add ellipsis if longer
  const maxLength = 50
  const trimmed = firstUserMessage.trim()

  if (trimmed.length <= maxLength) {
    return trimmed
  }

  // Find the last space before the cutoff to avoid cutting words
  const cutoff = trimmed.lastIndexOf(' ', maxLength)
  const finalCutoff = cutoff > 20 ? cutoff : maxLength

  return trimmed.substring(0, finalCutoff) + '...'
}
