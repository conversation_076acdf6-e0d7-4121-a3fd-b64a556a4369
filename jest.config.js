const nextJest = require('next/jest')

/** @type {import('jest').Config} */
const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Toggle coverage to avoid OOM in local runs. Enable with JEST_COVERAGE=1 or in CI.
const enableCoverage = Boolean(process.env.JEST_COVERAGE || process.env.CI)

// Add any custom config to be passed to Jest
const config = {
  coverageProvider: 'v8',
  testEnvironment: 'jsdom',
  moduleNameMapper: {
    // Handle module aliases
    '^@/(.*)$': '<rootDir>/$1',
    '^next-auth/react$': '<rootDir>/__mocks__/next-auth/react.ts',
    '^react-syntax-highlighter$':
      '<rootDir>/__mocks__/react-syntax-highlighter.tsx',
    '^react-syntax-highlighter/dist/esm/styles/prism$':
      '<rootDir>/__mocks__/react-syntax-highlighter-prism-styles.ts',
    '^react-markdown$': '<rootDir>/__mocks__/react-markdown.tsx',
    '^harden-react-markdown$': '<rootDir>/__mocks__/harden-react-markdown.tsx',
    '^remark-gfm$': '<rootDir>/__mocks__/remark-gfm.ts',
    '^remark-math$': '<rootDir>/__mocks__/remark-math.ts',
    '^rehype-katex$': '<rootDir>/__mocks__/rehype-katex.ts',
  },
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/.next-dev/',
    '<rootDir>/node_modules/',
    '<rootDir>/e2e/',
    '<rootDir>/__tests__/__mocks__/',
  ],
  collectCoverage: enableCoverage,
  collectCoverageFrom: enableCoverage
    ? [
        'app/**/*.{js,jsx,ts,tsx}',
        'components/**/*.{js,jsx,ts,tsx}',
        '!**/*.d.ts',
        '!**/node_modules/**',
        '!**/.next/**',
        '!**/.next-dev/**',
      ]
    : undefined,
  coverageReporters: enableCoverage
    ? ['text', 'lcov', 'cobertura', 'html']
    : undefined,
  coverageDirectory: enableCoverage ? 'coverage' : undefined,
  transformIgnorePatterns: [
    'node_modules/(?!(zeed-dom|@tiptap|prosemirror.*|lowlight|highlight.js|use-stick-to-bottom|react-syntax-highlighter)/)',
  ],
  // Use default reporters to preserve correct test counts in summary
  // Custom quiet reporter caused incorrect test totals in summary
  reporters: [
    ['default', { silent: false, verbose: false }],
    [
      'jest-junit',
      { outputDirectory: 'test-results', outputName: 'junit.xml' },
    ],
  ],
  verbose: false,
  silent: false,

  // Performance optimizations
  maxWorkers: '25%', // lower concurrency to avoid OOM during large suites
  testTimeout: 10000, // 10 second timeout for individual tests

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(config)
