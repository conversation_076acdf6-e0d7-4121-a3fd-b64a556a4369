# UTM Capture Flow

## Overview

- Goal: retain both first-touch and last-touch attribution for authenticated users without relying on client-only JavaScript.
- Entry points: every navigation request that reaches middleware; authentication success through NextAuth events.
- Storage targets: HTTP-only cookies (`__utm_first`, `__utm_last`) + `user.metadata.utm_first` / `user.metadata.utm_last` in Postgres.

## Runtime Flow

1. **Edge middleware (`middleware.ts`)**
   - Runs on all non-static, non-API routes.
   - Allowlist of UTM keys lives in `app/libs/utm/constants.ts`.
   - If search params include tracked keys, middleware creates a sanitized payload with:
     - Allowed marketing params (`utm_source`, `gclid`, etc.).
     - `landing_url`: path + query for the request.
     - `captured_at`: ISO timestamp in UTC.
   - `__utm_last` is refreshed on every qualifying request; `__utm_first` is written only when absent.
   - Cookies are HTTP-only, `SameSite=Lax`, `Secure` when the request protocol is HTTPS.
2. **NextAuth sign-in event (`auth.ts`)**
   - On successful login, reads the cookies via `await cookies()`.
   - Sanitizes values with the same helper to prevent metadata injection.
   - Persists `utm_first` only if missing; always updates `utm_last` when a new payload appears.
   - Clears `__utm_last` after processing so subsequent requests only write when new data arrives.
3. **Fallback server action (`app/server-actions/utm.ts`)**
   - Still available for manual invocation; mirrors the NextAuth logic using the shared helpers.

## Data Model Notes

- `utm_first` / `utm_last` live inside `User.metadata` as JSON blobs.
- Payload schema: `Partial<Record<UtmAllowedKey, string>> & { landing_url?: string; captured_at?: string }`.
- Value lengths are capped (200 chars per param, 512 chars for `landing_url`).
- Timestamps store at most 24 chars to keep ISO 8601 precision while respecting cookie size.

## Testing / Verification

- `npm run test` should pass after changes; add regression tests where feasible (e.g., unit test for `sanitizeUtmRecord`).
- Manual QA checklist:
  1. Hit landing page with `?utm_source=...` anonymously; confirm cookies appear via browser devtools.
  2. Complete sign-in flow; verify `users.metadata` now contains `utm_first` and `utm_last`.
  3. Log out, revisit with new UTMs; ensure `utm_last` updates while `utm_first` stays immutable.

## Extending The Flow

- To expose UTMs inside client components, surface them via the session callback after persisting.
- Update the allowlist in `app/libs/utm/constants.ts` whenever marketing adds new parameters.
- If privacy rules change, adjust the sanitizer to redact or hash sensitive values before persistence.
