import { auth } from '@/auth-edge'
import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { LOCALES } from '@/app/i18n/config'
import {
  UTM_FIRST_COOKIE,
  UTM_FIRST_MAX_AGE_SECONDS,
  UTM_LAST_COOKIE,
  UTM_LAST_MAX_AGE_SECONDS,
  buildUtmCookiePayload,
  encodeUtmCookie,
  isSecureRequest,
  parseUtmCookie,
  sameUtmPayload,
} from '@/app/libs/utm/constants'

// Protected route prefixes (require auth)
const PROTECTED_PREFIXES = [
  '/conversations',
  '/users',
  '/feedback',
  '/subscription',
  '/dragTree',
  '/screening',
]

function stripLocalePrefix(pathname: string): {
  locale: string | null
  path: string
} {
  for (const locale of LOCALES) {
    const prefix = `/${locale}`
    if (pathname === prefix) {
      return { locale, path: '/' }
    }
    if (pathname.startsWith(prefix + '/')) {
      return { locale, path: pathname.slice(prefix.length) || '/' }
    }
  }
  return { locale: null, path: pathname }
}

export default auth(req => {
  const url = req.nextUrl.clone()

  // Handle locale-prefixed URLs by rewriting to the underlying route
  const { locale, path } = stripLocalePrefix(url.pathname)
  if (locale) {
    url.pathname = path
  }

  // Enforce auth only for protected prefixes
  const requiresAuth = PROTECTED_PREFIXES.some(prefix =>
    url.pathname.startsWith(prefix)
  )
  if (requiresAuth && !req.auth) {
    const signInUrl = new URL('/', req.nextUrl.origin)
    return withUtmCookies(req, NextResponse.redirect(signInUrl))
  }

  // If we stripped a locale, rewrite to the underlying path so existing routes work
  if (locale) {
    return withUtmCookies(req, NextResponse.rewrite(url))
  }

  // Otherwise continue
  return withUtmCookies(req, NextResponse.next())
})

const withUtmCookies = (
  request: NextRequest,
  response: NextResponse
): NextResponse => {
  const payload = buildUtmCookiePayload(request)
  if (!payload) return response

  const isSecure = isSecureRequest(request)
  const encoded = encodeUtmCookie(payload)

  const existingLast = parseUtmCookie(
    request.cookies.get(UTM_LAST_COOKIE)?.value
  )
  if (!sameUtmPayload(existingLast, payload)) {
    response.cookies.set({
      name: UTM_LAST_COOKIE,
      value: encoded,
      maxAge: UTM_LAST_MAX_AGE_SECONDS,
      httpOnly: true,
      sameSite: 'lax',
      path: '/',
      secure: isSecure,
    })
  }

  const existingFirst = parseUtmCookie(
    request.cookies.get(UTM_FIRST_COOKIE)?.value
  )
  if (!existingFirst) {
    response.cookies.set({
      name: UTM_FIRST_COOKIE,
      value: encoded,
      maxAge: UTM_FIRST_MAX_AGE_SECONDS,
      httpOnly: true,
      sameSite: 'lax',
      path: '/',
      secure: isSecure,
    })
  }

  return response
}

export const config = {
  matcher: [
    // Run on locale-prefixed paths so we can rewrite them
    '/(zh-Hans|zh-Hant|ja|es|en)/:path*',
    // And on protected base paths for auth enforcement
    '/conversations/:path*',
    '/users/:path*',
    '/feedback/:path*',
    '/subscription/:path*',
    '/dragTree/:path*',
    '/screening/:path*',
  ],
}
